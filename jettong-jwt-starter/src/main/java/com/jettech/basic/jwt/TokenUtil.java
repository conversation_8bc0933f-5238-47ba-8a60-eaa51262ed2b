package com.jettech.basic.jwt;


import cn.hutool.core.convert.Convert;
import com.jettech.basic.jwt.model.AuthInfo;
import com.jettech.basic.jwt.model.JwtUserInfo;
import com.jettech.basic.jwt.model.Token;
import com.jettech.basic.jwt.utils.JwtUtil;
import io.jsonwebtoken.Claims;
import lombok.AllArgsConstructor;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.basic.context.ContextConstants.*;

/**
 * 认证工具类
 *
 * <AUTHOR>
 * @date 2020年03月31日19:03:47
 */
@AllArgsConstructor
public class TokenUtil
{
    /**
     * 认证服务端使用，如 authority-server
     * 生成和 解析token
     */
    private final JwtProperties jwtProperties;


    /**
     * 创建认证token
     *
     * @param userInfo 用户信息
     * @return token
     */
    public AuthInfo createAuthInfo(JwtUserInfo userInfo, Long expireMillis)
    {
        if (expireMillis == null || expireMillis <= 0)
        {
            expireMillis = jwtProperties.getExpire();
        }

        //设置jwt参数
        Map<String, String> param = new HashMap<>(16);
        param.put(JWT_KEY_TOKEN_TYPE, BEARER_HEADER_KEY);
        param.put(JWT_KEY_USER_ID, Convert.toStr(userInfo.getUserId(), "0"));
        param.put(JWT_KEY_USER_NAME, userInfo.getUserName());
        param.put(JWT_KEY_USER_ACCOUNT, userInfo.getAccount());
        param.put(JWT_KEY_ORG_ID, Convert.toStr(userInfo.getOrgId()));

        Token token = JwtUtil.createJwt(param, expireMillis);

        AuthInfo authInfo = new AuthInfo();
        authInfo.setUserAccount(userInfo.getAccount());
        authInfo.setUserName(userInfo.getUserName());
        authInfo.setUserId(userInfo.getUserId());
        authInfo.setOrgId(userInfo.getOrgId());
        authInfo.setTokenType(BEARER_HEADER_KEY);
        authInfo.setToken(token.getToken());
        authInfo.setExpire(token.getExpire());
        authInfo.setExpiration(token.getExpiration());
        authInfo.setRefreshToken(createRefreshToken(userInfo).getToken());
        authInfo.setExpireMillis(expireMillis);
        return authInfo;
    }

    /**
     * 创建refreshToken
     *
     * @param userInfo 用户信息
     * @return refreshToken
     */
    private Token createRefreshToken(JwtUserInfo userInfo)
    {
        Map<String, String> param = new HashMap<>(16);
        param.put(JWT_KEY_TOKEN_TYPE, REFRESH_TOKEN_KEY);
        param.put(JWT_KEY_USER_ID, Convert.toStr(userInfo.getUserId(), "0"));
        return JwtUtil.createJwt(param, jwtProperties.getRefreshExpire());
    }

    /**
     * 解析token
     *
     * @param token token
     * @return 用户信息
     */
    public AuthInfo getAuthInfo(String token)
    {
        Claims claims = JwtUtil.getClaims(token, jwtProperties.getAllowedClockSkewSeconds());
        String tokenType = Convert.toStr(claims.get(JWT_KEY_TOKEN_TYPE));
        Long userId = Convert.toLong(claims.get(JWT_KEY_USER_ID));
        String userName = Convert.toStr(claims.get(JWT_KEY_USER_NAME));
        String userAccount = Convert.toStr(claims.get(JWT_KEY_USER_ACCOUNT));
        String avatarPath = Convert.toStr(claims.get(JWT_KEY_USER_AVATAR_PATH));
        Boolean avatarType = Convert.toBool(claims.get(JWT_KEY_USER_AVATAR_TYPE));
        Long orgId = Convert.toLong(claims.get(JWT_KEY_ORG_ID));
        Date expiration = claims.getExpiration();
        return new AuthInfo().setToken(token)
                .setExpire(expiration != null ? expiration.getTime() : 0L)
                .setTokenType(tokenType).setUserId(userId)
                .setUserAccount(userAccount).setUserName(userName)
                .setAvatarType(avatarType)
                .setAvatarPath(avatarPath)
                .setOrgId(orgId);
    }

    /**
     * 解析刷新token
     *
     * @param token 待解析的token
     * @return 认证信息
     */
    public AuthInfo parseRefreshToken(String token)
    {
        Claims claims = JwtUtil.parseJwt(token, jwtProperties.getAllowedClockSkewSeconds());
        String tokenType = Convert.toStr(claims.get(JWT_KEY_TOKEN_TYPE));
        Long orgId = Convert.toLong(claims.get(JWT_KEY_ORG_ID));
        Long userId = Convert.toLong(claims.get(JWT_KEY_USER_ID));
        String userName = Convert.toStr(claims.get(JWT_KEY_USER_NAME));
        String userAccount = Convert.toStr(claims.get(JWT_KEY_USER_ACCOUNT));
        Boolean avatarType = Convert.toBool(claims.get(JWT_KEY_USER_AVATAR_TYPE));
        String avatarPath = Convert.toStr(claims.get(JWT_KEY_USER_AVATAR_PATH));
        Date expiration = claims.getExpiration();
        return new AuthInfo().setToken(token)
                .setExpire(expiration != null ? expiration.getTime() : 0L)
                .setTokenType(tokenType).setUserId(userId).setUserAccount(userAccount).setUserName(userName)
                .setAvatarType(avatarType)
                .setAvatarPath(avatarPath)
                .setOrgId(orgId);
    }
}
