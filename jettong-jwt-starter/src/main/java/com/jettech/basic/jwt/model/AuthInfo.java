package com.jettech.basic.jwt.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * AuthInfo
 *
 * <AUTHOR>
 * @date 2020年03月31日21:43:31
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "认证信息")
public class AuthInfo
{
    @ApiModelProperty(value = "令牌")
    private String token;
    @ApiModelProperty(value = "令牌类型")
    private String tokenType;
    @ApiModelProperty(value = "刷新令牌")
    private String refreshToken;
    @ApiModelProperty(value = "用户id")
    private Long userId;
    @ApiModelProperty(value = "用户名")
    private String userName;
    @ApiModelProperty(value = "账号名")
    private String userAccount;
    @ApiModelProperty(value = "头像地址")
    private Boolean avatarType;
    @ApiModelProperty(value = "头像地址")
    private String avatarPath;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "组织机构ID")
    private Long orgId;
    @ApiModelProperty(value = "过期时间（秒）")
    private long expire;
    @ApiModelProperty(value = "到期时间")
    private LocalDateTime expiration;
    @ApiModelProperty(value = "有效期")
    private Long expireMillis;
}
