package com.jettech.basic.boot.handler;

import com.jettech.basic.annotation.base.IgnoreResponseBodyAdvice;
import com.jettech.basic.base.R;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 全局响应体包装
 *
 * <AUTHOR>
 * @version 1.0
 * @description 全局响应体包装
 * @projectName cloudbooster
 * @package com.jettech.basic.boot.handler
 * @className AbstractGlobalResponseBodyAdvice
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class AbstractGlobalResponseBodyAdvice implements ResponseBodyAdvice
{
    @Override
    public boolean supports(MethodParameter methodParameter, Class aClass)
    {
        // 类上如果被 IgnoreResponseBodyAdvice 标识就不拦截
        if (methodParameter.getDeclaringClass().isAnnotationPresent(IgnoreResponseBodyAdvice.class))
        {
            return false;
        }

        // 方法上被标注也不拦截
        if (methodParameter.getMethod().isAnnotationPresent(IgnoreResponseBodyAdvice.class))
        {
            return false;
        }
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType, Class aClass,
            ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse)
    {
        if (o == null)
        {
            return null;
        }
        if (o instanceof R)
        {
            return o;
        }

        return R.success(o);
    }
}
