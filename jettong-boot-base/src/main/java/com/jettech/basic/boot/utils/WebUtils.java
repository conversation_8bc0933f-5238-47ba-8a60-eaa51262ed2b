package com.jettech.basic.boot.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.jettech.basic.utils.StrPool;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.server.ServerWebExchange;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;

/**
 * Web工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description Web工具类
 * @projectName cloudbooster
 * @package com.jettech.basic.boot.utils
 * @className WebUtils
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public final class WebUtils
{
    private WebUtils()
    {
    }

    /**
     * 获取request
     *
     * @return HttpServletRequest HttpServletRequest
     * <AUTHOR>
     * @date 2021/9/13 11:14
     * @update zxy 2021/9/13 11:14
     * @since 1.0
     */
    public static HttpServletRequest request()
    {
        return ((ServletRequestAttributes) Objects.requireNonNull(
                RequestContextHolder.getRequestAttributes())).getRequest();
    }

    /**
     * 获取header中的参数值
     *
     * @param request request
     * @param name 参数name
     * @return String 参数值
     * <AUTHOR>
     * @date 2021/9/13 11:13
     * @update zxy 2021/9/13 11:13
     * @since 1.0
     */
    public static String getHeader(HttpServletRequest request, String name)
    {
        String value = request.getHeader(name);
        if (StrUtil.isEmpty(value))
        {
            return StrPool.EMPTY;
        }
        return URLUtil.decode(value);
    }


    /**
     * 获取IP地址
     *
     * @param exchange ServerWebExchange
     * @return String IP地址
     * <AUTHOR>
     * @date 2021/9/13 11:13
     * @update zxy 2021/9/13 11:13
     * @since 1.0
     */
    public static String getRemoteAddress(ServerWebExchange exchange)
    {
        ServerHttpRequest request = exchange.getRequest();
        Map<String, String> headers = request.getHeaders().toSingleValueMap();
        String unknown = "unknown";
        String ip = headers.get("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip))
        {
            ip = headers.get("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip))
        {
            ip = headers.get("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip))
        {
            ip = headers.get("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip))
        {
            ip = headers.get("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip))
        {
            ip = headers.get("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip))
        {
            ip = Objects.requireNonNull(request.getRemoteAddress()).getAddress().getHostAddress();
        }
        //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ip != null && ip.length() > 0)
        {
            String[] ips = ip.split(",");
            if (ips.length > 0)
            {
                ip = ips[0];
            }
        }
        return ip;
    }
}
