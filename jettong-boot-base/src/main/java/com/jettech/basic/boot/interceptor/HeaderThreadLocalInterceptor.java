package com.jettech.basic.boot.interceptor;

import cn.hutool.core.util.StrUtil;
import com.jettech.basic.context.ContextConstants;
import com.jettech.basic.context.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.jettech.basic.boot.utils.WebUtils.getHeader;

/**
 * 拦截器：
 * 将请求头数据，封装到BaseContextHandler(ThreadLocal)
 * <p>
 * 该拦截器要优先于系统中其他的业务拦截器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 将请求头数据，封装到BaseContextHandler(ThreadLocal)
 * @projectName cloudbooster
 * @package com.jettech.basic.boot.interceptor
 * @className HeaderThreadLocalInterceptor
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
public class HeaderThreadLocalInterceptor implements AsyncHandlerInterceptor
{

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception
    {
        if (!(handler instanceof HandlerMethod))
        {
            return true;
        }

        if (!ContextUtil.getBoot())
        {
            ContextUtil.setUserId(getHeader(request, ContextConstants.JWT_KEY_USER_ID));
            ContextUtil.setUserAccount(getHeader(request, ContextConstants.JWT_KEY_USER_ACCOUNT));
            ContextUtil.setUserName(getHeader(request, ContextConstants.JWT_KEY_USER_NAME));
            ContextUtil.setOrgId(getHeader(request, ContextConstants.JWT_KEY_ORG_ID));
            ContextUtil.setTenant(getHeader(request, ContextConstants.JWT_KEY_TENANT));
            ContextUtil.setSubTenant(getHeader(request, ContextConstants.JWT_KEY_SUB_TENANT));

            String traceId = request.getHeader(ContextConstants.TRACE_ID_HEADER);
            MDC.put(ContextConstants.LOG_TRACE_ID, StrUtil.isEmpty(traceId) ? StrUtil.EMPTY : traceId);
            MDC.put(ContextConstants.JWT_KEY_TENANT, getHeader(request, ContextConstants.JWT_KEY_TENANT));
            MDC.put(ContextConstants.JWT_KEY_SUB_TENANT, getHeader(request, ContextConstants.JWT_KEY_SUB_TENANT));
            MDC.put(ContextConstants.JWT_KEY_USER_ID, getHeader(request, ContextConstants.JWT_KEY_USER_ID));
        }
        // cloud
        ContextUtil.setGrayVersion(getHeader(request, ContextConstants.GRAY_VERSION));
        return true;
    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception
    {
        ContextUtil.remove();
    }
}
