//package com.jettech.basic.boot.undertow;
//
//import io.undertow.connector.ByteBufferPool;
//import io.undertow.server.DefaultByteBufferPool;
//import io.undertow.websockets.jsr.WebSocketDeploymentInfo;
//import org.springframework.boot.web.embedded.undertow.UndertowDeploymentInfoCustomizer;
//import org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory;
//import org.springframework.boot.web.server.WebServerFactoryCustomizer;
//import org.xnio.OptionMap;
//import org.xnio.Options;
//import org.xnio.Xnio;
//import org.xnio.XnioWorker;
//
//import java.io.IOException;
//
///**
// * 设置Undertow服务器 XnioWorker Buffers
// * <p>
// * 解决启动时警告：[io.undertow.websockets.jsr.handleDeployment:68] --> UT026010: Buffer pool was not set on
// * WebSocketDeploymentInfo, the default pool will be used
// * <p>
// * https://docs.spring.io/spring-boot/docs/current/reference/htmlsingle/#boot-features-programmatic-embedded
// * -container-customization
// *
// * <AUTHOR>
// * @version 1.0
// * @description 设置Undertow服务器 XnioWorker Buffers
// * @projectName cloudbooster
// * @package com.jettech.basic.boot.undertow
// * @className UndertowServerFactoryCustomizer
// * @date 2021/9/9 11:54
// * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
// * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
// */
//public class UndertowServerFactoryCustomizer implements WebServerFactoryCustomizer<UndertowServletWebServerFactory>
//{
//
//    @Override
//    public void customize(UndertowServletWebServerFactory factory)
//    {
//        UndertowDeploymentInfoCustomizer undertowDeploymentInfoCustomizer = deploymentInfo ->
//        {
//            WebSocketDeploymentInfo info = (WebSocketDeploymentInfo) deploymentInfo.getServletContextAttributes()
//                    .get(WebSocketDeploymentInfo.ATTRIBUTE_NAME);
//            XnioWorker worker = getXnioWorker();
//            ByteBufferPool buffers =
//                    new DefaultByteBufferPool(Boolean.getBoolean("io.undertow.websockets.direct-buffers"), 1024, 100,
//                            12);
//            info.setWorker(worker);
//            info.setBuffers(buffers);
//        };
//        factory.addDeploymentInfoCustomizers(undertowDeploymentInfoCustomizer);
//    }
//
//    private XnioWorker getXnioWorker()
//    {
//        XnioWorker worker = null;
//        try
//        {
//            worker = Xnio.getInstance().createWorker(OptionMap.create(Options.THREAD_DAEMON, true));
//        }
//        catch (IOException ignored)
//        {
//        }
//        return worker;
//    }
//
//}
