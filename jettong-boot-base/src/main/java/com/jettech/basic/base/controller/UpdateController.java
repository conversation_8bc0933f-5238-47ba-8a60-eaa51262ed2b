package com.jettech.basic.base.controller;

import cn.hutool.core.bean.BeanUtil;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 修改Controller
 *
 * @param <Entity> 实体
 * @param <UpdateDTO> 修改参数
 * <AUTHOR>
 * @version 1.0
 * @description 修改Controller
 * @projectName cloudbooster
 * @package com.jettech.basic.base.controller
 * @className UpdateController
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface UpdateController<Entity, UpdateDTO> extends BaseController<Entity>
{

    /**
     * 修改
     *
     * @param updateDTO 修改DTO
     * @return R<Entity> 修改后的实体数据
     * <AUTHOR>
     * @date 2021/9/13 10:27
     * @update zxy 2021/9/13 10:27
     * @since 1.0
     */
    @ApiOperation(value = "修改", notes = "修改UpdateDTO中不为空的字段")
    @PutMapping
    @SysLog(value = "'修改:' + #updateDTO?.id", request = false, optType = OptLogTypeEnum.EDIT)
    @PreAuth("hasAnyPermission('{}edit')")
    default R<Entity> update(@RequestBody @Validated(SuperEntity.Update.class) UpdateDTO updateDTO)
    {
        R<Entity> result = handlerUpdate(updateDTO);
        if (result.getDefExec())
        {
            Entity model = BeanUtil.toBean(updateDTO, getEntityClass());
            getBaseService().updateById(model);
            result.setData(model);
        }
        return result;
    }

    /**
     * 修改所有字段
     *
     * @param entity 要修改的实体对象
     * @return R<Entity> 修改后的实体数据
     * <AUTHOR>
     * @date 2021/9/13 10:27
     * @update zxy 2021/9/13 10:27
     * @since 1.0
     */
    @ApiOperation(value = "修改所有字段", notes = "修改所有字段，没有传递的字段会被置空")
    @PutMapping("/all")
    @SysLog(value = "'修改所有字段:' + #entity?.id", request = false, optType = OptLogTypeEnum.EDIT)
    @PreAuth("hasAnyPermission('{}edit')")
    default R<Entity> updateAll(@RequestBody @Validated(SuperEntity.Update.class) Entity entity)
    {
        getBaseService().updateAllById(entity);
        return R.success(entity);
    }

    /**
     * 自定义更新
     *
     * @param model 修改DTO
     * @return R<Entity> 返回SUCCESS_RESPONSE, 调用默认更新, 返回其他不调用默认更新
     * <AUTHOR>
     * @date 2021/9/13 10:28
     * @update zxy 2021/9/13 10:28
     * @since 1.0
     */
    default R<Entity> handlerUpdate(UpdateDTO model)
    {
        return R.successDef();
    }
}
