package com.jettech.basic.base.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.jettech.basic.base.R;
import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.basic.exception.BizException;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.ParameterizedType;

import static com.jettech.basic.exception.code.ExceptionCode.SERVICE_MAPPER_ERROR;

/**
 * 不含缓存的Service实现
 * <p>
 * 1，removeById：重写 ServiceImpl 类的方法，删除db
 * 2，removeByIds：重写 ServiceImpl 类的方法，删除db
 * 3，updateAllById： 新增的方法： 修改数据（所有字段）
 * 4，updateById：重写 ServiceImpl 类的方法，修改db后
 *
 * @param <T> 实体
 * <AUTHOR>
 * @version 1.0
 * @description 不含缓存的Service实现
 * @projectName cloudbooster
 * @package com.jettech.basic.base.service
 * @className SuperServiceImpl
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class SuperServiceImpl<M extends SuperMapper<T>, T> extends ServiceImpl<M, T> implements SuperService<T>
{

    private Class<T> entityClass = null;

    /**
     * 获取SuperMapper构造器
     *
     * @return SuperMapper SuperMapper构造器
     * <AUTHOR>
     * @date 2021/9/13 10:53
     * @update zxy 2021/9/13 10:53
     * @since 1.0
     */
    public SuperMapper getSuperMapper()
    {
        if (baseMapper instanceof SuperMapper)
        {
            return baseMapper;
        }
        throw BizException.wrap(SERVICE_MAPPER_ERROR);
    }

    @Override
    public Class<T> getEntityClass()
    {
        if (entityClass == null)
        {
            this.entityClass =
                    (Class) ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments()[1];
        }
        return this.entityClass;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(T model)
    {
        R<Boolean> result = handlerSave(model);
        if (result.getDefExec())
        {
            return super.save(model);
        }
        return result.getData();
    }

    /**
     * 处理新增相关操作
     *
     * @param model 实体
     * @return R<Boolean> 是否成功
     * <AUTHOR>
     * @date 2021/9/13 10:54
     * @update zxy 2021/9/13 10:54
     * @since 1.0
     */
    protected R<Boolean> handlerSave(T model)
    {
        return R.successDef();
    }

    /**
     * 处理修改相关操作
     *
     * @param model 实体
     * @return R<Boolean> 是否成功
     * <AUTHOR>
     * @date 2021/9/13 10:54
     * @update zxy 2021/9/13 10:54
     * @since 1.0
     */
    protected R<Boolean> handlerUpdateAllById(T model)
    {
        return R.successDef();
    }

    /**
     * 处理修改相关操作
     *
     * @param model 实体
     * @return R<Boolean> 是否成功
     * <AUTHOR>
     * @date 2021/9/13 10:56
     * @update zxy 2021/9/13 10:56
     * @since 1.0
     */
    protected R<Boolean> handlerUpdateById(T model)
    {
        return R.successDef();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAllById(T model)
    {
        R<Boolean> result = handlerUpdateAllById(model);
        if (result.getDefExec())
        {
            return SqlHelper.retBool(getSuperMapper().updateAllById(model));
        }
        return result.getData();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(T model)
    {
        R<Boolean> result = handlerUpdateById(model);
        if (result.getDefExec())
        {
            return super.updateById(model);
        }
        return result.getData();
    }


}
