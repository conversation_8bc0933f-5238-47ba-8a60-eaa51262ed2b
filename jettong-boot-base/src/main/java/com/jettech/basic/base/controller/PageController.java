package com.jettech.basic.base.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.QueryWrap;

/**
 * 分页Controller接口
 *
 * @param <Entity> 实体
 * @param <PageQuery> 分页参数
 * <AUTHOR>
 * @version 1.0
 * @description 分页Controller接口
 * @projectName cloudbooster
 * @package com.jettech.basic.base.controller
 * @className PageController
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface PageController<Entity, PageQuery> extends BaseController<Entity>
{
    /**
     * 处理查询参数
     *
     * @param params 前端传递的参数
     * <AUTHOR>
     * @date 2021/9/10 13:41
     * @update zxy 2021/9/10 13:41
     * @since 1.0
     */
    default void handlerQueryParams(PageParams<PageQuery> params)
    {
    }

    /**
     * 执行分页查询
     * <p>
     * 子类可以覆盖后重写查询逻辑
     *
     * @param params 分页参数
     * @return IPage<Entity> 分页信息
     * <AUTHOR>
     * @date 2021/9/10 13:43
     * @update zxy 2021/9/10 13:43
     * @since 1.0
     */
    default IPage<Entity> query(PageParams<PageQuery> params)
    {
        // 处理查询参数，如：覆盖前端传递的 current、size、sort 等参数 以及 model 中的参数 【提供给之类重写】【无默认实现】
        handlerQueryParams(params);

        // 构建分页参数(current、size)和排序字段等
        IPage<Entity> page = params.buildPage(getEntityClass());
        Entity model = BeanUtil.toBean(params.getModel(), getEntityClass());

        // 根据前端传递的参数，构建查询条件【提供给之类重写】【有默认实现】
        QueryWrap<Entity> wrapper = handlerWrapper(model, params);

        // 执行单表分页查询
        getBaseService().page(page, wrapper);

        // 处理查询后的分页结果， 如：调用EchoService回显字典、关联表数据等 【提供给之类重写】【无默认实现】
        handlerResult(page);
        return page;
    }

    /**
     * 处理对象中的非空参数和扩展字段中的区间参数，可以覆盖后处理组装查询条件
     *
     * @param model 实体类
     * @param params 分页参数
     * @return QueryWrap<Entity> 查询构造器
     * <AUTHOR>
     * @date 2021/9/10 13:44
     * @update zxy 2021/9/10 13:44
     * @since 1.0
     */
    default QueryWrap<Entity> handlerWrapper(Entity model, PageParams<PageQuery> params)
    {
        return Wraps.q(model, params.getExtra(), getEntityClass());
    }

    /**
     * 处理查询后的数据
     * <p>
     * 如：执行@Echo回显
     *
     * @param page 分页对象
     * <AUTHOR>
     * @date 2021/9/10 13:45
     * @update zxy 2021/9/10 13:45
     * @since 1.0
     */
    default void handlerResult(IPage<Entity> page)
    {
    }
}
