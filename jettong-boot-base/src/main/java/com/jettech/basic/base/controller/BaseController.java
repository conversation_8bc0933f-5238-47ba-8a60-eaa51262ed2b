package com.jettech.basic.base.controller;

import com.jettech.basic.base.R;
import com.jettech.basic.base.service.SuperService;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.exception.code.BaseExceptionCode;

/**
 * 基础Controller接口
 *
 * @param <Entity> 实体
 * <AUTHOR>
 * @version 1.0
 * @description 基础接口
 * @projectName cloudbooster
 * @package com.jettech.basic.base.controller
 * @className BaseController
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface BaseController<Entity>
{

    /**
     * 获取实体的类型
     *
     * @return Class<Entity> 实体的类型
     * <AUTHOR>
     * @date 2021/9/10 13:46
     * @update zxy 2021/9/10 13:46
     * @since 1.0
     */
    Class<Entity> getEntityClass();

    /**
     * 获取Service
     *
     * @return SuperService<Entity> Service
     * <AUTHOR>
     * @date 2021/9/10 13:46
     * @update zxy 2021/9/10 13:46
     * @since 1.0
     */
    SuperService<Entity> getBaseService();

    /**
     * 成功返回
     *
     * @param <T> 返回类型
     * @param data 返回内容
     * @return R<T> 成功
     * <AUTHOR>
     * @date 2021/9/10 13:47
     * @update zxy 2021/9/10 13:47
     * @since 1.0
     */
    default <T> R<T> success(T data)
    {
        return R.success(data);
    }

    /**
     * 成功返回
     *
     * @return R<Boolean> 成功
     * <AUTHOR>
     * @date 2021/9/10 13:48
     * @update zxy 2021/9/10 13:48
     * @since 1.0
     */
    default R<Boolean> success()
    {
        return R.success();
    }

    /**
     * 失败返回
     *
     * @param <T> 返回类型
     * @param msg 失败消息
     * @return R<T> 失败
     * <AUTHOR>
     * @date 2021/9/10 13:49
     * @update zxy 2021/9/10 13:49
     * @since 1.0
     */
    default <T> R<T> fail(String msg)
    {
        return R.fail(msg);
    }

    /**
     * 失败返回
     *
     * @param <T> 返回类型
     * @param msg 失败消息
     * @param args 动态参数
     * @return R<T> 失败
     * <AUTHOR>
     * @date 2021/9/10 13:51
     * @update zxy 2021/9/10 13:51
     * @since 1.0
     */
    default <T> R<T> fail(String msg, Object... args)
    {
        return R.fail(msg, args);
    }

    /**
     * 失败返回
     *
     * @param <T> 返回类型
     * @param code 失败编码
     * @param msg 失败消息
     * @return R<T> 失败
     * <AUTHOR>
     * @date 2021/9/10 13:52
     * @update zxy 2021/9/10 13:52
     * @since 1.0
     */
    default <T> R<T> fail(int code, String msg)
    {
        return R.fail(code, msg);
    }

    /**
     * 失败返回
     *
     * @param <T> 返回类型
     * @param exceptionCode 失败异常码
     * @return R<T> 失败
     * <AUTHOR>
     * @date 2021/9/10 13:52
     * @update zxy 2021/9/10 13:52
     * @since 1.0
     */
    default <T> R<T> fail(BaseExceptionCode exceptionCode)
    {
        return R.fail(exceptionCode);
    }

    /**
     * 失败返回
     *
     * @param <T> 返回类型
     * @param exception 异常
     * @return R<T> 失败
     * <AUTHOR>
     * @date 2021/9/10 13:53
     * @update zxy 2021/9/10 13:53
     * @since 1.0
     */
    default <T> R<T> fail(BizException exception)
    {
        return R.fail(exception);
    }

    /**
     * 失败返回
     *
     * @param <T> 返回类型
     * @param throwable 异常
     * @return R<T> 失败
     * <AUTHOR>
     * @date 2021/9/10 13:54
     * @update zxy 2021/9/10 13:54
     * @since 1.0
     */
    default <T> R<T> fail(Throwable throwable)
    {
        return R.fail(throwable);
    }

    /**
     * 参数校验失败返回
     *
     * @param <T> 返回类型
     * @param msg 错误消息
     * @return R<T> 失败
     * <AUTHOR>
     * @date 2021/9/10 13:55
     * @update zxy 2021/9/10 13:55
     * @since 1.0
     */
    default <T> R<T> validFail(String msg)
    {
        return R.validFail(msg);
    }

    /**
     * 参数校验失败返回
     *
     * @param <T> 返回类型
     * @param msg 错误消息
     * @param args 错误参数
     * @return R<T> 失败
     * <AUTHOR>
     * @date 2021/9/10 13:55
     * @update zxy 2021/9/10 13:55
     * @since 1.0
     */
    default <T> R<T> validFail(String msg, Object... args)
    {
        return R.validFail(msg, args);
    }

    /**
     * 参数校验失败返回
     *
     * @param <T> 返回类型
     * @param exceptionCode 错误编码
     * @return R<T> 失败
     * <AUTHOR>
     * @date 2021/9/10 13:56
     * @update zxy 2021/9/10 13:56
     * @since 1.0
     */
    default <T> R<T> validFail(BaseExceptionCode exceptionCode)
    {
        return R.validFail(exceptionCode);
    }

    /**
     * 获取当前登录用户id
     *
     * @return Long 当前登录用户id
     * <AUTHOR>
     * @date 2021/9/10 13:57
     * @update zxy 2021/9/10 13:57
     * @since 1.0
     */
    default Long getUserId()
    {
        return ContextUtil.getUserId();
    }

    /**
     * 获取当前登录用户租户编码
     *
     * @return String 当前租户编码
     * <AUTHOR>
     * @date 2021/9/10 13:57
     * @update zxy 2021/9/10 13:57
     * @since 1.0
     */
    default String getTenant()
    {
        return ContextUtil.getTenant();
    }

    /**
     * 获取当前登录人账号
     *
     * @return String 当前登录人账号
     * <AUTHOR>
     * @date 2021/9/10 13:58
     * @update zxy 2021/9/10 13:58
     * @since 1.0
     */
    default String getUserAccount()
    {
        return ContextUtil.getUserAccount();
    }

    /**
     * 获取当前登录人姓名
     *
     * @return String 当前登录人姓名
     * <AUTHOR>
     * @date 2021/9/10 13:59
     * @update zxy 2021/9/10 13:59
     * @since 1.0
     */
    default String getUserName()
    {
        return ContextUtil.getUserName();
    }

    /**
     * 获取当前登录人组织机构ID
     *
     * @return Long 当前登录人组织机构ID
     * <AUTHOR>
     * @date 2021/9/15 11:23
     * @update zxy 2021/9/15 11:23
     * @since 1.0
     */
    default Long getOrgId()
    {
        return ContextUtil.getOrgId();
    }

}
