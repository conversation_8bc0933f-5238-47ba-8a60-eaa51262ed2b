package com.jettech.basic.base.controller;

import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.service.SuperCacheService;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import java.io.Serializable;

/**
 * 缓存Controller
 * 继承该类，在SuperController类的基础上扩展了以下方法：
 * 1，get ： 根据ID查询缓存，若缓存不存在，则查询DB
 *
 * <AUTHOR>
 * @version 1.0
 * @description 缓存Controller
 * @projectName cloudbooster
 * @package com.jettech.basic.base.controller
 * @className SuperCacheController
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public abstract class SuperCacheController<S extends SuperCacheService<Entity>, Id extends Serializable, Entity,
        PageQuery, SaveDTO, UpdateDTO>
        extends SuperController<S, Id, Entity, PageQuery, SaveDTO, UpdateDTO>
{


    /**
     * 根据ID查询
     *
     * @param id 根据ID查询
     * @return R<Entity> 查询结果
     * <AUTHOR>
     * @date 2021/9/13 10:17
     * @update zxy 2021/9/13 10:17
     * @since 1.0
     */
    @Override
    @SysLog("'查询:' + #id")
    @PreAuth("hasAnyPermission('{}view')")
    public R<Entity> get(@PathVariable Id id)
    {
        return success(baseService.getByIdCache(id));
    }

    /**
     * 刷新缓存
     *
     * @return R<Boolean> 刷新是否成功
     * <AUTHOR>
     * @date 2021/9/13 10:18
     * @update zxy 2021/9/13 10:18
     * @since 1.0
     */
    @ApiOperation(value = "刷新缓存", notes = "刷新缓存")
    @PostMapping("refreshCache")
    @SysLog(value = "'刷新缓存'", optType = OptLogTypeEnum.OTHER)
    @PreAuth("hasAnyPermission('{}add')")
    public R<Boolean> refreshCache()
    {
        baseService.refreshCache();
        return success(true);
    }

    /**
     * 清理缓存
     *
     * @return R<Boolean> 清理是否成功
     * <AUTHOR>
     * @date 2021/9/13 10:19
     * @update zxy 2021/9/13 10:19
     * @since 1.0
     */
    @ApiOperation(value = "清理缓存", notes = "清理缓存")
    @PostMapping("clearCache")
    @SysLog(value = "'清理缓存'", optType = OptLogTypeEnum.OTHER)
    @PreAuth("hasAnyPermission('{}add')")
    public R<Boolean> clearCache()
    {
        baseService.clearCache();
        return success(true);
    }
}
