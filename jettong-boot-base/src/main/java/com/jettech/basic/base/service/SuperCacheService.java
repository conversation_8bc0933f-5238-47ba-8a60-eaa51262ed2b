package com.jettech.basic.base.service;

import com.jettech.basic.cache.model.CacheKey;
import org.springframework.lang.NonNull;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;

/**
 * 基于MP的 IService 新增了3个方法： getByIdCache
 * 其中：
 * 1，getByIdCache 方法 会先从缓存查询，后从DB查询 （取决于实现类）
 * 2、SuperService 上的方法
 *
 * @param <T> 实体
 * <AUTHOR>
 * @version 1.0
 * @description 基于MP的 IService 新增了3个方法
 * @projectName cloudbooster
 * @package com.jettech.basic.base.service
 * @className SuperCacheService
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface SuperCacheService<T> extends SuperService<T>
{

    /**
     * 根据id 先查缓存，再查db
     *
     * @param id 主键
     * @return T 对象
     * <AUTHOR>
     * @date 2021/9/13 10:40
     * @update zxy 2021/9/13 10:40
     * @since 1.0
     */
    T getByIdCache(Serializable id);

    /**
     * 根据 key 查询缓存中存放的id，缓存不存在根据loader加载并写入数据，然后根据查询出来的id查询 实体
     *
     * @param key 缓存key
     * @param loader 加载器
     * @return T 对象
     * <AUTHOR>
     * @date 2021/9/13 10:41
     * @update zxy 2021/9/13 10:41
     * @since 1.0
     */
    T getByKey(CacheKey key, Function<CacheKey, Object> loader);

    /**
     * 根据id查询数据，可能会出现缓存穿透
     *
     * @param ids 主键id
     * @param loader 回调
     * @return List<T> 对象集合
     * <AUTHOR>
     * @date 2021/9/13 10:41
     * @update zxy 2021/9/13 10:41
     * @since 1.0
     */
    List<T> findByIds(@NonNull Collection<? extends Serializable> ids,
            Function<Collection<? extends Serializable>, Collection<T>> loader);

    /**
     * 刷新缓存
     *
     * <AUTHOR>
     * @date 2021/9/13 10:43
     * @update zxy 2021/9/13 10:43
     * @since 1.0
     */
    void refreshCache();

    /**
     * 清理缓存
     *
     * <AUTHOR>
     * @date 2021/9/13 10:43
     * @update zxy 2021/9/13 10:43
     * @since 1.0
     */
    void clearCache();
}
