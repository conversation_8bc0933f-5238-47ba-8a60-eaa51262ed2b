package com.jettech.basic.base.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.QueryWrap;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.Serializable;
import java.util.List;

/**
 * 查询Controller
 *
 * @param <Entity> 实体
 * @param <Id> 主键
 * @param <PageQuery> 分页参数
 * <AUTHOR>
 * @version 1.0
 * @description 查询Controller
 * @projectName cloudbooster
 * @package com.jettech.basic.base.controller
 * @className QueryController
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface QueryController<Entity, Id extends Serializable, PageQuery> extends PageController<Entity, PageQuery>
{

    /**
     * 根据主键id查询实体
     *
     * @param id 主键id
     * @return R<Entity> 查询结果
     * <AUTHOR>
     * @date 2021/9/10 14:09
     * @update zxy 2021/9/10 14:09
     * @since 1.0
     */
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键", dataType = "long", paramType = "path"),
    })
    @ApiOperation(value = "单体查询", notes = "单体查询")
    @GetMapping("/{id}")
    @SysLog("'查询:' + #id")
    @PreAuth("hasAnyPermission('{}view')")
    default R<Entity> get(@PathVariable Id id)
    {
        return success(getBaseService().getById(id));
    }

    /**
     * 分页查询
     *
     * @param params 分页参数
     * @return R<IPage < Entity>> 分页数据
     * <AUTHOR>
     * @date 2021/9/10 14:10
     * @update zxy 2021/9/10 14:10
     * @since 1.0
     */
    @ApiOperation(value = "分页列表查询")
    @PostMapping("/page")
    @SysLog(value = "'分页列表查询:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    @PreAuth("hasAnyPermission('{}view')")
    default R<IPage<Entity>> page(@RequestBody @Validated PageParams<PageQuery> params)
    {
        return success(query(params));
    }

    /**
     * 批量查询
     *
     * @param data 批量查询
     * @return R<List < Entity>> 查询结果
     * <AUTHOR>
     * @date 2021/9/10 14:10
     * @update zxy 2021/9/10 14:10
     * @since 1.0
     */
    @ApiOperation(value = "批量查询", notes = "批量查询")
    @PostMapping("/query")
    @SysLog("批量查询")
    @PreAuth("hasAnyPermission('{}view')")
    default R<List<Entity>> query(@RequestBody Entity data)
    {
        QueryWrap<Entity> wrapper = Wraps.q(data);
        return success(getBaseService().list(wrapper));
    }

}
