package com.jettech.basic.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 基于MP的 BaseMapper 新增了2个方法： insertBatchSomeColumn、updateAllById
 *
 * @param <T> 实体
 * <AUTHOR>
 * @version 1.0
 * @description 基于MP的 BaseMapper 新增了2个方法： insertBatchSomeColumn、updateAllById
 * @projectName cloudbooster
 * @package com.jettech.basic.base.mapper
 * @className SuperMapper
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface SuperMapper<T> extends BaseMapper<T>
{

    /**
     * 全量修改所有字段
     *
     * @param entity 实体
     * @return int 修改数量
     * <AUTHOR>
     * @date 2021/9/13 10:31
     * @update zxy 2021/9/13 10:31
     * @since 1.0
     */
    int updateAllById(@Param(Constants.ENTITY) T entity);

    /**
     * 批量插入所有字段
     *
     * @param entityList 实体集合
     * @return int 插入数量
     * <AUTHOR>
     * @date 2021/9/13 10:32
     * @update zxy 2021/9/13 10:32
     * @since 1.0
     */
    int insertBatchSomeColumn(List<T> entityList);

}
