package com.jettech.basic.base.controller;

import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.Serializable;
import java.util.List;

/**
 * 删除Controller接口
 *
 * @param <Entity> 实体
 * @param <Id> 主键
 * <AUTHOR>
 * @version 1.0
 * @description 删除Controller接口
 * @projectName cloudbooster
 * @package com.jettech.basic.base.controller
 * @className DeleteController
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface DeleteController<Entity, Id extends Serializable> extends BaseController<Entity>
{

    /**
     * 根据id删除
     *
     * @param ids id
     * @return R<Boolean> 删除结果
     * <AUTHOR>
     * @date 2021/9/10 14:00
     * @update zxy 2021/9/10 14:00
     * @since 1.0
     */
    @ApiOperation(value = "删除")
    @DeleteMapping
    @SysLog(value = "'删除:' + #ids", optType = OptLogTypeEnum.DELETE)
    @PreAuth("hasAnyPermission('{}delete')")
    default R<Boolean> delete(@RequestBody List<Id> ids)
    {
        R<Boolean> result = handlerDelete(ids);
        if (result.getDefExec())
        {
            getBaseService().removeByIds(ids);
        }
        return result;
    }

    /**
     * 根据id自定义删除
     *
     * @param ids id
     * @return R<Boolean> 返回SUCCESS_RESPONSE, 调用默认更新, 返回其他不调用默认更新
     * <AUTHOR>
     * @date 2021/9/10 14:00
     * @update zxy 2021/9/10 14:00
     * @since 1.0
     */
    default R<Boolean> handlerDelete(List<Id> ids)
    {
        return R.successDef(true);
    }

}
