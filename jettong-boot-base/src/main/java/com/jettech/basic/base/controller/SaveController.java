package com.jettech.basic.base.controller;

import cn.hutool.core.bean.BeanUtil;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 新增Controller
 *
 * @param <Entity> 实体
 * @param <SaveDTO> 新增参数
 * <AUTHOR>
 * @version 1.0
 * @description 新增Controller
 * @projectName cloudbooster
 * @package com.jettech.basic.base.controller
 * @className SaveController
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface SaveController<Entity, SaveDTO> extends BaseController<Entity>
{

    /**
     * 新增
     *
     * @param saveDTO 新增对象
     * @return R<Entity> 新增的实体对象
     * <AUTHOR>
     * @date 2021/9/13 10:12
     * @update zxy 2021/9/13 10:12
     * @since 1.0
     */
    @ApiOperation(value = "新增")
    @PostMapping
    @SysLog(value = "新增", request = false, optType = OptLogTypeEnum.ADD)
    @PreAuth("hasAnyPermission('{}add')")
    default R<Entity> save(@RequestBody @Validated SaveDTO saveDTO)
    {
        R<Entity> result = handlerSave(saveDTO);
        if (result.getDefExec())
        {
            Entity model = BeanUtil.toBean(saveDTO, getEntityClass());
            getBaseService().save(model);
            result.setData(model);
        }
        return result;
    }

    /**
     * 自定义新增，子类实现
     *
     * @param model 新增对象
     * @return R<Entity> 返回SUCCESS_RESPONSE, 调用默认更新, 返回其他不调用默认更新
     * <AUTHOR>
     * @date 2021/9/13 10:13
     * @update zxy 2021/9/13 10:13
     * @since 1.0
     */
    default R<Entity> handlerSave(SaveDTO model)
    {
        return R.successDef();
    }

}
