package com.jettech.basic.base.request;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.utils.DateUtils;

import java.util.Map;

/**
 * 分页工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 分页工具类
 * @projectName cloudbooster
 * @package com.jettech.basic.base.request
 * @className PageUtil
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class PageUtil
{
    private PageUtil()
    {
    }

    /**
     * 重置时间区间参数
     *
     * @param params 分页参数
     * <AUTHOR>
     * @date 2021/9/13 10:37
     * @update zxy 2021/9/13 10:37
     * @since 1.0
     */
    public static <T> void timeRange(PageParams<T> params)
    {
        if (params == null)
        {
            return;
        }
        Map<String, Object> extra = params.getExtra();
        if (MapUtil.isEmpty(extra))
        {
            return;
        }
        for (Map.Entry<String, Object> field : extra.entrySet())
        {
            String key = field.getKey();
            Object value = field.getValue();
            if (ObjectUtil.isEmpty(value))
            {
                continue;
            }
            if (key.endsWith(Wraps.ST))
            {
                extra.put(key, DateUtils.getStartTime(value.toString()));
            }
            else if (key.endsWith(Wraps.ED))
            {
                extra.put(key, DateUtils.getEndTime(value.toString()));
            }
        }
    }
}
