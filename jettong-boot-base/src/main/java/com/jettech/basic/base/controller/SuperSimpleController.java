package com.jettech.basic.base.controller;

import com.jettech.basic.base.service.SuperService;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.ParameterizedType;

/**
 * 简单的实现了BaseController，为了获取注入 Service 和 实体类型
 * <p>
 * 基类该类后，没有任何方法。
 * 可以让业务Controller继承 SuperSimpleController 后，按需实现 *Controller 接口
 *
 * @param <S> Service
 * @param <Entity> 实体
 * <AUTHOR>
 * @version 1.0
 * @description 简单的实现了BaseController，为了获取注入 Service 和 实体类型
 * @projectName cloudbooster
 * @package com.jettech.basic.base.controller
 * @className SuperSimpleController
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public abstract class SuperSimpleController<S extends SuperService<Entity>, Entity> implements BaseController<Entity>
{

    Class<Entity> entityClass = null;
    @Autowired
    protected S baseService;

    @Override
    public Class<Entity> getEntityClass()
    {
        if (entityClass == null)
        {
            this.entityClass = (Class<Entity>) ((ParameterizedType) this.getClass()
                    .getGenericSuperclass()).getActualTypeArguments()[1];
        }
        return this.entityClass;
    }

    @Override
    public SuperService<Entity> getBaseService()
    {
        return baseService;
    }
}
