package com.jettech.basic.security.service;

import cn.hutool.core.util.RandomUtil;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-util
 * @package com.jettech.basic.security.service
 * @className CryptoHandle
 * @date 2025/8/19 17:53
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface CryptoHandle {

    String encrypt(String raw, String salt) throws Exception;

    String decrypt(String raw) throws Exception;

    boolean verify(String raw, String encryptRaw, String salt) throws Exception;

    /**
     * 生成算法需要的盐值
     * @return 盐值 (可为空)
     */
    default String generateSalt() {
//        return RandomUtil.randomString(20);
        return null;
    }
}
