package com.jettech.basic.security.service;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Value;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Base64;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-util
 * @package com.jettech.basic.security.service
 * @className AesCryptoHandle
 * @date 2025/8/19 17:59
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class Sm4CryptoHandle implements CryptoHandle {

    @Value("${jettong.security.secretKey}")
    private String secretKey;

    private static final String ALGORITHM_NAME = "SM4";
    private static final String ALGORITHM_ECB = "SM4/ECB/PKCS5Padding";
    private static final int DEFAULT_KEY_SIZE = 128;

    static {
        // 注册 Bouncy Castle 提供者
        if (Security.getProvider("BC") == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

//    private static final String FIXED_KEY = "MWH9asci7OiCKiscyxwF8Q=="; // 128位Base64编码密钥:ml-citation{ref="2,9" data="citationList"}

    public static String generateKey() throws Exception {
        KeyGenerator kg = KeyGenerator.getInstance(ALGORITHM_NAME, "BC");
        kg.init(DEFAULT_KEY_SIZE);
        SecretKey secretKey = kg.generateKey();
        return Base64.getEncoder().encodeToString(secretKey.getEncoded());
    }


    @Override
    public String encrypt(String raw, String salt) throws Exception {
//        String key = FIXED_KEY ;
        String plainText = raw + salt;
        byte[] keyBytes = Base64.getDecoder().decode(secretKey);
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, ALGORITHM_NAME);
        Cipher cipher = Cipher.getInstance(ALGORITHM_ECB, "BC");
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encrypted);
    }

    @Override
    public String decrypt(String raw) throws Exception {
//        String key = FIXED_KEY;
        byte[] keyBytes = Base64.getDecoder().decode(secretKey);
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, ALGORITHM_NAME);
        Cipher cipher = Cipher.getInstance(ALGORITHM_ECB, "BC");
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
        byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(raw));
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    @Override
    public boolean verify(String raw, String encryptRaw, String salt) throws Exception
    {
        String encrypt = encrypt(raw,salt);

        return encrypt.equals(encryptRaw);
    }
}
