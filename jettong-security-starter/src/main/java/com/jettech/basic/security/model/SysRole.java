package com.jettech.basic.security.model;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import lombok.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户角色实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户角色实体类
 * @projectName jettong
 * @package com.jettech.basic.security.model
 * @className SysRole
 * @date 2022/6/6 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode
public class SysRole
{
    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 角色编码
     */
    private String code;

    /**
     * 功能描述
     */
    private String description;

    /**
     * 是否启用
     */
    private Boolean state;

    /**
     * 是否只读角色
     */
    private Boolean readonly;

    /**
     * 角色列表转换成角色编码列表
     *
     * @param list 角色列表
     * @return {@link List<String>} 角色编码列表
     * <AUTHOR>
     * @date 2022/6/6 10:12
     * @update 2022/6/6 10:12
     * @since 1.0
     */
    public static List<String> getRoleCode(List<SysRole> list)
    {
        if (ArrayUtil.isEmpty(list))
        {
            return Collections.emptyList();
        }
        return list.stream().map(SysRole::getCode).collect(Collectors.toList());
    }

    /**
     * 指定角色编码是否在角色列表中
     * @param list 角色列表
     * @param code 角色编码
     * @return {@link boolean} true/false
     * <AUTHOR>
     * @date 2022/6/6 10:12
     * @update 2022/6/6 10:12
     * @since 1.0
     */
    public static boolean contains(List<SysRole> list, String code)
    {
        if (ArrayUtil.isEmpty(list) || StrUtil.isEmpty(code))
        {
            return false;
        }
        return list.stream().anyMatch((item) -> code.equals(item.getCode()));
    }
}
