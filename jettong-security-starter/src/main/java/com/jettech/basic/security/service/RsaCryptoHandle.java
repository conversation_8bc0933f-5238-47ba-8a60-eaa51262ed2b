package com.jettech.basic.security.service;

import org.springframework.beans.factory.annotation.Value;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-util
 * @package com.jettech.basic.security.service
 * @className AesCryptoHandle
 * @date 2025/8/19 17:59
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class RsaCryptoHandle implements CryptoHandle {

    @Value("${jettong.security.publicKey}")
    private String publicKey;
    @Value("${jettong.security.privateKey}")
    private String privateKey;
    private static final String ALGORITHM = "RSA";

    private static final int KEY_SIZE = 2048;

//    private static final String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkLiLgpT+/mfMIRgzER1S0cfxzAdL45z448F8XHen6WbYsWhCkCB22Ba+wSV3/HJMocEoIlbgvU6PEnWnzUsSqoDZMljm2PUQYDl6/FTKRIlxwqfld/zkuxiJ/9C4uwZqAtV7Gwqb4FGjNeEaiS9BbHuJ8ABLfj4wnWSgc5iumsWK4VYrZk6KxtgQVSp4n79SOLdjDovb6tc310enR2gnfLXz1uRNIQ+kjLY2TaMIOYEl3hqLT1aFP5oQ2LZakZ7CHQn69Cmm1n6LK5HcJQ/xt+pPN6Kja+mhZ4eyMGN6/09U7IYvDsxw7JbBzhTw2k5qUiT9k5COSDyEIzjnwAb9LwIDAQAB";
//    private static final String privateKey = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCQuIuClP7+Z8whGDMRHVLRx/HMB0vjnPjjwXxcd6fpZtixaEKQIHbYFr7BJXf8ckyhwSgiVuC9To8SdafNSxKqgNkyWObY9RBgOXr8VMpEiXHCp+V3/OS7GIn/0Li7BmoC1XsbCpvgUaM14RqJL0Fse4nwAEt+PjCdZKBzmK6axYrhVitmTorG2BBVKnifv1I4t2MOi9vq1zfXR6dHaCd8tfPW5E0hD6SMtjZNowg5gSXeGotPVoU/mhDYtlqRnsIdCfr0KabWfosrkdwlD/G36k83oqNr6aFnh7IwY3r/T1Tshi8OzHDslsHOFPDaTmpSJP2TkI5IPIQjOOfABv0vAgMBAAECggEAWMVajhbm8xaoWASIDo92JcgHwacYHd5DrTEaH34mOT0k3m2dptrDC3moOMHK/8b8ypu5CpdP888kY6ZX782E/aFbmU5/C8YG7X9UuUMpbzD/3u8uCpPNmhZLT+ev6Mjebi6U/NldFBP/kSUrKcsaznhY/CayKBXtrB31bfLR540h+1gAidaGpYhmiDJhbRcyvOPmZaV+ejOkhlXTUVqVbliNoQPKol2/nM7JA8WkVz274sIpwXhBdaUNE8xZrJLoaMYw9kvOr4ZQFgr6IZieao5qpmzW9v/XbHPkfqqxQsn2TPI4X6z2A//iRMlnjRVm0uHcAZSuFeLaviF0Sq9TAQKBgQD0BKQCfs/W9zBGhquZgIm1jdZbRbJpXgpqDeupclTMd2v7ynUxe2Ort0Ir6cnZrFp+bji5Kb1QHZK7o4qDdgLiK2PecOLjDXxjxpmhpfbcrADc8jym0LlhTMV9sX0I4GK0uBXoMrxycJNEc8q3loFh6zHjHSoxzW4CaRTE354V/wKBgQCX07eJBrr3sgLnTNWE5rUG9tjbtQHFFVHhim1NagCDdx0Pk0bwnDGJtLSnLuy+fdqsu3JF3xPQbW/n+cKNb2J6ihYG7zL3Xxy/8qECU16STP07HhJm9Jrjj4EKPusIBJCpovyL09OJZ/RetHCOz8RY5NS/P6EacDQ9jlo5hVj40QKBgFDhAON62LStdajKljHosrwkaPoTndDixJzmREgDhyvGIVpxfmqKNT0DHsGjWa32PIBPVCm7Ne5J/wqGaDCuesIwE4f07p7HJ4WCnAVCFxKJSDfzajr4T5l6crL4kzLP/KddpJUzy9Y6jfVQU86J6hRkekKGtiePqXjPD305iRJNAoGAElmreCtRE7j3Mt+NewerX5EcOsnQ0fI2uEzT0yR5tWoDICvLyi8Gqb8mioJYaNmrQW9Sa3F/4pQnj2EXi6Knw2nHKcVlYpKjVevMXAyZhJM7lBaZYMo1okVo8kUSgSCUH3ysi3XpvlTLK1shs5EXCSt3LzpQjXJ8TehTrVoZm9ECgYAQqrrJuFt8o/GNWvFjEgniXTr28XeVqpkjyl4lxPa7gCoc0BFBEZdJhltXz0HC/HwsAIq2VkDIQ8ctqJYgpXaTfSjs19rIBECuT89rqJnjqeg7KEvTwy3uDkhS/l94/E569tzU3S7WZsogyOMz/sD5iEtpB2vYDD73Bk9nxqsdbw==";


    // 生成密钥对
    public static KeyPair generateKeyPair() throws Exception {
        KeyPairGenerator keyGen = KeyPairGenerator.getInstance(ALGORITHM);
        keyGen.initialize(KEY_SIZE);
        return keyGen.generateKeyPair();
    }

    public static void main(String[] args) {
        KeyPair keyPair = null;
        try
        {
            keyPair = generateKeyPair();
        }
        catch (Exception e)
        {
            throw new RuntimeException(e);
        }
        PublicKey publicKey = keyPair.getPublic();
        PrivateKey privateKey = keyPair.getPrivate();
        String publicKeyStr = Base64.getEncoder().encodeToString(publicKey.getEncoded());
        String privateKeyStr = Base64.getEncoder().encodeToString(privateKey.getEncoded());
        System.out.println("publicKey===" + publicKeyStr );
        System.out.println("privateKey===" + privateKeyStr );
    }

    @Override
    public String encrypt(String raw, String salt) throws Exception {
        String data = raw + salt;
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        PublicKey loadedPublicKey = loadPublicKey(publicKey);
        cipher.init(Cipher.ENCRYPT_MODE, loadedPublicKey);
        byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 从Base64编码的公钥字符串加载PublicKey对象
     *
     * @param publicKeyStr Base64编码的公钥字符串
     * @return PublicKey对象
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     */
    public static PublicKey loadPublicKey(String publicKeyStr) throws NoSuchAlgorithmException, InvalidKeySpecException
    {
        byte[] keyBytes = Base64.getDecoder().decode(publicKeyStr);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(spec);
    }

    @Override
    public String decrypt(String raw) throws Exception {
        String encryptedData = raw ;
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        PrivateKey loadedPrivateKey = loadPrivateKey(privateKey);
        cipher.init(Cipher.DECRYPT_MODE, loadedPrivateKey);
        byte[] decoded = Base64.getDecoder().decode(encryptedData);
        return new String(cipher.doFinal(decoded), StandardCharsets.UTF_8);
    }

    /**
     * 从Base64编码的私钥字符串加载PrivateKey对象
     *
     * @param privateKeyStr Base64编码的私钥字符串
     * @return PrivateKey对象
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     */
    public static PrivateKey loadPrivateKey(String privateKeyStr) throws NoSuchAlgorithmException, InvalidKeySpecException {
        byte[] keyBytes = Base64.getDecoder().decode(privateKeyStr);
        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(spec);
    }

    @Override
    public boolean verify(String raw, String encryptRaw, String salt) throws Exception
    {
        String encrypt = encrypt(raw,salt);

        return encrypt.equals(encryptRaw);
    }
}
