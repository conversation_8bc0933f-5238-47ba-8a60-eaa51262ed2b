package com.jettech.basic.security.model;

import lombok.*;

import java.util.List;


/**
 * 用户实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户角色实体类
 * @projectName jettong
 * @package com.jettech.basic.security.model
 * @className SysUser
 * @date 2022/6/6 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString(callSuper = true)
@Builder
public class SysUser
{
    private static final long serialVersionUID = -5886012896705137070L;

    private Long id;
    /**
     * 账号
     */
    private String account;

    /**
     * 姓名
     */
    private String name;

    /**
     * 组织ID
     * #sys_org
     */
    private Long orgId;

    /**
     * 手机
     * 启用条件： LoginUser.isFull = true || LoginUser.isUser = true
     */
    private String mobile;

    /**
     * 照片
     * 启用条件： LoginUser.isFull = true || LoginUser.isUser = true
     */
    private String avatar;

    /**
     * 描述
     * 启用条件： LoginUser.isFull = true || LoginUser.isUser = true
     */
    private String description;

    /**
     * 登录次数
     * 一直累计，记录了此账号总共登录次数
     * 启用条件： LoginUser.isFull = true || LoginUser.isUser = true
     */
    private Integer loginCount;

    /**
     * 当前登录用户的角色编码
     * 启用条件： LoginUser.isFull = true || LoginUser.isRole = true
     */
    private List<SysRole> roles;

    /**
     * 当前登录用户的资源编码
     * 启用条件： LoginUser.isFull = true || LoginUser.isResource = true
     */
    private List<String> functions;

    /**
     * 当前登录用户的组织架构
     * 启用条件： LoginUser.isFull = true || LoginUser.isOrg = true
     */
    private SysOrg org;

}
