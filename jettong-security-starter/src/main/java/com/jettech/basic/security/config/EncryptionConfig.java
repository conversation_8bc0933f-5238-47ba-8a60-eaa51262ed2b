package com.jettech.basic.security.config;

import com.jettech.basic.security.service.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-util
 * @package com.jettech.basic.security.config
 * @className EncryptionConfig
 * @date 2025/8/19 17:23
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Configuration
@RefreshScope
public class EncryptionConfig {

    @Value("${jettong.security.crypto:SHA}")
    private String encryptionType;



    @Bean
    public CryptoHandle encryptionService() {
        switch (encryptionType) {
            case "AES":
                return new AesCryptoHandle();
            case "SM4":
                return new Sm4CryptoHandle();
            case "RSA":
                return new RsaCryptoHandle();
            case "SHA":
                return new ShaCryptoHandle();
            default:
                return new ShaCryptoHandle();
        }
    }
}
