package com.jettech.basic.security.model;

import lombok.*;

/**
 * 组织机构实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 组织机构实体类
 * @projectName jettong
 * @package com.jettech.basic.security.model
 * @className SysOrg
 * @date 2022/6/6 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode
public class SysOrg
{

    private static final long serialVersionUID = 1L;
    private Long id;
    /**
     * 名称
     */
    private String name;

    /**
     * 父ID
     */
    private Long parentId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态
     */
    private Boolean state;

    /**
     * 描述
     */
    private String description;


}
