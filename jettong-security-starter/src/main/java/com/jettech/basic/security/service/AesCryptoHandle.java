package com.jettech.basic.security.service;

import org.springframework.beans.factory.annotation.Value;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-util
 * @package com.jettech.basic.security.service
 * @className AesCryptoHandle
 * @date 2025/8/19 17:59
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class AesCryptoHandle implements CryptoHandle {

    @Value("${jettong.security.secretKey}")
    private String secretKey;
    @Value("${jettong.security.secretIv}")
    private String secretIv;
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";

    @Override
    public String encrypt(String raw, String salt) throws Exception {
        String data = raw + salt;
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        byte[] keyBytes = Base64.getDecoder().decode(secretKey);
        cipher.init(Cipher.ENCRYPT_MODE,
                new SecretKeySpec(keyBytes, ALGORITHM),
                new IvParameterSpec(secretIv.getBytes()));
        byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encrypted);
    }

    @Override
    public String decrypt(String raw) throws Exception {
        String encryptedData = raw ;
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        byte[] keyBytes = Base64.getDecoder().decode(secretKey);
        cipher.init(Cipher.DECRYPT_MODE,
                new SecretKeySpec(keyBytes, ALGORITHM),
                new IvParameterSpec(secretIv.getBytes()));
        byte[] decoded = Base64.getDecoder().decode(encryptedData);
        return new String(cipher.doFinal(decoded), StandardCharsets.UTF_8);
    }

    @Override
    public boolean verify(String raw, String encryptRaw, String salt) throws Exception
    {
        String encrypt = encrypt(raw,salt);

        return encrypt.equals(encryptRaw);
    }
}
