/**
 * Copyright 2012-2020 The Feign Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
 * in compliance with the License. You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 */
package com.jettech.basic.cloud.http;

import org.slf4j.Logger;

/**
 * <AUTHOR>
 * @date 2020/8/9 上午10:01
 */
public class InfoSlf4jFeignLogger extends feign.Logger
{
    private final Logger logger;

    public InfoSlf4jFeignLogger(Logger logger)
    {
        this.logger = logger;
    }

    @Override
    protected void log(String configKey, String format, Object... args)
    {
        if (logger.isInfoEnabled())
        {
            logger.info(String.format(methodTag(configKey) + format, args));
        }
    }

}
