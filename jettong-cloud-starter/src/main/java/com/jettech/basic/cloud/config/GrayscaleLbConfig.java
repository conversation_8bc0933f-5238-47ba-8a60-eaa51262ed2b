package com.jettech.basic.cloud.config;

import com.jettech.basic.cloud.rule.GrayscaleVersionRoundRobinLoadBalancer;
import com.jettech.basic.utils.StrPool;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.loadbalancer.core.ReactorLoadBalancer;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;

/**
 * 灰度配置
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2021/7/13 11:36 上午
 * @create [2021/7/13 11:36 上午 ] [tangyh] [初始创建]
 */
public class GrayscaleLbConfig
{

    @Bean
    public ReactorLoadBalancer<ServiceInstance> reactorServiceInstanceLoadBalancer(Environment environment,
            LoadBalancerClientFactory loadBalancerClientFactory)
    {
        String name = environment.getProperty(LoadBalancerClientFactory.PROPERTY_NAME, StrPool.EMPTY);
        return new GrayscaleVersionRoundRobinLoadBalancer(
                loadBalancerClientFactory.getLazyProvider(name, ServiceInstanceListSupplier.class), name);
    }
}
