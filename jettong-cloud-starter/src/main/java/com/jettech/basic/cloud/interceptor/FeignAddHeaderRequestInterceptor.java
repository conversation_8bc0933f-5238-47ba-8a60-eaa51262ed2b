package com.jettech.basic.cloud.interceptor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import com.jettech.basic.context.ContextConstants;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.utils.StrPool;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * feign client 拦截器， 实现将 feign 调用方的 请求头封装到 被调用方的请求头
 *
 * <AUTHOR>
 * @date 2019-07-25 11:23
 */
@Slf4j
public class FeignAddHeaderRequestInterceptor implements RequestInterceptor
{

    public static final List<String> HEADER_NAME_LIST = Arrays.asList(
            ContextConstants.JWT_KEY_TENANT, ContextConstants.JWT_KEY_SUB_TENANT, ContextConstants.JWT_KEY_USER_ID,
            ContextConstants.JWT_KEY_USER_ACCOUNT, ContextConstants.JWT_KEY_USER_NAME, ContextConstants.GRAY_VERSION,
            ContextConstants.TRACE_ID_HEADER, "X-Real-IP", "x-forwarded-for"
    );

    public FeignAddHeaderRequestInterceptor()
    {
        super();
    }

    /**
     * 中文正则处理
     */
    private static final Pattern PATTERN = Pattern.compile("[\u4e00-\u9fa5]");

    @Override
    public void apply(RequestTemplate template)
    {

        template.header(ContextConstants.FEIGN, StrPool.TRUE);

        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null)
        {
            HEADER_NAME_LIST.forEach((headerName) -> {
                Matcher m = PATTERN.matcher(ContextUtil.get(headerName));
                template.header(headerName, m.find() ? URLUtil.encode(ContextUtil.get(headerName)) : ContextUtil.get(headerName));
            });
            return;
        }

        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        if (request == null)
        {
            log.warn("path={}, 在FeignClient API接口未配置FeignConfiguration类， 故而无法在远程调用时获取请求头中的参数!", template.path());
            return;
        }
        HEADER_NAME_LIST.forEach((headerName) ->
        {
            String header = request.getHeader(headerName);
            template.header(headerName, ObjectUtil.isEmpty(header) ? ContextUtil.get(headerName) : header);
        });
    }
}
