/*
 * Copyright 2016-2020 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.jettech.basic.cloud.http;

import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignLoggerFactory;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @date 2020/8/9 上午10:03
 * @see org.springframework.cloud.openfeign.DefaultFeignLoggerFactory
 */
public class InfoFeignLoggerFactory implements FeignLoggerFactory
{
    @Override
    public feign.Logger create(Class<?> type)
    {
        return new InfoSlf4jFeignLogger(LoggerFactory.getLogger(type));
    }
}
