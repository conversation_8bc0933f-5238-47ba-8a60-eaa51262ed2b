<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jettong-util</artifactId>
        <groupId>com.jettech.basic</groupId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jettong-all</artifactId>
    <description>jettong公共工具包依赖引入</description>

    <dependencies>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-annotation</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-util-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-boot-base</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-cache-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-cloud-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-databases</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-dozer-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-echo-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-jwt-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-log-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-security-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-swagger2-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-validator-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-xss-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-view-starter</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>

</project>