package com.jettech.basic.echo;

import com.jettech.basic.echo.aspect.EchoResultAspect;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.echo.properties.EchoProperties;
import com.jettech.basic.model.LoadService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 关联字段数据注入工具 自动配置类
 *
 * <AUTHOR>
 * @date 2019/09/20
 */
@Slf4j
@Configuration
@AllArgsConstructor
@EnableConfigurationProperties(EchoProperties.class)
public class EchoAutoConfiguration
{
    private final EchoProperties remoteProperties;

    @<PERSON>
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = EchoProperties.PREFIX, name = "aop-enabled", havingValue = "true",
            matchIfMissing = true)
    public EchoResultAspect getEchoResultAspect(EchoService echoService)
    {
        return new EchoResultAspect(echoService);
    }

    /**
     * 回显服务
     *
     * @param strategyMap 回显查询实例
     * @return EchoService service对象
     * <AUTHOR>
     * @date 2021/10/28 11:46
     * @update zxy 2021/10/28 11:46
     * @since 1.0
     */
    @Bean
    @ConditionalOnMissingBean
    public EchoService getEchoService(Map<String, LoadService> strategyMap)
    {
        return new EchoService(remoteProperties, strategyMap);
    }

}

