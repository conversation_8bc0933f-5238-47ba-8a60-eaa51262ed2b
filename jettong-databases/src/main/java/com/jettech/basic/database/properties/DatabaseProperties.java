package com.jettech.basic.database.properties;

import com.baomidou.mybatisplus.annotation.DbType;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

import static com.jettech.basic.database.properties.DatabaseProperties.PREFIX;
import static com.jettech.basic.database.properties.MultiTenantType.SCHEMA;


/**
 * 客户端认证配置
 *
 * <AUTHOR>
 * @date 2018/11/20
 */
@Data
@NoArgsConstructor
@ConfigurationProperties(prefix = PREFIX)
public class DatabaseProperties
{
    public static final String PREFIX = "jettong.database";
    /**
     * 是否启用 防止全表更新与删除插件
     */
    private Boolean isBlockAttack = false;
    /**
     * 是否启用  sql性能规范插件
     */
    private Boolean isIllegalSql = false;

    /**
     * 是否p6spy在控制台打印日志
     */
    private Boolean p6spy = false;

    /**
     * 分页大小限制
     */
    private long limit = -1;

    private DbType dbType = DbType.MYSQL;

    /**
     * 溢出总页数后是否进行处理
     */
    protected Boolean overflow = true;

    /**
     * 生成 countSql 优化掉 join 现在只支持 left join
     */
    protected Boolean optimizeJoin = true;

    /**
     * 是否禁止写入
     */
    private Boolean isNotWrite = false;

    /**
     * 是否启用数据权限
     */
    private Boolean isDataScope = false;


    /**
     * 租户库 前缀
     */
    private String tenantDatabasePrefix = "jettong_base";

    /**
     * 多租户模式
     */
    private MultiTenantType multiTenantType = SCHEMA;

    /**
     * 租户id 列名
     * <p>
     * 使用于 COLUMN 模式
     */
    private String tenantIdColumn = "tenant_code";
}
