package com.jettech.basic.mq.rocket.vo;

import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.common.message.MessageExt;

import java.util.List;

/**
 * Mq消费者参数Builder对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description Mq消费者参数Builder对象
 * @projectName jettong-util
 * @package com.jettech.basic.mq.rocket.vo
 * @className RocketMqConsumerParamBuilder
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class RocketMqConsumerParamBuilder
{

    private List<MessageExt> list;
    private ConsumeConcurrentlyContext concurrentlyContext;
    private ConsumeOrderlyContext orderlyContext;

    public RocketMqConsumerParamBuilder list(List<MessageExt> list)
    {
        this.list = list;
        return this;
    }

    public RocketMqConsumerParamBuilder concurrentlyContext(ConsumeConcurrentlyContext concurrentlyContext)
    {
        this.concurrentlyContext = concurrentlyContext;
        return this;
    }

    public RocketMqConsumerParamBuilder orderlyContext(ConsumeOrderlyContext orderlyContext)
    {
        this.orderlyContext = orderlyContext;
        return this;
    }

    public RocketMqConsumerParam build()
    {
        return new RocketMqConsumerParam(this.list, this.concurrentlyContext, this.orderlyContext);
    }

}
