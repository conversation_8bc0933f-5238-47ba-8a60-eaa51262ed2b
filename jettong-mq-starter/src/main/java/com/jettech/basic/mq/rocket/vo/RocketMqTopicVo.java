package com.jettech.basic.mq.rocket.vo;

import lombok.Data;

/**
 * 主题VO对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 主题VO对象
 * @projectName jettong-util
 * @package com.jettech.basic.mq.rocket.vo
 * @className RocketMqTopicVo
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class RocketMqTopicVo
{

    private String topicName;

    private String tagName;

}
