package com.jettech.basic.mq.rabbit.vo;

import lombok.Data;


/**
 * RabbitMq消费者配置VO对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description RabbitMq消费者配置VO对象
 * @projectName jettong-util
 * @package com.jettech.basic.mq.rocket.vo
 * @className KafkaConsumerVo
 * @date 2022/12/7 11:53
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class RabbitMqConsumerVo
{

    /**
     * rabbit mq host
     */
    private String host;

    /**
     * rabbit mq port
     */
    private Integer port;

    /**
     * rabbit mq 虚拟主机
     */
    private String virtualHost;

    /**
     * rabbit mq 连接用户名
     */
    private String userName;

    /**
     * rabbit mq 连接密码
     */
    private String password;

    /**
     * rabbit mq 队列
     */
    private String queueName;

}
