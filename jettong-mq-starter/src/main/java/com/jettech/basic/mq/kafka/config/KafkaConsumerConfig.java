package com.jettech.basic.mq.kafka.config;

import com.jettech.basic.mq.kafka.vo.KafkaConsumerVo;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Kafka 消费者配置
 *
 * <AUTHOR>
 * @version 1.0
 * @description Kafka 消费者配置
 * @projectName jettong-util
 * @package com.jettech.basic.mq.kafka.config
 * @className KafkaConsumerConfig
 * @date 2022/12/7 11:53
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
@Configuration
@ConfigurationProperties(prefix = "kafka.consumer")
public class KafkaConsumerConfig
{

    private List<KafkaConsumerVo> consumerlist;

    public List<KafkaConsumerVo> getConsumerlist()
    {
        return consumerlist;
    }

    public void setConsumerlist(List<KafkaConsumerVo> consumerlist)
    {
        this.consumerlist = consumerlist;
    }
}
