package com.jettech.basic.mq.kafka.config;

import cn.hutool.core.util.StrUtil;
import com.jettech.basic.mq.kafka.vo.KafkaProducerVo;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.annotation.Order;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 初始化Kafka生产者
 *
 * <AUTHOR>
 * @version 1.0
 * @description 初始化Kafka生产者
 * @projectName jettong-mq-starter
 * @package com.jettech.basic.mq.kafka.config
 * @className InitKafkaProducer
 * @date 2022/12/7 11:53
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */

@Slf4j
@Component
@Order(value = 11)
@RequiredArgsConstructor
public class InitKafkaProducer implements ApplicationListener<ApplicationReadyEvent>
{


    /**
     * 存放所有生产者
     */
    public static Map<String, KafkaTemplate<String, String>> producerMap = new HashMap<>();

    private final KafkaProducerConfig kafkaProducerConfig;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent)
    {
        log.info("正在加载Kafka生产者---------------------------------------");
        List<KafkaProducerVo> producerlist = kafkaProducerConfig.getProducerlist();
        // 初始化生产者
        if (CollectionUtils.isEmpty(producerlist))
        {
            log.info("无Kafka生产者---------------------------------------");
            return;
        }
        producerlist.forEach(producer ->
        {
            KafkaTemplate<String, String> kafkaTemplate =
                    new KafkaTemplate<>(new DefaultKafkaProducerFactory<>(getKafkaProducerProperties(producer)));
            producerMap.put(producer.getProducerId(), kafkaTemplate);
            log.info("Kafka生产者,bootstrapServers:{}启动成功", producer.getBootstrapServers());
        });

    }

    /**
     * 获取生产者配置参数
     *
     * @param producer 获取生产者配置参数
     * @return {@link Map<String, Object>} 获取生产者配置参数
     * <AUTHOR>
     * @date 2022/12/7 16:00
     * @update 2022/12/7 16:00
     * @since 1.0
     */
    private Map<String, Object> getKafkaProducerProperties(KafkaProducerVo producer)
    {
        Map<String, Object> props = Maps.newHashMapWithExpectedSize(15);
        // Kafka集群
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, producer.getBootstrapServers());
        // ack
        props.put(ProducerConfig.ACKS_CONFIG, producer.getAcks());
        // 一个批次可以使用的内存大小 缺省16384(16k)
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, producer.getBatchSize());
        // 指定了生产者在发送批次前等待更多消息加入批次的时间,  缺省0  50ms
        props.put(ProducerConfig.LINGER_MS_CONFIG, producer.getLingerMs());
        // 控制生产者发送请求最大大小,默认1M （这个参数和Kafka主机的message.max.bytes 参数有关系）
        props.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, producer.getMaxRequestSize());
        // 生产者内存缓冲区大小
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, producer.getBufferMemory());
        // 重发消息次数
        props.put(ProducerConfig.RETRIES_CONFIG, producer.getRetries());
        // 客户端将等待请求的响应的最大时间 默认30秒
        props.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, producer.getRequestTimeoutMs());
        // 最大阻塞时间，超过则抛出异常 缺省60000ms
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, producer.getMaxBlockMs());
        // 于压缩数据的压缩类型。默认是无压缩 ,none、gzip、snappy
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, producer.getCompressionType());
        // key序列化器
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
                org.apache.kafka.common.serialization.StringSerializer.class);
        // value序列化器
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
                org.apache.kafka.common.serialization.StringSerializer.class);

        //用户密码认证参数
        if (StrUtil.isNotBlank(producer.getSecurityProtocol()))
        {
            props.put("security.protocol", producer.getSecurityProtocol());
        }
        if (StrUtil.isNotBlank(producer.getSaslMechanism()))
        {
            props.put("sasl.mechanism", producer.getSaslMechanism());
        }
        if (StrUtil.isNotBlank(producer.getUserName()) && StrUtil.isNotBlank(producer.getPwd()))
        {
            props.put("sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule required username=\""
                    + producer.getUserName() + "\" password=\"" + producer.getPwd() + "\";");
        }
        return props;
    }

}
