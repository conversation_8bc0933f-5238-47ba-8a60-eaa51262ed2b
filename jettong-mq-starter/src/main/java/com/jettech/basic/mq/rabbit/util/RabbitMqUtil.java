package com.jettech.basic.mq.rabbit.util;

import com.jettech.basic.mq.rabbit.config.InitRabbitMqProducer;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.ConfirmCallback;
import com.rabbitmq.client.ReturnCallback;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.util.CollectionUtils;

/**
 * RabbitMq util
 *
 * <AUTHOR>
 * @version 1.0
 * @description RabbitMq util
 * @projectName jettong-mq-starter
 * @package com.jettech.basic.mq.rabbit.util
 * @className KafkaUtil
 * @date 2022/12/7 18:01
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class RabbitMqUtil
{
    /**
     * 根据生成者唯一标识获取发送实例
     *
     * @param producerId 生成者唯一标识
     * @return RabbitTemplate 生产者实例
     * <AUTHOR>
     * @date 2022/8/5 10:54
     * @update 2022/8/5 10:54
     * @since 1.0
     */
    private static RabbitTemplate getProducer(String producerId)
    {
        if (CollectionUtils.isEmpty(InitRabbitMqProducer.producerMap))
        {
            return null;
        }
        else
        {
            return InitRabbitMqProducer.producerMap.get(producerId);
        }
    }

    /**
     * 发送消息，消息发送确认，Confirm模式
     * 通过实现 ConfirmCallback 接口，消息发送到 Broker 后触发回调，确认消息是否到达 Broker 服务器，也就是只确认是否正确到达 Exchange 中
     *
     * @param producerId 生产者唯一id
     * @param exchange 交换机名称
     * @param queue 队列
     * @param type 交换机类型,对应topic，direct，direct等
     * @param message 消息
     * @param ackCallback 消息正确到达交换机，触发ConfirmCallback回调，返回ack
     * @param nackCallback 消息没有正确到达交换机，触发ConfirmReturnCallback回调，返回nack
     * @param returnCallback 消息没有正确的从交换机路由到队列，设置mandory=true的情况下，触发ReturnCallback回调
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2022/12/10 10:00
     * @update 2022/12/10 10:00
     * @since 1.0
     */
    public static void send(String producerId, String exchange, String type, String queue, byte[] message,
            ConfirmCallback ackCallback, ConfirmCallback nackCallback, ReturnCallback returnCallback)
            throws Exception
    {
        RabbitTemplate rabbitTemplate = getProducer(producerId);
        if (null == rabbitTemplate)
        {
            throw new Exception("未获取到RabbitMq生产者");
        }
        Channel channel = rabbitTemplate.getConnectionFactory().createConnection().createChannel(false);
        channel.confirmSelect();
        // 交换机不存在则自动创建，如果存在则不会覆盖
        channel.exchangeDeclare(exchange, type, false, false, false, null);
        // 队列不存在则会自动创建，如果存在则不会覆盖
        channel.queueDeclare(queue, true, false, true, null);
        // 交换机和队列绑定
        channel.queueBind(queue, exchange, queue);

        channel.basicPublish(exchange, queue, null, message);
        channel.addConfirmListener(ackCallback, nackCallback);
        channel.addReturnListener(returnCallback);
    }

}
