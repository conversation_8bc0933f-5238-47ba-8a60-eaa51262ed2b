package com.jettech.basic.mq.rocket.util;

import com.jettech.basic.mq.rocket.config.InitRocketMqProducer;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.util.CollectionUtils;

/**
 * Mq util
 *
 * <AUTHOR>
 * @version 1.0
 * @description Mq util
 * @projectName jettong-util
 * @package com.jettech.basic.mq.util
 * @className RocketMqUtil
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class RocketMqUtil
{

    /**
     * 根据生成者唯一标识获取发送实例
     *
     * @param producerId 生成者唯一标识
     * @return DefaultMQProducer 生产者实例
     * <AUTHOR>
     * @date 2022/8/5 10:54
     * @update 2022/8/5 10:54
     * @since 1.0
     */
    public static DefaultMQProducer getProducer(String producerId)
    {
        if (CollectionUtils.isEmpty(InitRocketMqProducer.producerMap))
        {
            return null;
        }
        else
        {
            return InitRocketMqProducer.producerMap.get(producerId);
        }
    }

}
