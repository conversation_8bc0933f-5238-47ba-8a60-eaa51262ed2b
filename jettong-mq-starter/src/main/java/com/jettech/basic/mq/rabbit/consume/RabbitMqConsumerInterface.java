package com.jettech.basic.mq.rabbit.consume;

import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Delivery;

/**
 * RabbitMq消费者接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description RabbitMq消费者接口
 * @projectName jettong-util
 * @package com.jettech.basic.mq.rabbit.consume
 * @className RabbitMqConsumerInterface
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface RabbitMqConsumerInterface
{

    /**
     * 接收消息处理
     *
     * @param channel 信道
     * @param consumerTag 消费者tag
     * @param delivery 消息bean
     * <AUTHOR>
     * @date 2022/12/10 14:07
     * @update 2022/12/10 14:07
     * @since 1.0
     */
    void deliverCallback(Channel channel, String consumerTag, Delivery delivery);

    /**
     * 取消消息接收
     *
     * @param channel 信道
     * @param consumerTag 消费者tag
     * @return
     * @throws
     * <AUTHOR>
     * @date 2022/12/10 14:10
     * @update 2022/12/10 14:10
     * @since 1.0
     */
    void cancelCallback(Channel channel, String consumerTag);
}
