package com.jettech.basic.mq.kafka.vo;

import lombok.Data;

import java.util.List;


/**
 * Kafka消费者配置VO对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description Kafka消费者配置VO对象
 * @projectName jettong-util
 * @package com.jettech.basic.mq.rocket.vo
 * @className KafkaConsumerVo
 * @date 2022/12/7 11:53
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class KafkaConsumerVo
{

    /**
     * kafka的groupId
     */
    private String groupId;

    /**
     * kafka的bootstrapServers地址
     */
    private String bootstrapServers;

    /**
     * 是否开启批量消费
     */
    private Boolean batchListener = false;

    /**
     * 并发数
     */
    private Integer concurrency;

    /**
     * 开启自动提交,默认false
     */
    private Boolean enableAutoCommit = false;

    /**
     * 设置自动提交间隔毫秒,enableAutoCommit为true时使用
     */
    private Integer autoCommitIntervalMs;

    /**
     * 一次获取最大记录数
     */
    private Integer maxPollRecords;

    /**
     * 获取记录超时时间
     */
    private Integer maxPollTimeOut;

    /**
     * 最小获取消息字节数
     */
    private Integer fetchMinBytes;

    /**
     * 最大的等待获取消息时间（最多多久就要获取一次）与fetch-min-bytes满足一个条件，broker就会发送消息给consumer
     */
    private Integer fetchMaxWaitMs;

    /**
     * group coordinator检测consumer发生崩溃所需的时间
     */
    private Integer sessionTimeOutMs;

    /**
     * 轮询策略org.apache.kafka.clients.consumer.RangeAssignor 和 org.apache.kafka.clients.consumer.RoundRobinAssignor
     */
    private String partitionAssignmentStrategy;

    /**
     * 加密协议（和认证集群搭建时候的配置内容对应）
     */
    private String securityProtocol;

    /**
     * 加密方式（和认证集群搭建时候的配置内容对应）
     */
    private String saslMechanism;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String pwd;

    /**
     * 监听集合
     */
    private List<String> topic;


}
