package com.jettech.basic.mq.kafka.consume;

import org.apache.kafka.clients.consumer.ConsumerRecords;

/**
 * kafka消费者接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description kafka消费者接口
 * @projectName jettong-util
 * @package com.jettech.basic.mq.kafka.consume
 * @className KafkaConsumerInterface
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface KafkaConsumerInterface
{

    /**
     * 实际消费者继承该类
     *
     * @param consumerRecords 消息
     * <AUTHOR>
     * @date 2022/8/5 11:05
     * @update 2022/8/5 11:05
     * @since 1.0
     */
    void handle(ConsumerRecords<String, String> consumerRecords);

}
