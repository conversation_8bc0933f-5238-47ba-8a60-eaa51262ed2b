package com.jettech.basic.mq.rocket.config;

import cn.hutool.core.util.StrUtil;
import com.jettech.basic.mq.rocket.vo.RocketMqProducerVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.acl.common.AclClientRPCHook;
import org.apache.rocketmq.acl.common.SessionCredentials;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 初始化RocketMq生产者
 *
 * <AUTHOR>
 * @version 1.0
 * @description 初始化RocketMq生产者
 * @projectName jettong-util
 * @package com.jettech.basic.mq.config
 * @className InitRocketMqProducer
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Component
@Order(value = 13)
@RequiredArgsConstructor
public class InitRocketMqProducer implements ApplicationListener<ApplicationReadyEvent>
{

    /**
     * 存放所有生产者
     */
    public static Map<String, DefaultMQProducer> producerMap = new HashMap<>();
    private final RocketMqProducerConfig rocketMqProducerConfig;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent)
    {
        log.info("正在创建RocketMq生产者---------------------------------------");
        List<RocketMqProducerVo> producerlist = rocketMqProducerConfig.getProducerlist();
        if (null == producerlist || CollectionUtils.isEmpty(producerlist))
        {
            log.info("无RocketMq生产者---------------------------------------");
            return;
        }

        for (RocketMqProducerVo vo : producerlist)
        {
            try
            {
                DefaultMQProducer producer;
                if (StrUtil.isNotEmpty(vo.getAccessKey()) && StrUtil.isNotEmpty(vo.getSecretKey()))
                {
                    producer = new DefaultMQProducer(vo.getGroupName(),
                            new AclClientRPCHook(new SessionCredentials(vo.getAccessKey(), vo.getSecretKey())));
                }
                else
                {
                    producer = new DefaultMQProducer(vo.getGroupName());
                }
                producer.setNamesrvAddr(vo.getNamesrvAddr());
                producer.setVipChannelEnabled(false);
                producer.setMaxMessageSize(vo.getMaxMessageSize());
                producer.setSendMsgTimeout(vo.getSendMsgTimeOut());
                producer.setRetryTimesWhenSendAsyncFailed(vo.getRetryTimesWhenSendFailed());

                producer.start();
                producerMap.put(vo.getProducerId(), producer);
                log.info("RocketMq生产者{},{}启动成功", vo.getGroupName(), vo.getNamesrvAddr());
            }
            catch (MQClientException e)
            {
                log.error("RocketMq生产者{},{}启动失败", vo.getGroupName(), vo.getNamesrvAddr(), e);
            }
        }

    }

}
