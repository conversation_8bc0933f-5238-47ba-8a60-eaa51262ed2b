package com.jettech.basic.mq.rocket.vo;

import lombok.Data;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.common.message.MessageExt;

import java.util.List;

/**
 * Mq消费者参数对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description Mq消费者参数对象
 * @projectName jettong-util
 * @package com.jettech.basic.mq.rocket.vo
 * @className RocketMqConsumerParam
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class RocketMqConsumerParam
{

    /**
     * 消息
     */
    private List<MessageExt> list;

    /**
     * 非顺序消费上下文
     */
    private ConsumeConcurrentlyContext consumeConcurrentlyContext;

    /**
     * 顺序消费上下文
     */
    private ConsumeOrderlyContext consumeOrderlyContext;

    public RocketMqConsumerParam(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext,
            ConsumeOrderlyContext consumeOrderlyContext)
    {
        this.list = list;
        this.consumeConcurrentlyContext = consumeConcurrentlyContext;
        this.consumeOrderlyContext = consumeOrderlyContext;
    }

}
