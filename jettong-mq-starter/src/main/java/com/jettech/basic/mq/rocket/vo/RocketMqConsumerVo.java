package com.jettech.basic.mq.rocket.vo;

import lombok.Data;

import java.util.List;

/**
 * Mq消费者配置VO对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description Mq消费者配置VO对象
 * @projectName jettong-util
 * @package com.jettech.basic.mq.rocket.vo
 * @className RocketMqConsumerVo
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class RocketMqConsumerVo
{

    /**
     * mq的groupName
     */
    private String groupName;

    /**
     * mq的nameserver地址
     */
    private String namesrvAddr;

    /**
     * 监听集合
     */
    private List<RocketMqTopicVo> topics;

    /**
     * 消费者线程数据量
     */
    private Integer consumeThreadMin = 2;
    private Integer consumeThreadMax = 4;

    /**
     * 设置一次消费的条数，默认1
     */
    private Integer consumeMessageBatchMaxSize = 1;

    /**
     * 是否顺序消费
     */
    private Boolean orderly;

    /**
     * 消费模式
     */
    private String messageModel;

    /**
     * access-key
     */
    private String accessKey;

    /**
     * secret-key
     */
    private String secretKey;
}
