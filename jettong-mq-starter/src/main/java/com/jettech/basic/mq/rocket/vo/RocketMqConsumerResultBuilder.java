package com.jettech.basic.mq.rocket.vo;

/**
 * Mq消费者返回Builder对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description Mq消费者返回Builder对象
 * @projectName jettong-util
 * @package com.jettech.basic.mq.rocket.vo
 * @className RocketMqConsumerResultBuilder
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class RocketMqConsumerResultBuilder
{

    private boolean success = false;

    private boolean reconsumeLater = true;

    private boolean saveConsumeLog = false;

    private String errMsg;

    private Throwable throwable;

    public static RocketMqConsumerResult success()
    {
        return new RocketMqConsumerResultBuilder().success(true).build();
    }

    public RocketMqConsumerResultBuilder success(boolean success)
    {
        this.success = success;
        return this;
    }

    public RocketMqConsumerResultBuilder reconsumeLater(boolean reconsumeLater)
    {
        this.reconsumeLater = reconsumeLater;
        return this;
    }

    public RocketMqConsumerResultBuilder saveConsumeLog(boolean saveConsumeLog)
    {
        this.saveConsumeLog = saveConsumeLog;
        return this;
    }

    public RocketMqConsumerResultBuilder errMsg(String errMsg)
    {
        this.errMsg = errMsg;
        return this;
    }

    public RocketMqConsumerResultBuilder throwable(Throwable throwable)
    {
        this.throwable = throwable;
        return this;
    }

    public RocketMqConsumerResult build()
    {
        return new RocketMqConsumerResult(this.success, this.reconsumeLater, this.saveConsumeLog, this.errMsg,
                this.throwable);
    }

}
