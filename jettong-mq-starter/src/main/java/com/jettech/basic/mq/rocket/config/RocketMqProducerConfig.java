package com.jettech.basic.mq.rocket.config;

import com.jettech.basic.mq.rocket.vo.RocketMqProducerVo;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * RocketMq 生产者配置
 *
 * <AUTHOR>
 * @version 1.0
 * @description RocketMq 生产者配置
 * @projectName jettong-util
 * @package com.jettech.basic.mq.config
 * @className RocketMqProducerConfig
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
@Configuration
@ConfigurationProperties(prefix = "rocketmq.producer")
public class RocketMqProducerConfig
{

    private List<RocketMqProducerVo> producerlist;

    public List<RocketMqProducerVo> getProducerlist()
    {
        return producerlist;
    }

    public void setProducerlist(List<RocketMqProducerVo> producerlist)
    {
        this.producerlist = producerlist;
    }
}
