package com.jettech.basic.mq.rocket.consume;

import com.jettech.basic.mq.rocket.vo.RocketMqConsumerParam;
import com.jettech.basic.mq.rocket.vo.RocketMqConsumerResult;

/**
 * Mq消费者接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description Mq消费者接口
 * @projectName jettong-util
 * @package com.jettech.basic.mq.rocket.consume
 * @className RocketMqConsumerInterface
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface RocketMqConsumerInterface
{

    /**
     * 实际消费者继承该类
     *
     * @param param 参数
     * @return RocketMqConsumerResult 消费结果
     * <AUTHOR>
     * @date 2022/8/5 11:05
     * @update 2022/8/5 11:05
     * @since 1.0
     */
    RocketMqConsumerResult handle(RocketMqConsumerParam param);

}
