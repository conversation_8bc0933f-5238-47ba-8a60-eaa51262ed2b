package com.jettech.basic.mq.rabbit.config;

import com.jettech.basic.mq.rabbit.consume.RabbitMqConsumerInterface;
import com.jettech.basic.mq.rabbit.vo.RabbitMqConsumerVo;
import com.jettech.basic.mq.util.SpringBeanUtil;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;

/**
 * 初始化RabbitMq消费者
 *
 * <AUTHOR>
 * @version 1.0
 * @description 初始化RabbitMq消费者
 * @projectName jettong-mq-starter
 * @package com.jettech.basic.mq.rabbit.config
 * @className InitRabbitMqConsumer
 * @date 2022/12/7 11:52
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */

@Slf4j
@Component
@Order(value = 10)
@RequiredArgsConstructor
public class InitRabbitMqConsumer implements ApplicationListener<ApplicationReadyEvent>
{
    private final RabbitMqConsumerConfig rabbitMqConsumerConfig;

    private final List<Channel> startConsumer = new ArrayList<>();

    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent)
    {
        log.info("正在加载RabbitMq消费者---------------------------------------");
        List<RabbitMqConsumerVo> consumerlist = rabbitMqConsumerConfig.getConsumerlist();
        // 初始化消费者
        if (CollectionUtils.isEmpty(consumerlist))
        {
            log.info("无RabbitMq消费者---------------------------------------");
            return;
        }
        consumerlist.forEach(consumer ->
        {
            // 队列名
            String queueName = consumer.getQueueName();
            // 获取消费者工厂
            ConnectionFactory connectionFactory = getRabbitMqConnectionFactory(consumer);
            try
            {
                Connection connection = connectionFactory.newConnection();
                Channel channel = connection.createChannel();
                //信道设置,如果在Rabbit管理工具中创建了队列，则不需要调用此方法
                channel.queueDeclare(queueName, true, false, true, null);
                // 获取对应实际处理类
                RabbitMqConsumerInterface rabbitMqConsumerInterface =
                        SpringBeanUtil.getBean("rabbitMqConsumerHandle." + queueName, RabbitMqConsumerInterface.class);
                if (null == rabbitMqConsumerInterface)
                {
                    log.info("RabbitMq消费者:{}没有实现类", queueName);
                    return;
                }
                /**
                 * 消费者消费消息
                 * 1.消费哪个队列
                 * 2.消费成功之后是否要自动应答 true 代表的自动应答 false 代表手动应答
                 * 3.当消息传达到后(成功之后)的回调
                 * 4.消费者取消消费的回调
                 */
                channel.basicConsume(queueName, false,
                        (s, delivery) -> rabbitMqConsumerInterface.deliverCallback(channel, s, delivery), s ->
                                rabbitMqConsumerInterface.cancelCallback(channel, s));
                startConsumer.add(channel);

                log.info("RabbitMq消费者:{}启动成功", queueName);
            }
            catch (Exception e)
            {
                log.error("RabbitMq消费者:{}启动失败", queueName);
            }
        });

    }

    /**
     * 获取消费者工厂
     *
     * @param consumer 获取消费者配置参数
     * @return {@link ConnectionFactory} 消费者连接工厂
     * <AUTHOR>
     * @date 2022/12/7 16:00
     * @update 2022/12/7 16:00
     * @since 1.0
     */
    private ConnectionFactory getRabbitMqConnectionFactory(RabbitMqConsumerVo consumer)
    {
        ConnectionFactory factory = new ConnectionFactory();

        // 设置服务器地址
        factory.setHost(consumer.getHost());
        // 设置服务器端口
        factory.setPort(consumer.getPort());
        // 设置虚拟主机
        factory.setVirtualHost(consumer.getVirtualHost());
        // 设置用户名
        factory.setUsername(consumer.getUserName());
        // 设置密码
        factory.setPassword(consumer.getPassword());

        return factory;
    }


    @PreDestroy
    public void preDestroy()
    {
        log.info("正在关闭RabbitMq连接");
        startConsumer.forEach(consumer ->
        {
            try
            {
                consumer.close();
                consumer.getConnection().close();
                log.info("RabbitMq消费者关闭连接成功");
            }
            catch (Exception e)
            {
                log.error("RabbitMq消费者关闭连接成功", e);
            }
        });
    }
}
