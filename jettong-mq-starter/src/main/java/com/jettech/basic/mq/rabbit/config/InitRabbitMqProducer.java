package com.jettech.basic.mq.rabbit.config;

import com.jettech.basic.mq.rabbit.vo.RabbitMqProducerVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.annotation.Order;
import org.springframework.retry.support.RetryTemplateBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 初始化RabbitMq生产者
 *
 * <AUTHOR>
 * @version 1.0
 * @description 初始化RabbitMq生产者
 * @projectName jettong-mq-starter
 * @package com.jettech.basic.mq.rabbit.config
 * @className InitRabbitMqProducer
 * @date 2022/12/7 11:53
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */

@Slf4j
@Component
@Order(value = 9)
@RequiredArgsConstructor
public class InitRabbitMqProducer implements ApplicationListener<ApplicationReadyEvent>
{


    /**
     * 存放所有生产者
     */
    public static Map<String, RabbitTemplate> producerMap = new HashMap<>();

    private final RabbitMqProducerConfig rabbitMqProducerConfig;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent)
    {
        log.info("正在加载RabbitMq生产者---------------------------------------");
        List<RabbitMqProducerVo> producerlist = rabbitMqProducerConfig.getProducerlist();
        // 初始化生产者
        if (CollectionUtils.isEmpty(producerlist))
        {
            log.info("无RabbitMq生产者---------------------------------------");
            return;
        }
        producerlist.forEach(producer ->
        {
            CachingConnectionFactory factory = getRabbitMqConnectionFactory(producer);
            RabbitTemplate template = new RabbitTemplate(getRabbitMqConnectionFactory(producer));
            template.setMandatory(producer.getPublisherReturns());
            template.setMessageConverter(new Jackson2JsonMessageConverter());

            template.setReplyTimeout(producer.getReplyTimeout());
            if (producer.getRetryEnabled())
            {
                template.setRetryTemplate(new RetryTemplateBuilder().maxAttempts(producer.getRetryMaxAttempts())
                        .exponentialBackoff(producer.getInitialInterval(), producer.getRetryMultiplier(),
                                producer.getRetryMaxInterval())
                        .build());
            }

            producerMap.put(producer.getProducerId(), template);

            log.info("RabbitMq生产者,producerId:{}启动成功-------------------", producer.getProducerId());
        });

    }

    /**
     * 获取生产者工厂
     *
     * @param producer 获取生产者配置参数
     * @return {@link CachingConnectionFactory} 生产者连接工厂
     * <AUTHOR>
     * @date 2022/12/7 16:00
     * @update 2022/12/7 16:00
     * @since 1.0
     */
    private CachingConnectionFactory getRabbitMqConnectionFactory(RabbitMqProducerVo producer)
    {
        CachingConnectionFactory factory = new CachingConnectionFactory();

        // rabbit mq集群配置，指定client连接到的server的地址，多个以逗号分隔(优先取addresses，然后再取host)
        factory.setAddresses(producer.getAddresses());
        // 设置服务器地址
        factory.setHost(producer.getHost());
        // 设置服务器端口
        factory.setPort(producer.getPort());
        // 设置虚拟主机
        factory.setVirtualHost(producer.getVirtualHost());
        // 设置用户名
        factory.setUsername(producer.getUserName());
        // 设置密码
        factory.setPassword(producer.getPassword());
        // 发布确认方式
        factory.setPublisherConfirmType(producer.getPublisherConfirmType());
        // 是否启用发布返回
        factory.setPublisherReturns(producer.getPublisherReturns());
        // 当前最大允许空闲的最大channel数
        factory.setChannelCacheSize(producer.getChannelCacheSize());
        // 当缓存数量被设置时，从缓存中获取一个channel的超时时间，单位毫秒；如果为0，则总是创建一个新channel
        factory.setChannelCheckoutTimeout(producer.getChannelCheckoutTimeout());
        // 设置连接工厂缓存模式：
        factory.setCacheMode(producer.getCacheMode());
        // 缓存连接数
        factory.setConnectionCacheSize(producer.getConnectionCacheSize());
        // 连接超时，单位毫秒，0表示无穷大，不超时
        factory.setConnectionTimeout(producer.getConnectionTimeout());
        // 设置连接限制
        factory.setConnectionLimit(producer.getConnectionLimit());

        return factory;
    }

}
