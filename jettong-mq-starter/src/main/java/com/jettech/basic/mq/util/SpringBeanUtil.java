package com.jettech.basic.mq.util;


import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Spring Bena Util
 *
 * <AUTHOR>
 * @version 1.0
 * @description Spring Bena Util
 * @projectName jettong-util
 * @package com.jettech.basic.mq.util
 * @className SpringBeanUtil
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class SpringBeanUtil implements ApplicationContextAware
{

    private static ApplicationContext applicationContext = null;

    /**
     * 根据beanName返回相应的对象
     *
     * @param beanName beanName
     * @return T Spring bean 对象
     * <AUTHOR>
     * @date 2022/8/5 10:56
     * @update 2022/8/5 10:56
     * @since 1.0
     */
    public static <T> T getBeanByName(String beanName)
    {
        if (applicationContext.containsBean(beanName))
        {
            return (T) applicationContext.getBean(beanName);
        }
        else
        {
            return null;
        }
    }

    /**
     * 根据bean的类型返回一个对象
     *
     * @param type bean 类型
     * @return T Spring bean 对象
     * <AUTHOR>
     * @date 2022/8/5 10:56
     * @update 2022/8/5 10:56
     * @since 1.0
     */
    public static <T> T getBean(Class<T> type)
    {
        return applicationContext.getBean(type);
    }

    /**
     * 根据bean的类型返回对象
     *
     * @param baseType bean的类型
     * @return Map<String, T> Spring bean 对象
     * <AUTHOR>
     * @date 2022/8/5 10:57
     * @update 2022/8/5 10:57
     * @since 1.0
     */
    public static <T> Map<String, T> getBeansOfType(Class<T> baseType)
    {
        return applicationContext.getBeansOfType(baseType);
    }

    /**
     * 根据bean的类型和name返回一个对象
     *
     * @param name bean name
     * @param requiredType bean的类型
     * @return T Spring bean 对象
     * <AUTHOR>
     * @date 2022/8/5 10:57
     * @update 2022/8/5 10:57
     * @since 1.0
     */
    public static <T> T getBean(String name, Class<T> requiredType)
    {
        if (applicationContext.containsBean(name))
        {
            return applicationContext.getBean(name, requiredType);
        }
        else
        {
            return null;
        }
    }

    /**
     * 实现接口方法
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException
    {
        SpringBeanUtil.applicationContext = applicationContext;
    }
}
