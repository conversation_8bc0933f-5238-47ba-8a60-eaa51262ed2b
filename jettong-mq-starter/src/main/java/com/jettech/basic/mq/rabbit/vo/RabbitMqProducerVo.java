package com.jettech.basic.mq.rabbit.vo;

import lombok.Data;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;

/**
 * RabbitMq生产者配置VO对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description RabbitMq生产者配置VO对象
 * @projectName jettong-util
 * @package com.jettech.basic.mq.kafka.vo
 * @className KafkaProducerVo
 * @date 2022/12/7 11:53
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class RabbitMqProducerVo
{

    /**
     * 生产者唯一编号
     */
    private String producerId;

    /**
     * rabbit mq集群配置，指定client连接到的server的地址，多个以逗号分隔(优先取addresses，然后再取host)
     */
    private String addresses;

    /**
     * rabbit mq host
     */
    private String host;

    /**
     * rabbit mq port
     */
    private Integer port;

    /**
     * rabbit mq 虚拟主机
     */
    private String virtualHost;

    /**
     * rabbit mq 连接用户名
     */
    private String userName;

    /**
     * rabbit mq 连接密码
     */
    private String password;

    /**
     * 当前最大允许空闲的最大channel数
     */
    private Integer channelCacheSize;

    /**
     * 发布确认方式 SIMPLE CORRELATED NONE 默认为NONE不启用
     */
    private CachingConnectionFactory.ConfirmType publisherConfirmType = CachingConnectionFactory.ConfirmType.NONE;

    /**
     * 是否启用发布返回
     */
    private Boolean publisherReturns;

    /**
     * 当缓存数量被设置时，从缓存中获取一个channel的超时时间，单位毫秒；如果为0，则总是创建一个新channel
     */
    private Long channelCheckoutTimeout;

    /**
     * 设置连接工厂缓存模式, CHANNEL(缓存通道-单连接) CONNECTION(缓存每个连接中的连接和通道)
     */
    private CachingConnectionFactory.CacheMode cacheMode;

    /**
     * 缓存连接数
     */
    private Integer connectionCacheSize;

    /**
     * 连接超时，单位毫秒，0表示无穷大，不超时
     */
    private Integer connectionTimeout;

    /**
     * 设置连接限制
     */
    private Integer connectionLimit;

    /**
     * 启用强制信息；默认false
     */
    private Boolean mandatory;

    /**
     * sendAndReceive() 操作的超时时间
     */
    private Integer replyTimeout;

    /**
     * 发送重试是否可用,默认false
     */
    private Boolean retryEnabled = false;

    /**
     * 最大重试次数
     */
    private Integer retryMaxAttempts;

    /**
     * 第一次和第二次尝试发布或传递消息之间的间隔 毫秒
     */
    private Integer initialInterval;

    /**
     * 应用于上一重试间隔的乘数
     */
    private Double retryMultiplier;

    /**
     * 最大重试间隔时间
     */
    private Integer retryMaxInterval;
}
