package com.jettech.basic.mq.rocket.vo;

import lombok.Data;

/**
 * Mq生产者配置VO对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description Mq生产者配置VO对象
 * @projectName jettong-util
 * @package com.jettech.basic.mq.rocket.vo
 * @className RocketMqProducerVo
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class RocketMqProducerVo
{

    /**
     * 生产者唯一编号
     */
    private String producerId;

    /**
     * group
     */
    private String groupName;

    /**
     * MQ注册中心
     */
    private String namesrvAddr;

    /**
     * 消息最大值
     */
    private Integer maxMessageSize;

    /**
     * 消息发送超时时间
     */
    private Integer sendMsgTimeOut;

    /**
     * 失败重试次数
     */
    private Integer retryTimesWhenSendFailed;

    /**
     * access-key
     */
    private String accessKey;

    /**
     * secret-key
     */
    private String secretKey;
}
