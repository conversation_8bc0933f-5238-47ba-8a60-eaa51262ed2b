package com.jettech.basic.mq.rocket.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * Mq消费者返回对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description Mq消费者返回对象
 * @projectName jettong-util
 * @package com.jettech.basic.mq.rocket.vo
 * @className RocketMqConsumerResult
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class RocketMqConsumerResult implements Serializable
{

    /**
     * 是否处理成功
     */
    private boolean success = false;
    /**
     * 如果处理失败，是否允许消息队列继续调用，直到处理成功，默认true
     */
    private boolean reconsumeLater = true;
    /**
     * 是否需要记录消费日志，默认不记录
     */
    private boolean saveConsumeLog = false;

    /**
     * 错误消息
     */
    private String errMsg;
    /**
     * 错误堆栈
     */
    private Throwable e;

    public RocketMqConsumerResult(boolean success, boolean reconsumeLater, boolean saveConsumeLog, String errMsg,
            Throwable e)
    {
        this.success = success;
        this.reconsumeLater = reconsumeLater;
        this.saveConsumeLog = saveConsumeLog;
        this.errMsg = errMsg;
        this.e = e;
    }

    public boolean isSuccess()
    {
        return success;
    }

    public boolean isReconsumeLater()
    {
        return reconsumeLater;
    }

    public boolean isSaveConsumeLog()
    {
        return saveConsumeLog;
    }

}
