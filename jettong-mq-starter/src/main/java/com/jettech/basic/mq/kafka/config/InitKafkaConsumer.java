package com.jettech.basic.mq.kafka.config;

import cn.hutool.core.util.StrUtil;
import com.jettech.basic.mq.kafka.consume.KafkaConsumerInterface;
import com.jettech.basic.mq.kafka.vo.KafkaConsumerVo;
import com.jettech.basic.mq.util.SpringBeanUtil;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PreDestroy;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 初始化Kafka消费者
 *
 * <AUTHOR>
 * @version 1.0
 * @description 初始化Kafka消费者
 * @projectName jettong-mq-starter
 * @package com.jettech.basic.mq.kafka.config
 * @className InitKafkaConsumer
 * @date 2022/12/7 11:52
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */

@Slf4j
@Component
@Order(value = 12)
@RequiredArgsConstructor
public class InitKafkaConsumer implements ApplicationListener<ApplicationReadyEvent>
{
    private final KafkaConsumerConfig kafkaConsumerConfig;

    private List<KafkaConsumer<String, String>> startConsumer = new ArrayList<>();

    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent)
    {
        log.info("正在加载Kafka消费者---------------------------------------");
        List<KafkaConsumerVo> consumerlist = kafkaConsumerConfig.getConsumerlist();
        // 初始化消费者
        if (CollectionUtils.isEmpty(consumerlist))
        {
            log.info("无Kafka消费者---------------------------------------");
            return;
        }
        consumerlist.forEach(consumer ->
        {
            List<String> topics = consumer.getTopic();
            if (topics.isEmpty())
            {
                log.info("未获取到topic,请检查代码");
                return;
            }
            KafkaConsumer<String, String> kafkaConsumer = new KafkaConsumer<>(getKafkaConsumerProperties(consumer));
            kafkaConsumer.subscribe(topics);

            String topicName = topics.get(0);

            // 获取对应实际处理类
            KafkaConsumerInterface kafkaConsumerInterface =
                    SpringBeanUtil.getBean("kafkaConsumerHandle." + topicName, KafkaConsumerInterface.class);

            if (kafkaConsumerInterface == null)
            {
                log.info("未根据topic:{}找到对应处理类,请检查代码", topicName);
                return;
            }
            log.info("Kafka消费者groupId:{},bootstrapServers:{}启动成功", consumer.getGroupId(),
                    consumer.getBootstrapServers());
            startConsumer.add(kafkaConsumer);

            while (true)
            {
                ConsumerRecords<String, String> consumerRecords = kafkaConsumer.poll(Duration.ofSeconds(2));
                if (!consumerRecords.isEmpty())
                {
                    kafkaConsumerInterface.handle(consumerRecords);
                }
            }
        });

    }

    /**
     * 获取消费者配置参数
     *
     * @param consumer 消费者配置对象
     * @return {@link Map<String, Object>} 消费者配置对象
     * <AUTHOR>
     * @date 2022/12/7 16:00
     * @update 2022/12/7 16:00
     * @since 1.0
     */
    private Map<String, Object> getKafkaConsumerProperties(KafkaConsumerVo consumer)
    {
        Map<String, Object> props = Maps.newHashMapWithExpectedSize(15);
        // Kafka集群
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, consumer.getBootstrapServers());
        // 消费者组，只要group.id相同，就属于同一个消费者组
        props.put(ConsumerConfig.GROUP_ID_CONFIG, consumer.getGroupId());
        // 开启自动提交
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, consumer.getEnableAutoCommit());
        // 设置自动提交间隔毫秒,enableAutoCommit为true时使用
        props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, consumer.getAutoCommitIntervalMs());
        // key反序列化器
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG,
                org.apache.kafka.common.serialization.StringDeserializer.class);
        // value反序列化器
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,
                org.apache.kafka.common.serialization.StringDeserializer.class);
        // 一次获取最大记录数
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, consumer.getMaxPollRecords());
        // 最小获取消息字节数
        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, consumer.getFetchMinBytes());
        // 最大的等待获取消息时间（最多多久就要获取一次）与fetch-min-bytes满足一个条件，broker就会发送消息给consumer
        props.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, consumer.getFetchMaxWaitMs());
        // 获取记录超时时间
        props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, consumer.getMaxPollTimeOut());
        // group coordinator检测consumer发生崩溃所需的时间
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, consumer.getSessionTimeOutMs());
        // 轮询策略org.apache.kafka.clients.consumer.RangeAssignor 和 org.apache.kafka.clients.consumer.RoundRobinAssignor
        props.put(ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG, consumer.getPartitionAssignmentStrategy());

        //用户密码认证参数
        if (StrUtil.isNotBlank(consumer.getSecurityProtocol()))
        {
            props.put("security.protocol", consumer.getSecurityProtocol());
        }
        if (StrUtil.isNotBlank(consumer.getSaslMechanism()))
        {
            props.put("sasl.mechanism", consumer.getSaslMechanism());
        }
        if (StrUtil.isNotBlank(consumer.getUserName()) && StrUtil.isNotBlank(consumer.getPwd()))
        {
            props.put("sasl.jaas.config", "org.apache.kafka.common.security.scram.ScramLoginModule required username=\""
                    + consumer.getUserName() + "\" password=\"" + consumer.getPwd() + "\";");
        }
        return props;
    }


    @PreDestroy
    public void preDestroy()
    {
        log.info("正在关闭Kafka连接");
        startConsumer.forEach(consumer ->
        {
            try
            {
                consumer.close();
                log.info("Kafka消费者groupid:" + consumer.groupMetadata().groupId() + "关闭连接成功");
            }
            catch (Exception e)
            {
                log.error("RocketMq消费者group:" + consumer.groupMetadata().groupId() + "关闭连接失败", e);
            }
        });
    }
}
