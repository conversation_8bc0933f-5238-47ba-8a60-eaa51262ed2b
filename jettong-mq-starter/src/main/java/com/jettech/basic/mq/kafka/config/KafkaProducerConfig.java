package com.jettech.basic.mq.kafka.config;

import com.jettech.basic.mq.kafka.vo.KafkaProducerVo;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Kafka 生产者配置
 *
 * <AUTHOR>
 * @version 1.0
 * @description Kafka 生产者配置
 * @projectName jettong-util
 * @package com.jettech.basic.mq.kafka.config
 * @className KafkaProducerConfig
 * @date 2022/12/7 11:53
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
@Configuration
@ConfigurationProperties(prefix = "kafka.producer")
public class KafkaProducerConfig
{

    private List<KafkaProducerVo> producerlist;

    public List<KafkaProducerVo> getProducerlist()
    {
        return producerlist;
    }

    public void setProducerlist(List<KafkaProducerVo> producerlist)
    {
        this.producerlist = producerlist;
    }
}
