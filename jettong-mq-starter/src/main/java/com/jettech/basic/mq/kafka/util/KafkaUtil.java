package com.jettech.basic.mq.kafka.util;

import com.jettech.basic.mq.kafka.config.InitKafkaProducer;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.CollectionUtils;
import org.springframework.util.concurrent.ListenableFutureCallback;

/**
 * kafka util
 *
 * <AUTHOR>
 * @version 1.0
 * @description kafka util
 * @projectName jettong-mq-starter
 * @package com.jettech.basic.mq.kafka.util
 * @className KafkaUtil
 * @date 2022/12/7 18:01
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class KafkaUtil
{
    /**
     * 根据生成者唯一标识获取发送实例
     *
     * @param producerId 生成者唯一标识
     * @return KafkaTemplate<String, String> 生产者实例
     * <AUTHOR>
     * @date 2022/8/5 10:54
     * @update 2022/8/5 10:54
     * @since 1.0
     */
    private static KafkaTemplate<String, String> getProducer(String producerId)
    {
        if (CollectionUtils.isEmpty(InitKafkaProducer.producerMap))
        {
            return null;
        }
        else
        {
            return InitKafkaProducer.producerMap.get(producerId);
        }
    }

    /**
     * 发送消息
     *
     * @param producerId 生产者唯一标识
     * @param topic topic
     * @param message 消息
     * @param callback 回调函数
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2022/12/10 15:21
     * @update 2022/12/10 15:21
     * @since 1.0
     */
    public static void send(String producerId, String topic, String message,
            ListenableFutureCallback<SendResult<String, String>> callback) throws Exception
    {
        KafkaTemplate<String, String> producer = getProducer(producerId);
        if (null == producer)
        {
            throw new Exception("未获取到Kafka生产者");
        }

        producer.send(topic, message).addCallback(callback);
    }
}
