package com.jettech.basic.mq.rabbit.config;

import com.jettech.basic.mq.rabbit.vo.RabbitMqConsumerVo;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * RabbitMq 消费者配置
 *
 * <AUTHOR>
 * @version 1.0
 * @description RabbitMq 消费者配置
 * @projectName jettong-util
 * @package com.jettech.basic.mq.rabbit.config
 * @className RabbitMqConsumerConfig
 * @date 2022/12/7 11:53
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
@Configuration
@ConfigurationProperties(prefix = "rabbitmq.consumer")
public class RabbitMqConsumerConfig
{

    private List<RabbitMqConsumerVo> consumerlist;

    public List<RabbitMqConsumerVo> getConsumerlist()
    {
        return consumerlist;
    }

    public void setConsumerlist(List<RabbitMqConsumerVo> consumerlist)
    {
        this.consumerlist = consumerlist;
    }
}
