package com.jettech.basic.mq.rabbit.config;

import com.jettech.basic.mq.rabbit.vo.RabbitMqProducerVo;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * RabbitMq 生产者配置
 *
 * <AUTHOR>
 * @version 1.0
 * @description RabbitMq 生产者配置
 * @projectName jettong-util
 * @package com.jettech.basic.mq.rabbit.config
 * @className RabbitMqProducerConfig
 * @date 2022/12/7 11:53
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
@Configuration
@ConfigurationProperties(prefix = "rabbitmq.producer")
public class RabbitMqProducerConfig
{

    private List<RabbitMqProducerVo> producerlist;

    public List<RabbitMqProducerVo> getProducerlist()
    {
        return producerlist;
    }

    public void setProducerlist(List<RabbitMqProducerVo> producerlist)
    {
        this.producerlist = producerlist;
    }
}
