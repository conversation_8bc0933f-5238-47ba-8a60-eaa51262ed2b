package com.jettech.basic.mq.rocket.config;

import cn.hutool.core.util.StrUtil;
import com.jettech.basic.mq.rocket.consume.RocketMqConsumerInterface;
import com.jettech.basic.mq.rocket.vo.RocketMqConsumerParamBuilder;
import com.jettech.basic.mq.rocket.vo.RocketMqConsumerResult;
import com.jettech.basic.mq.rocket.vo.RocketMqConsumerVo;
import com.jettech.basic.mq.rocket.vo.RocketMqTopicVo;
import com.jettech.basic.mq.util.SpringBeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.acl.common.AclClientRPCHook;
import org.apache.rocketmq.acl.common.SessionCredentials;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.client.consumer.rebalance.AllocateMessageQueueAveragely;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;

/**
 * 初始化RocketMq消费者
 *
 * <AUTHOR>
 * @version 1.0
 * @description 初始化RocketMq消费者
 * @projectName jettong-util
 * @package com.jettech.basic.mq.config
 * @className InitRocketMqConsumer
 * @date 2022/08/05 12:45
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Component
@Order(value = 14)
@RequiredArgsConstructor
public class InitRocketMqConsumer implements ApplicationListener<ApplicationReadyEvent>
{

    private final RocketMqConsumerConfig rocketMqConsumerConfig;

    private List<DefaultMQPushConsumer> startConsumer = new ArrayList<>();

    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent)
    {
        log.info("正在创建RocketMq消费者---------------------------------------");
        List<RocketMqConsumerVo> consumerlist = rocketMqConsumerConfig.getConsumerlist();
        // 初始化消费者
        if (CollectionUtils.isEmpty(consumerlist))
        {
            log.info("无RocketMq消费者---------------------------------------");
            return;
        }

        consumerlist.forEach(consumer ->
        {
            // 此步可以针对配置参数进行校验，校验consumer格式，符合规则才启动
            DefaultMQPushConsumer start;
            if (StrUtil.isNotEmpty(consumer.getAccessKey()) && StrUtil.isNotEmpty(consumer.getSecretKey()))
            {
                start = new DefaultMQPushConsumer(consumer.getGroupName(),
                        new AclClientRPCHook(new SessionCredentials(consumer.getAccessKey(), consumer.getSecretKey())),
                        // 平均分配队列算法，hash
                        new AllocateMessageQueueAveragely());
            }
            else
            {
                start = new DefaultMQPushConsumer(consumer.getGroupName());
            }
            start.setNamesrvAddr(consumer.getNamesrvAddr());
            start.setConsumeThreadMin(consumer.getConsumeThreadMin());
            start.setConsumeThreadMax(consumer.getConsumeThreadMax());
            start.setConsumeMessageBatchMaxSize(consumer.getConsumeMessageBatchMaxSize());
            // 设置消费模型，集群(MessageModel.CLUSTERING)还是广播(MessageModel.BROADCASTING)，默认为集群
            String messageModel = consumer.getMessageModel();
            if (StringUtils.isBlank(messageModel) || StringUtils.equals(MessageModel.CLUSTERING.name(), messageModel))
            {
                start.setMessageModel(MessageModel.CLUSTERING);
            }
            else
            {
                start.setMessageModel(MessageModel.BROADCASTING);
            }
            // 设置consumer第一次启动是从队列头部开始还是队列尾部开始,否则按照上次消费的位置继续消费
            start.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);

            // 设置监听,判断是否为顺序消费
            Boolean orderly = consumer.getOrderly();
            if (orderly == null || orderly)
            {
                // 顺序消费
                start.registerMessageListener((MessageListenerOrderly) (list, consumeOrderlyContext) ->
                {
                    if (CollectionUtils.isEmpty(list))
                    {
                        return ConsumeOrderlyStatus.SUCCESS;
                    }
                    // MQ不会一次拉取多个不同Topic消息,直接取第一个
                    String topicName = list.get(0).getTopic();

                    // 获取对应实际处理类
                    RocketMqConsumerInterface rocketMqConsumerInterface =
                            SpringBeanUtil.getBean("rocketMqConsumerHandle." + topicName,
                                    RocketMqConsumerInterface.class);

                    if (rocketMqConsumerInterface == null)
                    {
                        log.info("未根据topic:{}找到对应处理类,请检查代码", topicName);
                        return ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
                    }

                    RocketMqConsumerResult result = rocketMqConsumerInterface.handle(
                            new RocketMqConsumerParamBuilder().list(list).orderlyContext(consumeOrderlyContext)
                                    .build());

                    // 判断是否成功
                    if (result.isSuccess())
                    {
                        return ConsumeOrderlyStatus.SUCCESS;
                    }
                    else
                    {
                        // 失败是否需要重试
                        if (result.isReconsumeLater())
                        {
                            // 有序消费,最好在业务消费类中加入消费次数记录，当消费达到多少次之后，还是失败则返回成功，并且加入日志加预警功能
                            // 因为有序消费返回SUSPEND_CURRENT_QUEUE_A_MOMENT会一直消费，导致其他消息处理不了
                            return ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
                        }
                        else
                        {
                            return ConsumeOrderlyStatus.SUCCESS;
                        }
                    }
                });
            }
            else
            {
                start.registerMessageListener((MessageListenerConcurrently) (list, consumeConcurrentlyContext) ->
                {

                    if (CollectionUtils.isEmpty(list))
                    {
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }
                    // MQ不会一次拉取多个不同Topic消息,直接取第一个
                    String topicName = list.get(0).getTopic();

                    // 获取对应实际处理类
                    RocketMqConsumerInterface rocketMqConsumerInterface =
                            SpringBeanUtil.getBean("rocketMqConsumerHandle." + topicName,
                                    RocketMqConsumerInterface.class);

                    if (rocketMqConsumerInterface == null)
                    {
                        log.info("未根据topic:{}找到对应处理类,请检查代码", topicName);
                        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                    }

                    RocketMqConsumerResult result = rocketMqConsumerInterface.handle(
                            new RocketMqConsumerParamBuilder().list(list)
                                    .concurrentlyContext(consumeConcurrentlyContext)
                                    .build());

                    // 判断是否成功
                    if (result.isSuccess())
                    {
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }
                    else
                    {
                        // 失败是否需要重试,默认失败次数达到16次消息会进入死信队列
                        if (result.isReconsumeLater())
                        {
                            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                        }
                        else
                        {
                            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                        }
                    }
                });
            }

            List<RocketMqTopicVo> topics = consumer.getTopics();
            if (CollectionUtils.isEmpty(topics))
            {
                // 未配置主题，则不启动
                return;
            }

            try
            {
                for (RocketMqTopicVo topic : topics)
                {
                    start.subscribe(topic.getTopicName(),
                            StringUtils.isBlank(topic.getTagName()) ? "*" : topic.getTagName());
                }
               // start.start();
                startConsumer.add(start);
                log.info("RocketMq消费者group:{},namesrv:{}启动成功", consumer.getGroupName(), consumer.getNamesrvAddr());
            }
            catch (MQClientException e)
            {
                log.error("RocketMq消费者group:" + consumer.getGroupName() + ",namesrv:" + consumer.getNamesrvAddr() +
                        "启动失败", e);
            }

        });

    }

    @PreDestroy
    public void preDestroy()
    {
        log.info("正在关闭MQ连接");
        startConsumer.forEach(consumer ->
        {
            try
            {
                consumer.shutdown();
                log.info("RocketMq消费者group:" + consumer.getConsumerGroup() + ",namesrv:" + consumer.getNamesrvAddr() +
                        "关闭连接成功");
            }
            catch (Exception e)
            {
                log.error("RocketMq消费者group:" + consumer.getConsumerGroup() + ",namesrv:" + consumer.getNamesrvAddr() +
                        "关闭连接失败", e);
            }
        });
    }

}
