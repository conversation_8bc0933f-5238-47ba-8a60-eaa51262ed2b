package com.jettech.basic.mq.kafka.vo;

import lombok.Data;

/**
 * Kafka生产者配置VO对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description Kafka生产者配置VO对象
 * @projectName jettong-util
 * @package com.jettech.basic.mq.kafka.vo
 * @className KafkaProducerVo
 * @date 2022/12/7 11:53
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class KafkaProducerVo
{

    /**
     * 生产者唯一编号
     */
    private String producerId;

    /**
     * groupId
     */
    private String groupId;

    /**
     * kafka的bootstrapServers地址
     */
    private String bootstrapServers;

    /**
     * acks
     */
    private String acks;

    /**
     * 一个批次可以使用的内存大小 缺省16384(16k)
     */
    private Integer batchSize;

    /**
     * 指定了生产者在发送批次前等待更多消息加入批次的时间,  缺省0  50ms
     */
    private Integer lingerMs;

    /**
     * 控制生产者发送请求最大大小,默认1 * 1024 * 1024(1M） （这个参数和Kafka主机的message.max.bytes 参数有关系）
     */
    private Integer maxRequestSize;

    /**
     * 生产者内存缓冲区大小 32 * 1024 * 1024L
     */
    private Integer bufferMemory;

    /**
     * 重发消息次数
     */
    private Integer retries;

    /**
     * 客户端将等待请求的响应的最大时间 默认30 * 1000(30秒）
     */
    private Integer requestTimeoutMs;

    /**
     * 最大阻塞时间，超过则抛出异常 缺省60000ms
     */
    private Integer maxBlockMs;

    /**
     * 于压缩数据的压缩类型。默认是无压缩 ,none、gzip、snappy
     */
    private String compressionType;

    /**
     * 加密协议（和认证集群搭建时候的配置内容对应）
     */
    private String securityProtocol;

    /**
     * 加密方式（和认证集群搭建时候的配置内容对应）
     */
    private String saslMechanism;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String pwd;
}
