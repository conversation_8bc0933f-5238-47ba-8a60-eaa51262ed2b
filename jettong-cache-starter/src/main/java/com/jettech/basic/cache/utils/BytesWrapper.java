package com.jettech.basic.cache.utils;

/**
 * BytesWrapper
 *
 * <AUTHOR>
 * @version 1.0
 * @description BytesWrapper
 * @projectName cloudbooster
 * @package com.jettech.basic.cache.utils
 * @className BytesWrapper
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class BytesWrapper<T> implements Cloneable
{
    private T value;

    public BytesWrapper()
    {
    }

    public BytesWrapper(T value)
    {
        this.value = value;
    }

    public T getValue()
    {
        return value;
    }

    public void setValue(T value)
    {
        this.value = value;
    }

    @Override
    @SuppressWarnings("unchecked")
    public BytesWrapper<T> clone()
    {
        try
        {
            return (BytesWrapper) super.clone();
        }
        catch (CloneNotSupportedException e)
        {
            return new BytesWrapper<>();
        }
    }
}
