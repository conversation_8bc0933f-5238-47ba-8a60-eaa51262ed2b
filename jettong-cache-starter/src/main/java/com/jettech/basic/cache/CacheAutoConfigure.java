package com.jettech.basic.cache;

import com.jettech.basic.utils.StrPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

/**
 * 缓存配置
 *
 * <AUTHOR>
 * @version 1.0
 * @description 缓存配置
 * @projectName cloudbooster
 * @package com.jettech.basic.cache
 * @className CacheAutoConfigure
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@EnableCaching
@Import({
        RedisAutoConfigure.class
})
public class CacheAutoConfigure
{

    /**
     * key 的生成
     */
    @Bean
    public KeyGenerator keyGenerator()
    {
        return (target, method, objects) ->
        {
            StringBuilder sb = new StringBuilder();
            sb.append(target.getClass().getName());
            sb.append(StrPool.COLON);
            sb.append(method.getName());
            for (Object obj : objects)
            {
                if (obj != null)
                {
                    sb.append(StrPool.COLON);
                    sb.append(obj.toString());
                }
            }
            return sb.toString();
        };
    }

}
