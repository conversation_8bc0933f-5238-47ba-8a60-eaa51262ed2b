package com.jettech.basic.cache.properties;

/**
 * 序列化类型
 *
 * <AUTHOR>
 * @version 1.0
 * @description 缓存配置
 * @projectName cloudbooster
 * @package com.jettech.basic.cache.properties
 * @className SerializerType
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public enum SerializerType
{
    /**
     * json 序列化
     */
    JACK_SON,
    /**
     * 默认:ProtoStuff 序列化
     */
    ProtoStuff,
    /**
     * jdk 序列化
     */
    JDK
}
