package com.jettech.basic.cache.lock;

import com.jettech.basic.lock.DistributedLock;

/**
 * 分布式锁 只能用redis实现
 * 写这个类的目的，只是为了防止代码启动报错
 *
 * <AUTHOR>
 * @version 1.0
 * @description 分布式锁 只能用redis实现
 * @projectName cloudbooster
 * @package com.jettech.basic.cache.lock
 * @className CaffeineDistributedLock
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class CaffeineDistributedLock implements DistributedLock
{
    @Override
    public boolean lock(String key, long expire, int retryTimes, long sleepMillis)
    {
        return true;
    }

    @Override
    public boolean releaseLock(String key)
    {
        return true;
    }
}
