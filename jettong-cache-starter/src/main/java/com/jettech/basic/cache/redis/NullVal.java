package com.jettech.basic.cache.redis;

import java.io.Serializable;

/**
 * 空值 解决缓存穿透
 *
 * <AUTHOR>
 * @version 1.0
 * @description 缓存配置
 * @projectName cloudbooster
 * @package com.jettech.basic.cache.redis
 * @className NullVal
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class NullVal implements Serializable
{
    private static final long serialVersionUID = 1L;
}
