package com.jettech.basic.cache.repository;

import com.jettech.basic.cache.model.CacheKey;
import org.springframework.lang.NonNull;

import java.util.Collection;
import java.util.List;
import java.util.function.Function;

/**
 * 缓存操作公共接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 缓存操作公共接口
 * @projectName cloudbooster
 * @package com.jettech.basic.cache.repository
 * @className CacheOps
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface CacheOps
{

    /**
     * 删除指定的key
     *
     * @param keys 多个key
     * @return Long 删除个数
     * <AUTHOR>
     * @date 2021/9/13 11:31
     * @update zxy 2021/9/13 11:31
     * @since 1.0
     */
    Long del(@NonNull CacheKey... keys);

    /**
     * 删除指定的key
     *
     * @param keys 多个key
     * @return Long 删除个数
     * <AUTHOR>
     * @date 2021/9/13 11:31
     * @update zxy 2021/9/13 11:31
     * @since 1.0
     */
    Long del(@NonNull String... keys);

    /**
     * 判断指定的key 是否存在
     *
     * @param key key
     * @return Boolean 是否存在
     * <AUTHOR>
     * @date 2021/9/13 11:32
     * @update zxy 2021/9/13 11:32
     * @since 1.0
     */
    Boolean exists(@NonNull CacheKey key);

    /**
     * 添加到带有 过期时间的  缓存
     *
     * @param key redis主键
     * @param value 值
     * @param cacheNullValues 是否缓存null对象
     * <AUTHOR>
     * @date 2021/9/13 11:32
     * @update zxy 2021/9/13 11:32
     * @since 1.0
     */
    void set(@NonNull CacheKey key, Object value, boolean... cacheNullValues);

    /**
     * 根据key获取对象
     *
     * @param key redis主键
     * @param cacheNullValues 是否缓存null对象
     * @return T 值 不存在时，返回null
     * <AUTHOR>
     * @date 2021/9/13 11:33
     * @update zxy 2021/9/13 11:33
     * @since 1.0
     */
    <T> T get(@NonNull CacheKey key, boolean... cacheNullValues);

    /**
     * 根据key获取对象
     *
     * @param key redis主键
     * @param cacheNullValues 是否缓存null对象
     * @return T 值 不存在时，返回null
     * <AUTHOR>
     * @date 2021/9/13 11:33
     * @update zxy 2021/9/13 11:33
     * @since 1.0
     */
    <T> T get(@NonNull String key, boolean... cacheNullValues);

    /**
     * 根据keys获取对象
     *
     * @param keys redis主键
     * @return List<T> 值 不存在时，返回空集合
     * <AUTHOR>
     * @date 2021/9/13 11:34
     * @update zxy 2021/9/13 11:34
     * @since 1.0
     */
    <T> List<T> find(@NonNull Collection<CacheKey> keys);

    /**
     * 据key获取对象
     * 不存在时，调用function回调获取数据，并set进入，然后返回
     *
     * @param key redis主键
     * @param loader 加载器
     * @param cacheNullValues 是否缓存null对象
     * @return T 值
     * <AUTHOR>
     * @date 2021/9/13 11:34
     * @update zxy 2021/9/13 11:34
     * @since 1.0
     */
    <T> T get(@NonNull CacheKey key, Function<CacheKey, ? extends T> loader, boolean... cacheNullValues);

    /**
     * 清空所有存储的数据
     *
     * <AUTHOR>
     * @date 2021/9/13 11:35
     * @update zxy 2021/9/13 11:35
     * @since 1.0
     */
    void flushDb();

    /**
     * 为键 key 储存的数字值加上一。
     *
     * @param key 一定不能为 {@literal null}.
     * @return Long 返回键 key 在执行加一操作之后的值。
     * <AUTHOR>
     * @date 2021/9/13 11:35
     * @update zxy 2021/9/13 11:35
     * @since 1.0
     */
    Long incr(@NonNull CacheKey key);

    /**
     * 获取key中存放的Long值
     *
     * @param key 一定不能为 {@literal null}.
     * @param loader 加载器
     * @return Long key中存储的的数字
     * <AUTHOR>
     * @date 2021/9/13 11:35
     * @update zxy 2021/9/13 11:35
     * @since 1.0
     */
    Long getCounter(@NonNull CacheKey key, Function<CacheKey, Long> loader);

    /**
     * 为键 key 储存的数字值加上increment。
     *
     * @param key 一定不能为 {@literal null}.
     * @param increment 增量值
     * @return Long 返回键 key 在执行加增量值操作之后的值。
     * <AUTHOR>
     * @date 2021/9/13 11:36
     * @update zxy 2021/9/13 11:36
     * @since 1.0
     */
    Long incrBy(@NonNull CacheKey key, long increment);

    /**
     * 为键 key 储存的数字值加上increment。
     *
     * @param key 一定不能为 {@literal null}.
     * @param increment 增量值
     * @return Double  返回键 key 在执行加increment操作之后的值。
     * <AUTHOR>
     * @date 2021/9/13 11:37
     * @update zxy 2021/9/13 11:37
     * @since 1.0
     */
    Double incrByFloat(@NonNull CacheKey key, double increment);

    /**
     * 为键 key 储存的数字值减去一。
     *
     * @param key 一定不能为 {@literal null}.
     * @return Long 在减去增量 1 之后， 键 key 的值。
     * <AUTHOR>
     * @date 2021/9/13 11:37
     * @update zxy 2021/9/13 11:37
     * @since 1.0
     */
    Long decr(@NonNull CacheKey key);

    /**
     * 将 key 所储存的值减去减量 decrement 。
     *
     * @param key 一定不能为 {@literal null}.
     * @param decrement 减量值
     * @return Long 在减去增量 decrement 之后， 键 key 的值。
     * <AUTHOR>
     * @date 2021/9/13 11:38
     * @update zxy 2021/9/13 11:38
     * @since 1.0
     */
    Long decrBy(@NonNull CacheKey key, long decrement);
}
