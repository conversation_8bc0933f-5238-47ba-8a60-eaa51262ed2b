package com.jettech.basic.cache.properties;

/**
 * 缓存类型
 *
 * <AUTHOR>
 * @version 1.0
 * @description 缓存类型
 * @projectName cloudbooster
 * @package com.jettech.basic.cache.properties
 * @className CacheType
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public enum CacheType
{
    /**
     * 内存
     */
    CAFFEINE,
    /**
     * redis
     */
    REDIS,
    ;

    public boolean eq(CacheType cacheType)
    {
        return cacheType != null && this.name().equals(cacheType.name());
    }
}
