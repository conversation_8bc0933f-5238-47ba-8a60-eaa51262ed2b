package com.jettech.basic.log.entity;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * <p>
 * 实体注释中生成的类型枚举
 * 系统日志
 * </p>
 *
 * <AUTHOR>
 * @date 2020-11-20
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "OptLogTypeEnum", description = "日志类型-枚举")
public enum OptLogTypeEnum implements BaseEnum
{

    /**
     * ADD="新增"
     */
    ADD("新增"),
    /**
     * EDIT="修改"
     */
    EDIT("修改"),
    /**
     * DELETE = "删除"
     */
    DELETE("删除"),
    /**
     *
     */
    QUERY("查询"),
    /**
     * 上传
     */
    UPLOAD("上传"),
    /**
     * 下载
     */
    DOWNLOAD("下载"),
    /**
     * 导入
     */
    IMPORT("导入"),
    /**
     * 导出
     */
    EXPORT("导出"),
    /**
     * 其他
     */
    OTHER("其他"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static OptLogTypeEnum match(String val, OptLogTypeEnum def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static OptLogTypeEnum get(String val)
    {
        return match(val, null);
    }

    public boolean eq(OptLogTypeEnum val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "ADD,DELETE,EDIT,QUERY,UPLOAD,DOWNLOAD,IMPORT,EXPORT,OTHER",
            example = "ADD")
    public String getCode()
    {
        return this.name();
    }

}
