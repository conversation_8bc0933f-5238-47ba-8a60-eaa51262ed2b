package com.jettech.basic.model;

import java.util.Map;

/**
 * 注入VO 父类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 注入VO 父类
 * @projectName cloudbooster
 * @package com.jettech.basic.model
 * @className EchoVO
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface EchoVO
{

    /**
     * 回显值 集合
     *
     * @return Map<String, Object> 回显值 集合
     * <AUTHOR>
     * @date 2021/9/13 10:10
     * @update zxy 2021/9/13 10:10
     * @since 1.0
     */
    Map<String, Object> getEchoMap();
}
