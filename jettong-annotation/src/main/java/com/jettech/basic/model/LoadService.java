package com.jettech.basic.model;


import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * 加载数据
 *
 * <AUTHOR>
 * @version 1.0
 * @description 加载数据
 * @projectName cloudbooster
 * @package com.jettech.basic.model
 * @className LoadService
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface LoadService
{
    /**
     * 根据id查询待回显参数
     *
     * @param ids 唯一键（可能不是主键ID)
     * @return Map<Serializable, Object> 待回显参数
     * <AUTHOR>
     * @date 2021/9/13 10:11
     * @update zxy 2021/9/13 10:11
     * @since 1.0
     */
    Map<Serializable, Object> findByIds(Set<Serializable> ids);
}
