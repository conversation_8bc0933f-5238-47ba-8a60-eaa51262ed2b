package com.jettech.basic.annotation.log;

import com.jettech.basic.log.entity.OptLogTypeEnum;

import java.lang.annotation.*;

/**
 * 操作日志注解
 *
 * <AUTHOR>
 * @version 1.0
 * @description 操作日志注解
 * @projectName cloudbooster
 * @package com.jettech.basic.annotation.log
 * @className SysLog
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SysLog
{
    /**
     * 是否启用 操作日志
     * 禁用控制优先级：jettong.log.enabled = false > 控制器类上@SysLog(enabled = false) > 控制器方法上@SysLog(enabled = false)
     *
     * @return 是否启用
     */
    boolean enabled() default true;

    /**
     * 操作日志类型
     *
     * @return 操作日志类型
     */
    OptLogTypeEnum optType() default OptLogTypeEnum.QUERY;

    /**
     * 操作日志的描述， 支持spring 的 SpEL 表达式。
     *
     * @return {String}
     */
    String value() default "";

    /**
     * 是否拼接Controller类上@Api注解的描述值
     *
     * @return 是否拼接Controller类上的描述值
     */
    boolean controllerApiValue() default true;

    /**
     * 是否记录方法的入参
     *
     * @return 是否记录方法的入参
     */
    boolean request() default true;

    /**
     * 若设置了 request = false、requestByError = true，则方法报错时，依然记录请求的入参
     *
     * @return 当 request = false时， 方法报错记录请求参数
     */
    boolean requestByError() default true;

    /**
     * 是否记录返回值
     *
     * @return 是否记录返回值
     */
    boolean response() default true;
}
