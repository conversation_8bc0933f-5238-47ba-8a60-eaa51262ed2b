package com.jettech.basic.base;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * 枚举类型基类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 枚举类型基类
 * @projectName cloudbooster
 * @package com.jettech.basic.base
 * @className BaseEnum
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface BaseEnum extends IEnum<String>
{

    /**
     * 编码
     *
     * @return 编码
     */
    default String getCode()
    {
        return toString();
    }

    /**
     * 描述
     *
     * @return 描述
     */
    String getDesc();

    /**
     * 扩展参数
     *
     * @return 扩展参数
     */
    default String getExtra()
    {
        return "";
    }

    /**
     * 判断val是否跟当前枚举相等
     *
     * @param val 值
     * @return 是否等于
     */
    default boolean eq(String val)
    {
        return this.getCode().equalsIgnoreCase(val);
    }

    /**
     * 枚举值
     *
     * @return 数据库中的值
     */
    @Override
    default String getValue()
    {
        return getCode();
    }
}
