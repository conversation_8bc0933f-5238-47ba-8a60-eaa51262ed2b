package com.jettech.basic.base.validation;

/**
 * 实现了此接口，表示此类将会支持验证框架。
 *
 * <AUTHOR>
 * @version 1.0
 * @description 实现了此接口，表示此类将会支持验证框架。
 * @projectName cloudbooster
 * @package com.jettech.basic.base.validation
 * @className IValidatable
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface IValidatable
{

    /**
     * 此类需要检验什么值
     * 支持length长度检验。也可以看情况实现支持类似于email，正则等等校验
     *
     * @return 需要验证的值
     */
    Object value();
}
