package com.jettech.basic.database.mybatis.auth;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * 实体注释中生成的类型枚举
 * 角色
 *
 * <AUTHOR>
 * @version 1.0
 * @description 实体注释中生成的类型枚举
 * @projectName cloudbooster
 * @package com.jettech.basic.database.mybatis.auth
 * @className DataScopeType
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@ApiModel(value = "DataScopeType", description = "数据权限类型-枚举")
public enum DataScopeType implements BaseEnum
{

    /**
     * ALL=5全部
     */
    ALL(5, "全部"),
    /**
     * THIS_LEVEL=4本级
     */
    THIS_LEVEL(4, "本级"),
    /**
     * THIS_LEVEL_CHILDREN=3本级以及子级
     */
    THIS_LEVEL_CHILDREN(3, "本级以及子级"),
    /**
     * CUSTOMIZE=2自定义
     */
    CUSTOMIZE(2, "自定义"),
    /**
     * SELF=1个人
     */
    SELF(1, "个人"),
    ;

    @ApiModelProperty(value = "描述")
    private final int val;

    private final String desc;


    public static DataScopeType match(String val, DataScopeType def)
    {
        return Stream.of(values()).parallel().filter((item) -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static DataScopeType match(Integer val, DataScopeType def)
    {
        return Stream.of(values()).parallel().filter((item) -> val != null && item.getVal() == val).findAny()
                .orElse(def);
    }

    public static DataScopeType get(String val)
    {
        return match(val, null);
    }

    public static DataScopeType get(Integer val)
    {
        return match(val, null);
    }

    public boolean eq(final DataScopeType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "ALL,THIS_LEVEL,THIS_LEVEL_CHILDREN,CUSTOMIZE,SELF",
            example = "ALL")
    public String getCode()
    {
        return this.name();
    }

}
