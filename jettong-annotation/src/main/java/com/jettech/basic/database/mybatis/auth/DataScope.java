package com.jettech.basic.database.mybatis.auth;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.List;

/**
 * 数据权限查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据权限查询参数
 * @projectName cloudbooster
 * @package com.jettech.basic.database.mybatis.auth
 * @className DataScope
 * @date 2021/9/9 11:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataScope extends HashMap
{
    /**
     * 限制范围的字段名称 （除个人外）
     */
    private String scopeName = "org_id";
    /**
     * 限制范围为个人时的字段名称
     */
    private String selfScopeName = "created_by";
    /**
     * 当前用户ID
     */
    private Long userId;

    /**
     * 具体的数据范围
     */
    private List<Long> orgIds;

}
