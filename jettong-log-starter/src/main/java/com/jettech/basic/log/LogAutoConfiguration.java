package com.jettech.basic.log;


import com.jettech.basic.jackson.JsonUtil;
import com.jettech.basic.log.aspect.SysLogAspect;
import com.jettech.basic.log.event.SysLogListener;
import com.jettech.basic.log.monitor.PointUtil;
import com.jettech.basic.log.properties.OptLogProperties;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 日志自动配置
 * <p>
 * 启动条件：
 * 1，存在web环境
 * 2，配置文件中jettong.log.enabled=true 或者 配置文件中不存在：jettong.log.enabled 值
 *
 * <AUTHOR>
 * @date 2019/2/1
 */
@EnableAsync
@Configuration
@AllArgsConstructor
@ConditionalOnWebApplication
@ConditionalOnProperty(prefix = OptLogProperties.PREFIX, name = "enabled", havingValue = "true", matchIfMissing = true)
public class LogAutoConfiguration
{

    @Bean
    @ConditionalOnMissingBean
    public SysLogAspect sysLogAspect()
    {
        return new SysLogAspect();
    }

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnExpression("${jettong.log.enabled:true} && 'LOGGER'.equals('${jettong.log.type:LOGGER}')")
    public SysLogListener sysLogListener()
    {
        return new SysLogListener(log -> PointUtil.debug("0", "OPT_LOG", JsonUtil.toJson(log)), true);
    }
}
