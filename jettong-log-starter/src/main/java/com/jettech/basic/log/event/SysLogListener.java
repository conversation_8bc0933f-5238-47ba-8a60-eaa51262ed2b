package com.jettech.basic.log.event;


import cn.hutool.core.util.StrUtil;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.log.entity.OptLogDTO;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;

import java.util.function.Consumer;


/**
 * 异步监听日志事件
 *
 * <AUTHOR>
 * @date 2019-07-01 15:13
 */
@Slf4j
@AllArgsConstructor
public class SysLogListener
{

    private final Consumer<OptLogDTO> consumer;

    private final Boolean query;

    @Async
    @Order
    @EventListener(SysLogEvent.class)
    public void saveSysLog(SysLogEvent event)
    {
        OptLogDTO sysLog = (OptLogDTO) event.getSource();
        // 是否需要记录查询日志
        if (!query && OptLogTypeEnum.QUERY.eq(sysLog.getType()))
        {
            log.warn("不记录查询类操作日志，{}", sysLog != null ? sysLog.getRequestUri() : "");
            return;
        }

        // 非租户模式 (NONE) ， 需要修改这里的判断
        if (sysLog == null)
        {
            log.warn("日志信息为null，不记录操作日志=={}", sysLog != null ? sysLog.getRequestUri() : "");
            return;
        }
        else
        {
            if (StrUtil.isNotEmpty(sysLog.getTenantCode()))
            {
                ContextUtil.setTenant(sysLog.getTenantCode());
            }
        }

        consumer.accept(sysLog);

    }

}
