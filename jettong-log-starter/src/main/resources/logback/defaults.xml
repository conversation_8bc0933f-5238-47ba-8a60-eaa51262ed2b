<?xml version="1.0" encoding="UTF-8"?>
<included>
    <!--
         规则：
            dev 开发环境： 启用实时打印日志，启用p6spy打印sql语句到控制台
            其他环境:  启用异步打印，  禁用p6spy
           %m
           输出代码中指定的消息
           %p
           输出优先级，即DEBUG，INFO，WARN，ERROR，FATAL
           %r
           输出自应用启动到输出该log信息耗费的毫秒数
           %c
           输出所属的类目，通常就是所在类的全名
           %t
           输出产生该日志事件的线程名
           %n
           输出一个回车换行符，Windows平台为“\r\n”，Unix平台为“\n”
           %d
           输出日志时间点的日期或时间，默认格式为ISO8601，也可以在其后指定格式，比如：%d{yyy MMM dd HH:mm:ss,SSS}，
           输出类似：2002年10月18日 22：10：28，921
           %l
           输出日志事件的发生位置，包括类目名、发生的线程，以及在代码中的行数。举例：TestLog.main(TestLog.java:10)
        -->
    <springProperty scope="context" name="log.path" source="logging.file.path" defaultValue="/data/projects/logs"/>
    <springProperty scope="context" name="spring.application.name" source="spring.application.name"/>
    <springProperty scope="context" name="spring.profiles.active" source="spring.profiles.active"/>
    <!--    <springProperty scope="context" name="common-pattern" source="logging.common-pattern"-->
    <!--                    defaultValue="%d{yyyy-MM-dd HH:mm:ss.SSS}:[%5p] [%t:%r] [%logger{50}.%M:%L] &ndash;&gt; %msg%n"/>-->
    <springProperty scope="context" name="log.level.console" source="logging.level.console" defaultValue="INFO"/>
    <springProperty scope="context" name="log.level.controller" source="logging.level.controller" defaultValue="INFO"/>
    <springProperty scope="context" name="log.level.service" source="logging.level.service" defaultValue="INFO"/>
    <springProperty scope="context" name="log.level.dao" source="logging.level.sql" defaultValue="INFO"/>
    <springProperty scope="context" name="server.port" source="server.port" defaultValue="0000"/>
    <!-- 彩色日志 -->
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <!-- 彩色日志格式 -->
    <springProperty scope="context" name="common-pattern-color" source="logging.common-pattern-color"
                    defaultValue="[${spring.application.name}:${server.port}:%X{tenant}:%X{userid}] %clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint}:%clr([%5p]){blue} %clr([${PID}]){magenta} %clr([%X{trace}]){yellow} %clr([%t:%r]){orange} %clr([%logger{50}.%M:%L]){cyan} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
    <springProperty scope="context" name="common-pattern" source="logging.common-pattern"
                    defaultValue="[${spring.application.name}:${server.port}:%X{tenant}:%X{userid}] %d{yyyy-MM-dd HH:mm:ss.SSS}[%5p] ${PID} [%X{trace}] [%t:%r] [%logger{50}.%M:%L] %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

    <contextName>${spring.application.name}-logback</contextName>

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <jmxConfigurator/>


    <!-- 控制台实时输出，采用高亮语法，用于开发环境 -->
    <appender name="CONSOLE_APPENDER" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <!--            <level>${log.level.console}</level>-->
        </filter>
        <encoder>
            <pattern>${common-pattern-color}</pattern>
        </encoder>
    </appender>
    <!-- 控制台异步实时输出 -->
    <appender name="ASYNC_CONSOLE_APPENDER" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>256</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="CONSOLE_APPENDER"/>
    </appender>

    <!-- 整个项目的所有日志， 包括第三方包 -->
    <appender name="ROOT_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${spring.application.name}/root.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 每天一归档 -->
            <fileNamePattern>${log.path}/${spring.application.name}/%d{yyyy-MM}/root-%d{yyyy-MM-dd}-%i.log.gz
            </fileNamePattern>
            <!-- 单个日志文件最多 100MB, 60天的日志周期，最大不能超过20GB -->
            <maxFileSize>128MB</maxFileSize>
            <maxHistory>60</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${common-pattern}</pattern>
        </encoder>
    </appender>
    <appender name="ASYNC_ROOT_APPENDER" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>256</queueSize>
        <appender-ref ref="ROOT_APPENDER"/>
    </appender>

    <!-- 整个项目com.jettech.basic包下的的所有日志， 不包括第三方包 -->
    <appender name="PROJECT_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${spring.application.name}/project.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 每天一归档 -->
            <fileNamePattern>${log.path}/${spring.application.name}/%d{yyyy-MM}/project-%d{yyyy-MM-dd}-%i.log.gz
            </fileNamePattern>
            <!-- 单个日志文件最多 100MB, 60天的日志周期，最大不能超过20GB -->
            <maxFileSize>128MB</maxFileSize>
            <maxHistory>60</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${common-pattern}</pattern>
        </encoder>
    </appender>
    <appender name="ASYNC_PROJECT_APPENDER" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>256</queueSize>
        <appender-ref ref="PROJECT_APPENDER"/>
    </appender>

    <!-- 整个项目第三方包下的的日志 -->
    <appender name="THIRD_PARTY_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${spring.application.name}/third_party.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 每天一归档 -->
            <fileNamePattern>${log.path}/${spring.application.name}/%d{yyyy-MM}/third_party-%d{yyyy-MM-dd}-%i.log.gz
            </fileNamePattern>
            <!-- 单个日志文件最多 100MB, 60天的日志周期，最大不能超过20GB -->
            <maxFileSize>128MB</maxFileSize>
            <maxHistory>60</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${common-pattern}</pattern>
        </encoder>
    </appender>
    <appender name="ASYNC_THIRD_PARTY_APPENDER" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>256</queueSize>
        <appender-ref ref="THIRD_PARTY_APPENDER"/>
    </appender>

    <!-- service包的日志 -->
    <appender name="SERVICE_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${spring.application.name}/service.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/${spring.application.name}/%d{yyyy-MM}/service-%d{yyyy-MM-dd}-%i.log.gz
            </fileNamePattern>
            <maxFileSize>128MB</maxFileSize>
            <maxHistory>60</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${common-pattern}</pattern>
        </encoder>
    </appender>
    <appender name="ASYNC_SERVICE_APPENDER" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>256</queueSize>
        <appender-ref ref="SERVICE_APPENDER"/>
    </appender>

    <!-- 埋点包的日志 -->
    <appender name="POINT_LOG_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${spring.application.name}/point.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/${spring.application.name}/%d{yyyy-MM}/point-%d{yyyy-MM-dd}-%i.log.gz
            </fileNamePattern>
            <maxFileSize>128MB</maxFileSize>
            <maxHistory>60</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${common-pattern}</pattern>
        </encoder>
    </appender>
    <appender name="ASYNC_POINT_LOG_APPENDER" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>256</queueSize>
        <appender-ref ref="POINT_LOG_APPENDER"/>
    </appender>

    <!-- controller 包的日志 -->
    <appender name="CONTROLLER_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${spring.application.name}/controller.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/${spring.application.name}/%d{yyyy-MM}/controller-%d{yyyy-MM-dd}-%i.log.gz
            </fileNamePattern>
            <maxFileSize>128MB</maxFileSize>
            <maxHistory>60</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${common-pattern}</pattern>
        </encoder>
    </appender>
    <appender name="ASYNC_CONTROLLER_APPENDER" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>256</queueSize>
        <appender-ref ref="CONTROLLER_APPENDER"/>
    </appender>


    <!-- dao层日志，用于打印执行的sql  -->
    <appender name="DAO_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${spring.application.name}/dao.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/${spring.application.name}/%d{yyyy-MM}/dao-%d{yyyy-MM-dd}-%i.log.gz
            </fileNamePattern>
            <maxFileSize>128MB</maxFileSize>
            <maxHistory>60</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${common-pattern}</pattern>
        </encoder>
    </appender>
    <appender name="ASYNC_DAO_APPENDER" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>256</queueSize>
        <appender-ref ref="DAO_APPENDER"/>
    </appender>

    <!-- 共用异常包的日志 -->
    <appender name="EXCEPTION_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${spring.application.name}/exception.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/${spring.application.name}/%d{yyyy-MM}/exception-%d{yyyy-MM-dd}-%i.log.gz
            </fileNamePattern>
            <maxFileSize>128MB</maxFileSize>
            <maxHistory>60</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${common-pattern}</pattern>
        </encoder>
    </appender>
    <appender name="ASYNC_EXCEPTION_APPENDER" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>256</queueSize>
        <appender-ref ref="EXCEPTION_APPENDER"/>
    </appender>

</included>
