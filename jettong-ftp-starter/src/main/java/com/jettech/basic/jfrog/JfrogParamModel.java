package com.jettech.basic.jfrog;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * JFROG操作参数对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description FTP操作参数对象
 * @projectName jettong
 * @package com.jettech.basic.jfrog
 * @className JfrogParamModel
 * @date 2022-06-27
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@Builder
@ToString(callSuper = true)
@EqualsAndHashCode
@Accessors(chain = true)
public class JfrogParamModel implements Serializable
{

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * jfrog名称
     */
    private String name;

    /**
     * 主机ip
     */
    private String ip;

    /**
     * 端口号
     */
    private int port;

    /**
     * ftp用户名
     */
    private String userName;

    /**
     * ftp密码
     */
    private String password;

    /**
     * ftp中的目录（不包含文件名）
     */
    private String jfrogPath;

    /**
     * 类型
     */
    private String type;
}
