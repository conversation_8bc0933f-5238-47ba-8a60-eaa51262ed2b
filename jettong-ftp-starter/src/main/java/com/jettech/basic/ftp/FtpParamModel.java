package com.jettech.basic.ftp;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * FTP操作参数对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description FTP操作参数对象
 * @projectName jettong
 * @package com.jettech.basic.ftp
 * @className FtpParam
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@Builder
@ToString(callSuper = true)
@EqualsAndHashCode
@Accessors(chain = true)
public class FtpParamModel implements Serializable
{

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * ftp名称
     */
    private String name;

    /**
     * 主机ip
     */
    private String ip;

    /**
     * 端口号
     */
    private int port;

    /**
     * ftp用户名
     */
    private String userName;

    /**
     * ftp密码
     */
    private String password;

    /**
     * ftp中的目录（不包含文件名）
     */
    private String ftpPath;

    /**
     * ftp中的文件名称
     */
    private String[] ftpFileNames;

    /**
     * 本地文件目录（包含文件名）
     */
    private String localPath;

    /**
     * 存放要下载的ftp服务器上的文件
     */
    private String[] ftpPaths;

    /**
     * 文件下载的本地文件路径
     */
    private String[] localPaths;

    /**
     * 登录成功之后，切换到的目录
     */
    private String rootPath;

    /**
     * 主机ip
     */
    private String type;
}
