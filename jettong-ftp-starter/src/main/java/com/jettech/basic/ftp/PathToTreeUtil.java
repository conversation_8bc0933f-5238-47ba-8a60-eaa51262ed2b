package com.jettech.basic.ftp;


import com.google.common.collect.Maps;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * 根据字符串文件相对路径生成文件树结构
 *
 * <AUTHOR>
 * @version 1.0
 * @description 根据字符串文件相对路径生成文件树结构
 * @projectName jettong
 * @package com.jettech.basic.ftp
 * @className PathToTreeUtil
 * @date 2021-11-02
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class PathToTreeUtil {
    public static String getType(String filename, String path) {
        if (path.endsWith(filename)) {
            return "file";
        }
        return "folder";
    }

    public static void addPath(HashMap<String, Object> root, String path) {
        String[] pathArr = path.split("/");
        for (String name : pathArr) {
            boolean flag = true;
            for (HashMap<String, Object> node : (ArrayList<HashMap<String, Object>>) root.get("content")) {
                if (node.get("name").equals(name)) {
                    root = node;
                    flag = false;
                    break;
                }
            }
            if (flag)
            {
                HashMap<String, Object> newNode = Maps.newHashMapWithExpectedSize(3);
                newNode.put("name", name);
                newNode.put("type", getType(name, path));
                newNode.put("content", new ArrayList<HashMap<String, Object>>());
                ((ArrayList<HashMap<String, Object>>) root.get("content")).add(newNode);
                root = newNode;
            }
        }
    }

    public static HashMap<String, Object> generateData(String s)
    {
        String[] paths = s.split(",");
        HashMap<String, Object> root = Maps.newHashMapWithExpectedSize(2);
        root.put("name", "");
        root.put("type", "");
        ArrayList<String> arrayList = new ArrayList<>();
        root.put("content", arrayList);
        for (String path : paths)
        {
            addPath(root, path);
        }
        return root;
    }
    /****
     * 1. 主分析程序，拿到路径总的字符串，进行分析
     * @param path
     * @return
     */
    public static HashMap<String, Object> pathToTree(String path) {
        path = zhuanYi(path);
        return generateData(path);
    }

    public static String zhuanYi(String path) {
        System.out.println(path);
        // 将双引号去掉，将多余的空字符去掉
        path = path.replaceAll("\"", "").replaceAll(" ", "");
        // 把\\转成/
        if (path.contains("\\\\")) {
            path = path.replaceAll("\\\\", "/");
        }
        if (path.contains("//")) {
            path = path.replaceAll("//", "/");
        }
        if (path.contains("//")) {
            path = path.replaceAll("//", "/");
        }
        return path;
    }
}


