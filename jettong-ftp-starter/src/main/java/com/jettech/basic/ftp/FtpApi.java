package com.jettech.basic.ftp;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

import java.io.*;
import java.util.*;

/**
 * ftp api
 *
 * <AUTHOR>
 * @version 1.0
 * @description ftp api
 * @projectName jettong-enterprises
 * @package com.jettech.basic.ftp
 * @className FtpApi
 * @date 2021/10/30 12:45
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
public class FtpApi
{

    public static final String FTP = "ftp";

    protected FTPClient ftp;

    public boolean connectFtp(FtpParamModel f)
    {
        try
        {
            ftp = new FTPClient();

            int reply;
            if (f.getPort() == 0)
            {
                ftp.connect(f.getIp(), 21);
            }
            else
            {
                ftp.connect(f.getIp(), f.getPort());
            }
            ftp.login(f.getUserName(), f.getPassword());
            ftp.setFileType(FTPClient.BINARY_FILE_TYPE);
            reply = ftp.getReplyCode();
            if (!FTPReply.isPositiveCompletion(reply))
            {
                ftp.disconnect();
                return false;
            }
            ftp.changeWorkingDirectory(f.getFtpPath());
            ftp.setRemoteVerificationEnabled(false);
            ftp.enterLocalPassiveMode();
            return true;

        }
        catch (Exception e)
        {
            log.error("连接FTP异常，原因：{}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 关闭FTP连接 1
     */
    public void closeFtp()
    {
        if (ftp != null && ftp.isConnected())
        {
            try
            {
                ftp.logout();
                ftp.disconnect();
            }
            catch (IOException e)
            {
            }

        }

    }

    /**
     * 查询FTP列表 1
     *
     * @param f
     * @return
     * @throws Exception
     */
    public List<String> getFtpFileListToShow(FtpParamModel f) throws Exception
    {
        List<String> fileNames = new ArrayList<>();
        if (connectFtp(f))
        {
            //注意编码格式
            ftp.setControlEncoding("utf-8");
            FTPClientConfig conf = new FTPClientConfig(FTPClientConfig.SYST_UNIX);
            //中文
            conf.setServerLanguageCode("zh");
            FTPFile[] listFiles = ftp.listFiles(f.getFtpPath());
            for (FTPFile listFile : listFiles)
            {
                String fileName = listFile.getName();
                fileNames.add(fileName);
            }
            Collections.sort(fileNames);
        }
        closeFtp();
        return fileNames;
    }

    /**
     * 查询FTP文件列表
     *
     * @param ftpParamModel
     * @return
     * @throws Exception
     */
    public Map<String, Object> getFtpFilesToShow(FtpParamModel ftpParamModel) throws Exception
    {
        Map<String, Object> result = Maps.newHashMapWithExpectedSize(2);
        String workingDirectory = null;
        List<FTPFile> ftpFiles = new ArrayList<>();
        if (connectFtp(ftpParamModel))
        {
            workingDirectory = ftp.printWorkingDirectory();
            //注意编码格式
            ftp.setControlEncoding("utf-8");
            FTPClientConfig conf = new FTPClientConfig(FTPClientConfig.SYST_UNIX);
            //中文
            conf.setServerLanguageCode("zh");
            FTPFile[] listFiles = ftp.listFiles(ftpParamModel.getFtpPath());
            Collections.addAll(ftpFiles, listFiles);
        }
        else
        {
            log.error("连接失败");
        }
        closeFtp();
        result.put("ftpFiles", ftpFiles);
        result.put("workingDirectory", workingDirectory);
        return result;
    }

    /**
     * 查询FTP指定路径文件列表
     *
     * @param ftpParamModel
     * @return
     * @throws Exception
     */
    public Map<String, Object> getFtpFilesToShow(FtpParamModel ftpParamModel, String ftpPath) throws Exception
    {
        Map<String, Object> result = Maps.newHashMapWithExpectedSize(2);
        String workingDirectory = null;
        List<FTPFile> ftpFiles = new ArrayList<>();
        BufferedReader ds = null;
        if (connectFtp(ftpParamModel))
        {
            workingDirectory = ftpPath;
            //注意编码格式
            ftp.setControlEncoding("utf-8");
            FTPClientConfig conf = new FTPClientConfig(FTPClientConfig.SYST_UNIX);
            //中文
            conf.setServerLanguageCode("zh");
            FTPFile[] listFiles = ftp.listFiles(new String(ftpPath.getBytes("utf-8"), "iso-8859-1"));
            Collections.addAll(ftpFiles, listFiles);
        }
        else
        {
            log.error("连接失败");
        }
        closeFtp();
        result.put("ftpFiles", ftpFiles);
        result.put("workingDirectory", workingDirectory);
        return result;
    }

    /**
     * 查询FTP指定路径的父路径文件列表
     *
     * @param ftpParamModel
     * @return
     * @throws Exception
     */
    public Map<String, Object> getParentFtpFilesToShow(FtpParamModel ftpParamModel, String ftpPath) throws Exception
    {
        Map<String, Object> result = Maps.newHashMapWithExpectedSize(2);
        String workingDirecoty = null;
        List<FTPFile> ftpFiles = new ArrayList<>();
        if (connectFtp(ftpParamModel))
        {
            ftp.changeWorkingDirectory(new String(ftpPath.getBytes("utf-8"), "iso-8859-1"));
            ftp.changeToParentDirectory();
            workingDirecoty = ftp.printWorkingDirectory();
            //注意编码格式
            ftp.setControlEncoding("utf-8");
            FTPClientConfig conf = new FTPClientConfig(FTPClientConfig.SYST_UNIX);
            //中文
            conf.setServerLanguageCode("zh");
            FTPFile[] listFiles = ftp.listFiles();
            Collections.addAll(ftpFiles, listFiles);
        }
        else
        {
            log.error("连接失败");
        }
        closeFtp();
        result.put("ftpFiles", ftpFiles);
        result.put("workingDirectory", workingDirecoty);
        return result;
    }

    public InputStream getFileContent(FtpParamModel ftpParamModel, String ftpFilePath) throws Exception
    {
        InputStream result = null;
        if (connectFtp(ftpParamModel))
        {
            //注意编码格式
            ftp.setControlEncoding("utf-8");
            FTPClientConfig conf = new FTPClientConfig(FTPClientConfig.SYST_UNIX);
            //中文
            conf.setServerLanguageCode("zh");
            ftp.enterLocalPassiveMode();
            ftp.setFileType(FTPClient.BINARY_FILE_TYPE);
            result = ftp.retrieveFileStream(new String(ftpFilePath.getBytes("utf-8"), "iso-8859-1"));
            //return result;
        }
        else
        {
            log.error("连接失败");
        }
        closeFtp();
        return result;

    }

    /**
     * ftp上传文件 1
     *
     * @param f
     * @throws Exception
     */
    public void upload(String ftpPath, File f) throws Exception
    {
        ftp.enterLocalPassiveMode();
        if (f.isDirectory())
        {
            //        	String dirName=new String(f.getName().getBytes("utf-8"), "iso-8859-1");//涉及到中文文件
            String dirName = f.getName();
            ftpPath = ftpPath + "/" + dirName;
            createDir(ftpPath);
            ftp.changeWorkingDirectory(ftpPath);
            String[] files = f.list();
            for (String fstr : files)
            {
                File file1 = new File(f.getPath() + "/" + fstr);
                if (file1.isDirectory())
                {
                    upload(ftpPath, file1);
                    ftp.changeToParentDirectory();
                }
                else
                {
                    String fn = f.getPath() + "/" + fstr;
                    File file2 = new File(fn);
                    FileInputStream input = new FileInputStream(file2);
                    // String filename=new String(file2.getName().getBytes("utf-8"), "iso-8859-1");
                    // 涉及到中文文件
                    String filename = file2.getName();
                    ftp.storeFile(filename, input);
                    input.close();
                }
            }
        }
        else
        {
            createDir(ftpPath);
            ftp.changeWorkingDirectory(ftpPath);
            ftp.setBufferSize(1024);
            FTPClientConfig conf = new FTPClientConfig(FTPClientConfig.SYST_UNIX);
            //设置文件类型（二进制）
            ftp.setControlEncoding("UTF-8");
            ftp.setFileType(FTPClient.BINARY_FILE_TYPE);
            String fn = f.getPath().replace("\\", "/");
            File file2 = new File(fn);
            try (FileInputStream input = new FileInputStream(file2))
            {
                //涉及到中文文件
                String filename = new String(file2.getName().getBytes("utf-8"), "iso-8859-1");
                ftp.storeFile(filename, input);
            }
            catch (Exception e)
            {
                throw new Exception(e);
            }
        }
    }

    /**
     * 创建FTP文件夹
     *
     * @param dir
     * @throws Exception
     */
    private void createDir(String dir) throws Exception
    {
        // sign
        StringTokenizer s = new StringTokenizer(dir, "/");
        s.countTokens();
        String pathName = "";
        while (s.hasMoreElements())
        {
            pathName = pathName + "/" + (String) s.nextElement();
            try
            {
                ftp.makeDirectory(pathName);
            }
            catch (Exception e)
            {
                log.error("createDir Exception");
            }
        }
    }

    /**
     * 下载连接配置
     *
     * @param f
     * @param localBaseDir 本地目录
     * @param remoteBaseDir 远程目录
     * @throws Exception
     */
    public void startDown(FtpParamModel f, String localBaseDir, String remoteBaseDir) throws Exception
    {
        ftp.enterLocalPassiveMode();
        File fl = new File(localBaseDir);
        if (!fl.exists())
        {
            fl.mkdirs();
        }
        if (this.connectFtp(f))
        {
            FTPFile[] files = null;
            boolean changedir = ftp.changeWorkingDirectory(remoteBaseDir);
            if (changedir)
            {
                //注意编码格式
                ftp.setControlEncoding("utf-8");
                FTPClientConfig conf = new FTPClientConfig(FTPClientConfig.SYST_UNIX);
                //中文
                conf.setServerLanguageCode("zh");
                files = ftp.listFiles();
                for (FTPFile file : files)
                {
                    downloadFile(file, localBaseDir, remoteBaseDir);

                }
            }
        }
        else
        {
            log.error("连接失败!");
        }

    }

    public void downloadSingeFile(FtpParamModel f, String localBaseDir, String remoteBaseDir, String fileName)
            throws Exception
    {
        File fl = new File(localBaseDir);
        if (!fl.exists())
        {
            fl.mkdirs();
        }
        if (this.connectFtp(f))
        {
            FTPFile[] files = null;
            boolean changedir = ftp.changeWorkingDirectory(remoteBaseDir);
            if (changedir)
            {
                //注意编码格式
                ftp.setControlEncoding("utf-8");
                FTPClientConfig conf = new FTPClientConfig(FTPClientConfig.SYST_UNIX);
                //中文
                conf.setServerLanguageCode("zh");
                files = ftp.listFiles();
                for (FTPFile file : files)
                {
                    if (file.getName().equals(fileName))
                    {
                        downloadFile(file, localBaseDir, remoteBaseDir);
                    }

                }
            }
        }
        else
        {
            log.error("连接失败!");
        }
    }

    /**
     * 下载FTP文件
     * 当你需要下载FTP文件的时候，调用此方法
     * 根据--获取的文件名，本地地址，远程地址--进行下载
     *
     * @param ftpFile
     * @param relativeLocalPath
     * @param relativeRemotePath
     * @throws Exception
     */
    private void downloadFile(FTPFile ftpFile, String relativeLocalPath, String relativeRemotePath) throws Exception
    {

        if (ftpFile.isFile())
        {
            //涉及到中文文件
            String filename = new String(ftpFile.getName().getBytes("utf-8"), "iso-8859-1");

            if (!ftpFile.getName().contains("?"))
            {
                OutputStream outputStream = null;
                try
                {
                    File localFile = new File(relativeLocalPath + ftpFile.getName());
                    if (!localFile.exists())
                    {
                        outputStream = new FileOutputStream(relativeLocalPath + ftpFile.getName());

                        ftp.retrieveFile(new String(ftpFile.getName().getBytes("UTF-8"), "ISO-8859-1"), outputStream);
                        outputStream.flush();
                        outputStream.close();
                    }
                }
                catch (Exception e)
                {
                    log.error(e.getMessage());
                }
                finally
                {
                    try
                    {
                        if (outputStream != null)
                        {
                            outputStream.close();
                        }
                    }
                    catch (IOException e)
                    {
                        log.error("输出文件流异常");
                    }
                }
            }
        }
        else
        {

            String fileName = new String(ftpFile.getName().getBytes("utf-8"), "iso-8859-1");
            String newlocalRelatePath = relativeLocalPath + ftpFile.getName();
            String newRemote = relativeRemotePath + fileName;

            File fl = new File(newlocalRelatePath);
            if (!fl.exists())
            {
                fl.mkdirs();
            }
            try
            {
                newlocalRelatePath = newlocalRelatePath + '/';
                newRemote = newRemote + "/";
                String currentWorkDir = fileName;
                boolean changeDir = ftp.changeWorkingDirectory(currentWorkDir);
                if (changeDir)
                {
                    FTPFile[] files = ftp.listFiles();
                    for (FTPFile file : files)
                    {
                        downloadFile(file, newlocalRelatePath, newRemote);
                    }
                }
                if (changeDir)
                {
                    ftp.changeToParentDirectory();
                }
            }
            catch (Exception e)
            {
                log.error(e.getMessage());
            }
        }
    }

    /**
     * @param ftpParam
     * @throws Exception
     */
    public Map<String, Object> deleteFileFtp(FtpParamModel ftpParam) throws Exception
    {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
        if (connectFtp(ftpParam))
        {
            String[] filePaths = ftpParam.getFtpPaths();
            for (int i = 0; i < filePaths.length; i++)
            {
                String pathFomat = filePaths[i].replace("\\", "/");
                if (!isDirExist(pathFomat))
                {
                    map.put("tag", false);
                    map.put("msg", "FTP文件夹不存在");
                    return map;
                }
                ftp.changeWorkingDirectory(pathFomat);
                iterateDelete(pathFomat + "/" + ftpParam.getFtpFileNames()[i]);
                Boolean dirExist = isDirExist(pathFomat + "/" + ftpParam.getFtpFileNames()[i]);
                if (dirExist)
                {
                    map.put("tag", false);
                    map.put("msg", "删除失败,请人工处理");
                }
                else
                {
                    map.put("tag", true);
                    map.put("msg", "删除成功");
                }
            }
        }
        else
        {
            log.error("连接FTP失败");
            map.put("tag", false);
            map.put("msg", "FTP连接失败");
        }
        return map;
    }

    /**
     * 【功能描述：删除文件夹】
     * 【功能详细描述：功能详细描述】
     *
     * @param ftpPath 文件夹的地址
     * @return true 表似成功，false 失败
     * @throws IOException
     * @see 【类、类#方法、类#成员】
     */
    public boolean iterateDelete(String ftpPath) throws Exception
    {
        //注意编码格式
        ftp.setControlEncoding("utf-8");
        FTPClientConfig conf = new FTPClientConfig(FTPClientConfig.SYST_UNIX);
        //中文
        conf.setServerLanguageCode("zh");
        FTPFile[] files = ftp.listFiles(ftpPath);
        boolean flag;
        for (FTPFile f : files)
        {
            String fileName = new String(f.getName().getBytes("utf-8"), "iso-8859-1");
            String path = ftpPath + "/" + fileName;
            if (f.isFile())
            {
                // 是文件就删除文件  
                boolean deleteFile = ftp.deleteFile(path);
                /*if (!deleteFile)
                {
                    throw new Exception("删除失败,请人工处理");
                }*/
            }
            else if (f.isDirectory())
            {
                iterateDelete(path);
            }
        }
        // 每次删除文件夹以后就去查看该文件夹下面是否还有文件，没有就删除该空文件夹  
        FTPFile[] files2 = ftp.listFiles(ftpPath);
        if (files2.length == 0)
        {
            flag = ftp.removeDirectory(ftpPath);
            /*if (!flag)
            {
                throw new Exception("删除失败,请人工处理");
            }*/
        }
        else
        {
            flag = false;
        }
        return flag;
    }

    /**
     * 【功能描述：删除文件】
     * 【功能详细描述：功能详细描述】
     *
     * @param filePath
     * @return
     * @throws IOException
     * @see 【类、类#方法、类#成员】
     */
    public boolean deleteFile(String filePath) throws IOException
    {
        return ftp.deleteFile(filePath);
    }

    /**
     * FTP getFileList
     *
     * @param rootPath 保存遍历的文件名
     * @param path 遍历目录的路径
     * @throws Exception
     */
    public Map<String, Object> getDeleteFtpFileList(String path, String rootPath) throws Exception
    {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(7);
        try
        {
            //注意编码格式
            ftp.setControlEncoding("utf-8");
            FTPClientConfig conf = new FTPClientConfig(FTPClientConfig.SYST_UNIX);
            //中文
            conf.setServerLanguageCode("zh");
            if (rootPath.equals(path))
            {
                throw new Exception("deleteFileFtpSuccess");
            }
            FTPFile[] listFiles = ftp.listFiles(path);
            String childPath = "";
            if (listFiles.length != 0)
            {
                childPath = listFiles[0].getName();
            }
            String fileName = new String(childPath.getBytes("utf-8"), "iso-8859-1");
            while (!fileName.isEmpty())
            {
                //判断是否是文件夹
                boolean flg = isDirExist(path + "/" + fileName);
                if (flg)
                {
                    this.getDeleteFtpFileList(path + "/" + fileName, rootPath);
                }
                else
                {

                    boolean deleteFile = ftp.deleteFile(path + "/" + fileName);
                    if (!deleteFile)
                    {
                        throw new Exception();
                    }
                    //删除文件
                    this.getDeleteFtpFileList(path, rootPath);
                }
            }
        }
        catch (Exception e)
        {
            throw e;
        }
        /**finally{
         if(rootPath.equals(path)){
         throw new Exception("deleteFileFtpSuccess");
         }else if(deleteResult){
         throw new Exception(path+"/"+deleteChildPath+"文件异常系统无法删除            删除失败");
         }else if(path.length()>rootPath.length()){
         boolean removeDirectory = ftp.removeDirectory(path);
         if(removeDirectory){
         String newPath = path.substring(0,path.lastIndexOf("/"));
         getDeleteFtpFileList(newPath,rootPath);
         }else{
         throw new Exception(path+"文件异常系统无法删除            删除失败");
         }
         }
         }**/
        return map;
    }

    /**
     * 检查FTP文件夹是否存在
     *
     * @param dir
     * @return
     * @throws IOException
     */
    private Boolean isDirExist(String dir) throws IOException
    {
        return ftp.changeWorkingDirectory(dir);
    }

    public boolean startDownZip(FtpParamModel ftpMessage, String tomcatPath, String ftpPath, String ftpZipName)
            throws IOException
    {
        ftp.enterLocalPassiveMode();
        boolean changeDir = ftp.changeWorkingDirectory(ftpPath);
        if (changeDir)
        {
            FileOutputStream fos = new FileOutputStream(tomcatPath);
            ftp.setBufferSize(1024);
            //设置文件类型（二进制）
            ftp.setFileType(FTPClient.BINARY_FILE_TYPE);
            boolean retrieveFile = ftp.retrieveFile(ftpPath + ftpZipName, fos);
            fos.close();
            return retrieveFile;
        }
        else
        {
            return false;
        }

    }

    /**
     * 获取Ftp文件夹大小
     *
     * @param serverPath
     * @return
     */
    public Long getFolderSize(String serverPath)
    {
        //初始化文件夹大小为0byte
        Long folderSize = 0L;
        if (ftp != null)
        {
            try
            {
                ftp.changeWorkingDirectory(serverPath);
                FTPFile[] ftpFiles = ftp.listFiles(serverPath);
                for (FTPFile ftpFile : ftpFiles)
                {
                    if (ftpFile.isDirectory())
                    {
                        folderSize = this.getFolderSize(folderSize, serverPath + "/" + ftpFile.getName());
                    }
                    else
                    {
                        folderSize = folderSize + ftpFile.getSize();
                    }
                }

            }
            catch (IOException e)
            {
                log.error(e.getMessage(), e);
            }
        }
        return folderSize;
    }

    private Long getFolderSize(Long initSize, String serverPath)
    {
        if (ftp != null)
        {
            try
            {
                ftp.changeWorkingDirectory(serverPath);
                FTPFile[] ftpFiles = ftp.listFiles(serverPath);
                for (FTPFile ftpFile : ftpFiles)
                {
                    if (ftpFile.isDirectory())
                    {
                        initSize = this.getFolderSize(initSize, serverPath + "/" + ftpFile.getName());
                    }
                    else
                    {
                        initSize = initSize + ftpFile.getSize();
                    }
                }

            }
            catch (IOException e)
            {
                log.error(e.getMessage(), e);
            }
        }
        return initSize;

    }

}