package com.jettech.basic.ftp;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * 现文件[夹]解压
 *
 * <AUTHOR>
 * @version 1.0
 * @description 现文件[夹]解压
 * @projectName jettong
 * @package com.jettech.basic.ftp
 * @className ZipToFile
 * @date 2022-03-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class ZipToFile
{

    private static final Logger logger = LoggerFactory.getLogger(ZipToFile.class);

    /**
     * 解压到指定目录
     *
     * @param zipPath
     * @param descDir
     */
    public static String unZipFiles(String zipPath, String descDir) throws IOException
    {
        return unZipFiles(new File(zipPath), descDir);
    }

    /**
     * 解压文件到指定目录 解压后的文件名，和之前一致
     *
     * @param zipFile 待解压的zip文件
     * @param descDir 指定目录
     */
    public static String unZipFiles(File zipFile, String descDir) throws IOException
    {
        // 解决中文文件夹乱码
        ZipFile zip = new ZipFile(zipFile, Charset.forName("GBK"));
        File pathFile = new File(descDir);
        if (!pathFile.exists())
        {
            pathFile.mkdirs();
        }

        for (Enumeration<? extends ZipEntry> entries = zip.entries(); entries.hasMoreElements(); )
        {
            ZipEntry entry = entries.nextElement();
            String zipEntryName = entry.getName();
            InputStream in = zip.getInputStream(entry);
            String outPath = (descDir + "/" + zipEntryName).replaceAll("\\*", "/");

            // 判断路径是否存在,不存在则创建文件路径
            File file = new File(outPath.substring(0, outPath.lastIndexOf('/')));
            if (!file.exists())
            {
                file.mkdirs();
            }
            // 判断文件全路径是否为文件夹,如果是上面已经上传,不需要解压
            if (new File(outPath).isDirectory())
            {
                continue;
            }
            // 输出文件路径信息
            FileOutputStream out = new FileOutputStream(outPath);
            byte[] buf1 = new byte[1024];
            int len;
            while ((len = in.read(buf1)) > 0)
            {
                out.write(buf1, 0, len);
            }
            in.close();
            out.close();
        }
        zip.close();
        return descDir;
    }

    /**
     * 解压文件到指定目录 解压后的文件名，和之前一致
     *
     * @param zipFileStr 待解压的zip文件
     * @param descDir 指定目录
     * @return List<String> 文件路径的list
     * @throws IOException IOException
     * <AUTHOR>
     * @date 2022/5/18 9:40
     * @update zxy 2022/5/18 9:40
     * @since 1.0
     */
    public static List<String> unZipFilesToList(String zipFileStr, String descDir) throws IOException
    {
        File zipFile = new File(zipFileStr);
        List<String> list = new ArrayList<>();
        // 解决中文文件夹乱码
        ZipFile zip = new ZipFile(zipFile, Charset.forName("GBK"));
        String name = zip.getName().substring(zip.getName().replace("\\", "/").lastIndexOf('/') + 1,
                zip.getName().lastIndexOf('.'));
        File pathFile = new File(descDir);
        if (!pathFile.exists())
        {
            pathFile.mkdirs();
        }

        for (Enumeration<? extends ZipEntry> entries = zip.entries(); entries.hasMoreElements(); )
        {
            ZipEntry entry = entries.nextElement();
            String zipEntryName = entry.getName();
            InputStream in = zip.getInputStream(entry);
            String outPath = (descDir + "/" + zipEntryName).replaceAll("\\*", "/");

            // 判断路径是否存在,不存在则创建文件路径
            File file = new File(outPath.substring(0, outPath.lastIndexOf('/')));
            if (!file.exists())
            {
                file.mkdirs();
            }
            // 判断文件全路径是否为文件夹,如果是上面已经上传,不需要解压
            if (new File(outPath).isDirectory())
            {
                continue;
            }
            // 输出文件路径信息
            list.add(outPath);
            FileOutputStream out = new FileOutputStream(outPath);
            byte[] buf1 = new byte[1024];
            int len;
            while ((len = in.read(buf1)) > 0)
            {
                out.write(buf1, 0, len);
            }
            in.close();
            out.close();
        }
        zip.close();
        logger.error("descDir:" + descDir);
        logger.error("name:" + name);
        return list;
    }

}
