package com.jettech.basic.salt;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.net.HttpHeaders;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


/**
 * HttpClient方式实现SaltApi
 *
 * <AUTHOR>
 * @version 1.0
 * @description HttpClient方式实现SaltApi
 * @projectName jettong
 * @package com.jettech.basic.salt
 * @className DbComponentsUserInstanceService
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
public class SaltApi implements Serializable
{
    private static final String SUCCESS = "success";

    private static final String MESSAGE = "message";

    private static final String RETURN = "return";

    private static final String CLIENT = "client";

    private static final String WHEEL = "wheel";

    private static final String LOCAL = "local";

    private static final String RUNNER = "runner";

    private static final String FUN = "fun";

    private static final String MATCH = "match";

    private static final String TGT = "tgt";

    private static final String ARG = "arg";

    private static final String EXPR_FORM = "expr_form";

    private static final String PAM = "pam";

    private static final String EAUTH = "eauth";

    private static final String USERNAME_KEY = "username";

    private static final String PASSWORD_KEY = "password";

    private static final String CONTENT_TYPE = "application/json; charset=utf-8";

    private static final String BACKSLASH = "/";

    private String saltApi;

    private String userName;

    private String passWord;

    public SaltApi(String url, String userName, String password)
    {
        //salt_api地址
        this.saltApi = url;
        //salt_api用户名
        this.userName = userName;
        //salt_api密码
        this.passWord = password;
    }

    /**
     * 获取salt的token
     *
     * @return String token
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 15:16
     * @update zxy 2021/10/25 15:16
     * @since 1.0
     */
    public String getToken() throws Exception
    {
        if (this.saltApi.endsWith(BACKSLASH))
        {
            this.saltApi = this.saltApi.substring(0, this.saltApi.length() - 1);
        }

        String url = this.saltApi + "/login";

        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);

        String result = HttpUtil.createPost(url).header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE).body(params.toString())
                .execute().body();

        log.info(result);

        JSONObject loginResult = JSONObject.parseObject(result);

        return loginResult.getJSONArray(RETURN).getJSONObject(0).getString("token");

    }

    /**
     * saltApi根据指定参数执行HttpPostJson方法
     *
     * @param params 参数
     * @return JSONObject 执行结果返回JSONObject
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 14:50
     * @update zxy 2021/10/25 14:50
     * @since 1.0
     */
    private JSONObject saltPostByParams(JSONObject params) throws Exception
    {

        String result = HttpUtil.createPost(this.saltApi + "/run").header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(params.toString()).execute().body();

        log.info(result);

        return JSONObject.parseObject(result);
    }

    /**
     * 测试salt连通性
     *
     * @return Map<String, Object> (success:true为成功，false为失败;message:信息)
     * <AUTHOR>
     * @date 2021/10/25 14:51
     * @update zxy 2021/10/25 14:51
     * @since 1.0
     */
    public Map<String, Object> testConnect()
    {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
        try
        {
            this.getToken();

            map.put(SUCCESS, true);
            map.put(MESSAGE, "连接成功");
        }
        catch (Exception e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "连接失败，失败原因：" + e.getMessage());

            log.error("连接失败，失败原因：{}", e.getMessage());
        }
        return map;
    }

    /**
     * 获取所有服务器的Key状态
     *
     * @return JSONObject 所有服务器状态json
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 14:52
     * @update zxy 2021/10/25 14:52
     * @since 1.0
     */
    public JSONObject listAllKey() throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, WHEEL);
        params.put(FUN, "key.list_all");

        return saltPostByParams(params).getJSONArray(RETURN)
                .getJSONObject(0)
                .getJSONObject("data")
                .getJSONObject(RETURN);

    }

    /**
     * 删除key，被删除key的节点，过一段时间会自动在Unaccepted Keys列表里出现
     *
     * @param minionId 目标机minionId
     * @return boolean 成功返回true，失败返回false
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 14:52
     * @update zxy 2021/10/25 14:52
     * @since 1.0
     */
    public boolean deleteKey(String minionId) throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, WHEEL);
        params.put(FUN, "key.delete");
        params.put(MATCH, minionId);

        return saltPostByParams(params).getJSONArray(RETURN).getJSONObject(0).getJSONObject("data").getBoolean(SUCCESS);
    }

    /**
     * 纳管服务器，接收成功则会在Accepted Keys列表里出现
     *
     * @param minionId 服务器minionId
     * @return boolean 成功返回true，失败返回false
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 14:52
     * @update zxy 2021/10/25 14:52
     * @since 1.0
     */
    public boolean acceptKey(String minionId) throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, WHEEL);
        params.put(FUN, "key.accept");
        params.put(MATCH, minionId);

        return saltPostByParams(params).getJSONArray(RETURN).getJSONObject(0).getJSONObject("data").getBoolean(SUCCESS);
    }

    /**
     * 取消纳管服务器，取消成功则会在Unaccepted Keys列表里出现
     *
     * @param minionId 服务器minionId
     * @return boolean 成功返回true，失败返回false
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 14:52
     * @update zxy 2021/10/25 14:52
     * @since 1.0
     */
    public boolean rejectKey(String minionId) throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, WHEEL);
        params.put(FUN, "key.reject");
        params.put(MATCH, minionId);

        return saltPostByParams(params).getJSONArray(RETURN).getJSONObject(0).getJSONObject("data").getBoolean(SUCCESS);
    }

    /**
     * 根据指定minionId或"*"，获取grains信息
     *
     * @param minionId minionId，*代表所有
     * @return JSONObject grains信息
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 14:53
     * @update zxy 2021/10/25 14:53
     * @since 1.0
     */
    public JSONObject getGrainsByTgt(String minionId) throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, LOCAL);
        params.put(FUN, "grains.items");
        params.put(TGT, minionId);

        return saltPostByParams(params).getJSONArray(RETURN).getJSONObject(0);
    }

    /**
     * 根据指定minionId，获取grains信息
     *
     * @param minionId minionId，多个minionId之间逗号
     * @return JSONObject grains信息
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 14:54
     * @update zxy 2021/10/25 14:54
     * @since 1.0
     */
    public JSONObject getGrainsByTgts(String minionId) throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, LOCAL);
        params.put(FUN, "grains.items");
        params.put(EXPR_FORM, "list");
        params.put(TGT, minionId);

        return saltPostByParams(params).getJSONArray(RETURN).getJSONObject(0);
    }

    /**
     * 根据指定minionId和指定item，获取grains信息
     *
     * @param minionId minionId minionId，*代表所有
     * @param item 指定grains的item(例：cpu_model)
     * @return JSONObject grains信息
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 14:54
     * @update zxy 2021/10/25 14:54
     * @since 1.0
     */
    public JSONObject getGrainsByItem(String minionId, String item) throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, LOCAL);
        params.put(FUN, "grains.item");
        params.put(TGT, minionId);
        params.put(ARG, item);

        return saltPostByParams(params).getJSONArray(RETURN).getJSONObject(0);
    }

    /**
     * 返回所有服务器的status信息
     *
     * @return JSONObject 所有服务器的status信息
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 14:55
     * @update zxy 2021/10/25 14:55
     * @since 1.0
     */
    public JSONObject getRunerStatus() throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, RUNNER);
        params.put(FUN, "manage.status");

        return saltPostByParams(params).getJSONArray(RETURN).getJSONObject(0);
    }

    /**
     * 通过指定minionId、模块名，同步执行操作
     *
     * @param minionId minionId
     * @param fun 指定模块名(例：test.ping)
     * @return JSONObject 执行结果
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 15:13
     * @update zxy 2021/10/25 15:13
     * @since 1.0
     */
    public JSONObject remoteNoargExecution(String minionId, String fun) throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, LOCAL);
        params.put(FUN, fun);
        params.put(TGT, minionId);

        return saltPostByParams(params).getJSONArray(RETURN).getJSONObject(0);
    }

    /**
     * 通过指定minionId、模块名，同步执行操作，支持多个minionId
     *
     * @param minionId minionId,多个minionId之间逗号隔开
     * @param fun 指定模块名(例：test.ping)
     * @return JSONObject 执行结果
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 15:12
     * @update zxy 2021/10/25 15:12
     * @since 1.0
     */
    public JSONObject remoteNoargExecutionForTags(String minionId, String fun) throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, LOCAL);
        params.put(EXPR_FORM, "list");
        params.put(FUN, fun);
        params.put(TGT, minionId);

        return saltPostByParams(params).getJSONArray(RETURN).getJSONObject(0);
    }

    /**
     * 通过指定minionId、模块名、模块对应的参数，同步执行操作
     *
     * @param minionId minionId,多个minionId之间逗号隔开
     * @param fun fun 指定模块名(例：test.ping)
     * @param arg 模块对应的参数，例：['whoami','runas=zxy'],注：参数列表的元素个数由模块决定
     * @return JSONObject 执行结果
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 15:11
     * @update zxy 2021/10/25 15:11
     * @since 1.0
     */
    public JSONObject remoteExecutionNotgt(String minionId, String fun, List<String> arg) throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, LOCAL);
        params.put(EXPR_FORM, "list");
        params.put(FUN, fun);
        params.put(TGT, minionId);
        params.put(ARG, arg);

        JSONObject ret = saltPostByParams(params).getJSONArray(RETURN).getJSONObject(0);
        if (ret.containsKey(minionId) && "[YOU HAVE NEW MAIL]".equals(ret.get(minionId).toString()))
        {
            ret.put(minionId, "");
        }
        return ret;
    }

    /**
     * 指定minionId或*，模块名，模块对应的参数，异步执行操作
     *
     * @param minionId minionId
     * @param fun fun 指定模块名(例：test.ping)
     * @param arg 模块对应的参数，例：['whoami','runas=zxy'],注：参数列表的元素个数由模块决定
     * @return String
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 15:11
     * @update zxy 2021/10/25 15:11
     * @since 1.0
     */
    public String remoteExecution(String minionId, String fun, List<String> arg) throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, "local_async");
        params.put(FUN, fun);
        params.put(TGT, minionId);
        params.put(ARG, arg);

        return saltPostByParams(params).getJSONArray(RETURN).getJSONObject(0).getString("jid");
    }

    /**
     * 指定minionId执行state.sls模块
     *
     * @param minionId minionId,多个minionId之间逗号隔开
     * @param arg 模块对应的参数
     * @return JSONObject 执行结果
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 14:59
     * @update zxy 2021/10/25 14:59
     * @since 1.0
     */
    public JSONObject stateSls(String minionId, String arg) throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, "local_async");
        params.put(FUN, "state.sls");
        params.put(EXPR_FORM, "list");
        params.put(TGT, minionId);
        params.put(ARG, arg);

        return saltPostByParams(params).getJSONArray(RETURN).getJSONObject(0);
    }

    /**
     * 根据jid查看job执行状态
     *
     * @param jid jobID号
     * @return boolean true：完成，false：未完成
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 14:58
     * @update zxy 2021/10/25 14:58
     * @since 1.0
     */
    public boolean jobsStatus(String jid) throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, RUNNER);
        params.put(FUN, "jobs.exit_success");
        params.put("jid", jid);

        JSONObject resultJson = saltPostByParams(params).getJSONArray(RETURN).getJSONObject(0);

        for (String key : resultJson.keySet())
        {
            // 获得key
            // 根据key获得value, value也可以是JSONObject,JSONArray,使用对应的参数接收即可  
            if (!resultJson.getBoolean(key))
            {
                return false;
            }
        }
        return true;
    }

    /**
     * 根据jid获取job返回详情
     *
     * @param jid jobID号
     * @return JSONObject job返回详情
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 14:58
     * @update zxy 2021/10/25 14:58
     * @since 1.0
     */
    public JSONObject jobsDetail(String jid) throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, RUNNER);
        params.put(FUN, "jobs.lookup_jid");
        params.put("jid", jid);

        return saltPostByParams(params).getJSONArray(RETURN).getJSONObject(0);
    }

    /**
     * 获取与Master连接中的minion列表
     *
     * @return JSONArray minion列表
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/25 14:57
     * @update zxy 2021/10/25 14:57
     * @since 1.0
     */
    public JSONArray isConnected() throws Exception
    {
        JSONObject params = new JSONObject();
        params.put(EAUTH, PAM);
        params.put(USERNAME_KEY, this.userName);
        params.put(PASSWORD_KEY, this.passWord);
        params.put(CLIENT, WHEEL);
        params.put(FUN, "minions.connected");

        return saltPostByParams(params).getJSONArray(RETURN)
                .getJSONObject(0)
                .getJSONObject("data")
                .getJSONArray(RETURN);
    }
}