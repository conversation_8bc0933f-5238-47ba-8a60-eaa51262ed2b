<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jettong-util</artifactId>
        <groupId>com.jettech.basic</groupId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>jettong-salt-starter</artifactId>
    <description>SaltStack api</description>
    <name>${project.artifactId}</name>

    <dependencies>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
        </dependency>
    </dependencies>


    <!--    &lt;!&ndash; 打包sdk &ndash;&gt;-->
    <!--    <build>-->
    <!--        <plugins>-->
    <!--            <plugin>-->
    <!--                <groupId>org.apache.maven.plugins</groupId>-->
    <!--                <artifactId>maven-assembly-plugin</artifactId>-->
    <!--                <configuration>-->
    <!--                    <appendAssemblyId>false</appendAssemblyId>-->
    <!--                    <finalName>${artifactId}-sdk-${version}</finalName>-->
    <!--                    <descriptorRefs>-->
    <!--                        &lt;!&ndash; 将依赖的jar包中的class文件打进生成的jar包&ndash;&gt;-->
    <!--                        <descriptorRef>jar-with-dependencies</descriptorRef>-->
    <!--                    </descriptorRefs>-->
    <!--                    <archive>-->
    <!--                        <manifest>-->
    <!--                            <addClasspath>true</addClasspath>-->
    <!--                        </manifest>-->
    <!--                    </archive>-->
    <!--                </configuration>-->
    <!--                <executions>-->
    <!--                    <execution>-->
    <!--                        <id>make-assembly</id>-->
    <!--                        <phase>package</phase>-->
    <!--                        <goals>-->
    <!--                            <goal>assembly</goal>-->
    <!--                        </goals>-->
    <!--                    </execution>-->
    <!--                </executions>-->
    <!--            </plugin>-->
    <!--        </plugins>-->
    <!--    </build>-->
</project>