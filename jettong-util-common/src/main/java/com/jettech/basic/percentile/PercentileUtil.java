package com.jettech.basic.percentile;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 百分位数计算工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 百分位数计算工具类
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.utils
 * @className PercentileUtil
 * @date 2022/3/22 15:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class PercentileUtil implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 取一个Integer集合的百分位数
     *
     * @param integers Integer 类型集合
     * @param percentile 百分位 90%传0.9
     * @param digit 保留的小数位
     * @return Double 百分位数
     * <AUTHOR>
     * @date 2022/3/22 15:57
     * @update zxy 2022/3/22 15:57
     * @since 1.0
     */
    public static Double getIntPercentile(List<Integer> integers, double percentile, int digit)
    {
        return getIntPercentile(integers.stream().filter(Objects::nonNull).mapToInt(i -> i).toArray(), percentile,
                digit);
    }

    /**
     * 取一个int集合的百分位数
     *
     * @param array int 类型数组
     * @param percentile 百分位 90%传0.9
     * @param digit 保留的小数位
     * @return Double 百分位数
     * <AUTHOR>
     * @date 2022/3/22 15:57
     * @update zxy 2022/3/22 15:57
     * @since 1.0
     */
    public static Double getIntPercentile(int[] array, double percentile, int digit)
    {
        return getDoublePercentile(Arrays.stream(array).asDoubleStream().toArray(), percentile, digit);
    }

    /**
     * 取一个Integer集合的百分位数
     *
     * @param longs Long 类型集合
     * @param percentile 百分位 90%传0.9
     * @param digit 保留的小数位
     * @return Double 百分位数
     * <AUTHOR>
     * @date 2022/3/22 15:57
     * @update zxy 2022/3/22 15:57
     * @since 1.0
     */
    public static Double getLongPercentile(List<Long> longs, double percentile, int digit)
    {
        return getLongPercentile(longs.stream().filter(Objects::nonNull).mapToLong(i -> i).toArray(), percentile,
                digit);
    }

    /**
     * 取一个long集合的百分位数
     *
     * @param array long 类型数组
     * @param percentile 百分位 90%传0.9
     * @param digit 保留的小数位
     * @return Double 百分位数
     * <AUTHOR>
     * @date 2022/3/22 15:57
     * @update zxy 2022/3/22 15:57
     * @since 1.0
     */
    public static Double getLongPercentile(long[] array, double percentile, int digit)
    {
        return getDoublePercentile(Arrays.stream(array).asDoubleStream().toArray(), percentile, digit);
    }

    /**
     * 取一个Double集合的百分位数
     *
     * @param doubles double 类型集合
     * @param percentile 百分位 90%传0.9
     * @param digit 保留的小数位
     * @return Double 百分位数
     * <AUTHOR>
     * @date 2022/3/22 15:57
     * @update zxy 2022/3/22 15:57
     * @since 1.0
     */
    public static Double getDoublePercentile(List<Double> doubles, double percentile, int digit)
    {
        return getDoublePercentile(doubles.stream().filter(Objects::nonNull).mapToDouble(i -> i).toArray(), percentile,
                digit);
    }

    /**
     * 取一个double数组的百分位数
     *
     * @param array double 类型数组
     * @param percentile 百分位 90%传0.9
     * @param digit 保留的小数位
     * @return Double 百分位数
     * <AUTHOR>
     * @date 2022/3/22 15:57
     * @update zxy 2022/3/22 15:57
     * @since 1.0
     */
    public static Double getDoublePercentile(double[] array, double percentile, int digit)
    {
        if (array == null || array.length == 0)
        {
            return null;
        }
        if (array.length == 1)
        {
            return new BigDecimal(String.valueOf(array[0])).setScale(digit, RoundingMode.HALF_EVEN)
                    .stripTrailingZeros().doubleValue();
        }
        Arrays.sort(array);
        BigDecimal x = new BigDecimal(array.length - 1).multiply(new BigDecimal(String.valueOf(percentile)));
        int i = x.intValue();
        BigDecimal j = x.subtract(new BigDecimal(i));
        BigDecimal r = (new BigDecimal(1).subtract(j)).multiply(new BigDecimal(String.valueOf(array[i])))
                .add(j.multiply(new BigDecimal(String.valueOf(array[i + 1]))));
        return r.setScale(digit, RoundingMode.HALF_EVEN).stripTrailingZeros().doubleValue();
    }

}
