package com.jettech.basic.md5;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * ClassName: Md5Utils
 * Function:  ADD FUNCTION.
 * date: 2017年12月5日 下午6:40:37
 *
 * <AUTHOR>
 */
@Slf4j
public class Md5Utils
{

    private static final char[] HEX_DIGITS =
            {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
    private static final String UTF_8 = "UTF-8";

    public MessageDigest initMessageDigest()
    {
        MessageDigest messageDigest = null;
        try
        {
            messageDigest = MessageDigest.getInstance("MD5");
        }
        catch (NoSuchAlgorithmException e)
        {
            log.error("initMessageDigest NoSuchAlgorithmException", e);
        }
        return messageDigest;
    }

    /**
     * @param file 文件
     * @return Md5码
     * @throws IOException IOException
     */
    public String getFileMd5String(File file) throws IOException
    {
        MessageDigest messageDigest = this.initMessageDigest();
        try (FileInputStream fis = new FileInputStream(file))
        {
            byte[] buffer = new byte[1024];
            int numRead;
            while ((numRead = fis.read(buffer)) > 0)
            {
                messageDigest.update(buffer, 0, numRead);
            }
        }
        catch (Exception e)
        {
            log.error("getFileMd5String Exception", e);
        }

        return bufferToHex(messageDigest.digest());
    }

    private String bufferToHex(byte[] bytes)
    {
        return bufferToHex(bytes, 0, bytes.length);
    }

    private String bufferToHex(byte[] bytes, int m, int n)
    {
        StringBuilder stringBuffer = new StringBuilder(2 * n);
        int k = m + n;
        for (int l = m; l < k; ++l)
        {
            appendHexPair(bytes[l], stringBuffer);
        }
        return stringBuffer.toString();
    }

    private void appendHexPair(byte bt, StringBuilder stringBuffer)
    {
        char c0 = HEX_DIGITS[((bt & 0xF0) >> 4)];
        char c1 = HEX_DIGITS[(bt & 0xF)];
        stringBuffer.append(c0);
        stringBuffer.append(c1);
    }


    public static String md5(String src)
    {
        return md5(src, UTF_8);
    }

    public static String md5(String src, String charset)
    {
        try
        {
            byte[] strTemp = charset == null || charset.isEmpty() ? src.getBytes() : src.getBytes(charset);
            MessageDigest mdTemp = MessageDigest.getInstance("MD5");
            mdTemp.update(strTemp);

            byte[] md = mdTemp.digest();
            int j = md.length;
            char[] str = new char[j * 2];
            int k = 0;

            for (byte byte0 : md)
            {
                str[k++] = HEX_DIGITS[byte0 >>> 4 & 0xf];
                str[k++] = HEX_DIGITS[byte0 & 0xf];
            }

            return new String(str);
        }
        catch (Exception e)
        {
            throw new RuntimeException("MD5 encrypt error:", e);
        }
    }
}
