package com.jettech.basic.image;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;


/**
 * 图片压缩工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 图片压缩工具类
 * @projectName jettong-enterprises
 * @package com.jettech.basic.image
 * @className ImageCompressUtil
 * @date 2022/4/18 19:48
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
public class ImageCompressUtil
{

    /**
     * 直接指定压缩后的宽高(先保存原文件，再压缩、上传)
     *
     * @param oldFile 要进行压缩的文件全路径
     * @param width 压缩后的宽度
     * @param height 压缩后的高度
     * @param smallIcon 文件名的小小后缀(注意，非文件后缀名称),如压缩文件名是yasuo.jpg,则压缩后文件名是yasuo(+smallIcon).jpg
     * @return String 返回压缩后的文件的全路径
     * <AUTHOR>
     * @date 2022/4/19 15:59
     * @update zxy 2022/4/19 15:59
     * @since 1.0
     */
    public static String zipImageFile(String oldFile, int width, int height, String smallIcon)
    {
        if (oldFile == null)
        {
            return null;
        }
        String newImage = null;
        try
        {
            // 对服务器上的临时文件进行处理
            Image srcFile = ImageIO.read(new File(oldFile));
            // 宽,高设定
            BufferedImage tag = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            tag.getGraphics().drawImage(srcFile, 0, 0, width, height, null);
            String filePyrex = oldFile.substring(0, oldFile.indexOf('.'));
            // 压缩后的文件名
            newImage = filePyrex + smallIcon + oldFile.substring(filePyrex.length());
            // 压缩之后临时存放位置
            String formatName = newImage.substring(newImage.lastIndexOf(".") + 1);
            ImageIO.write(tag, formatName, new File(newImage));
        }
        catch (FileNotFoundException e)
        {
            log.error("压缩文件失败，原因：未找到要压缩的文件", e);
        }
        catch (IOException e)
        {
            log.error("压缩文件失败，原因:{}", e.getMessage(), e);
        }
        return newImage;
    }

    /**
     * 等比例压缩算法：
     * 算法思想：根据压缩基数和压缩比来压缩原图，生产一张图片效果最接近原图的缩略图
     *
     * @param srcURL 原图地址
     * @param deskURL 缩略图地址
     * @param comBase 压缩基数
     * @param scale 压缩限制(宽/高)比例  一般用1： 当scale>=1,缩略图height=comBase,width按原图宽高比例;若scale<1,缩略图width=comBase,height按原图宽高比例
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2022/4/19 16:03
     * @update zxy 2022/4/19 16:03
     * @since 1.0
     */
    public static void saveMinPhoto(String srcURL, String deskURL, double comBase,
            double scale) throws Exception
    {
        File srcFile = new java.io.File(srcURL);
        Image src = ImageIO.read(srcFile);
        int srcHeight = src.getHeight(null);
        int srcWidth = src.getWidth(null);
        // 缩略图高
        int deskHeight = 0;
        // 缩略图宽
        int deskWidth = 0;
        double srcScale = (double) srcHeight / srcWidth;
        // 缩略图宽高算法
        if ((double) srcHeight > comBase || (double) srcWidth > comBase)
        {
            if (srcScale >= scale || 1 / srcScale > scale)
            {
                if (srcScale >= scale)
                {
                    deskHeight = (int) comBase;
                    deskWidth = srcWidth * deskHeight / srcHeight;
                }
                else
                {
                    deskWidth = (int) comBase;
                    deskHeight = srcHeight * deskWidth / srcWidth;
                }
            }
            else
            {
                if ((double) srcHeight > comBase)
                {
                    deskHeight = (int) comBase;
                    deskWidth = srcWidth * deskHeight / srcHeight;
                }
                else
                {
                    deskWidth = (int) comBase;
                    deskHeight = srcHeight * deskWidth / srcWidth;
                }
            }
        }
        else
        {
            deskHeight = srcHeight;
            deskWidth = srcWidth;
        }
        BufferedImage tag = new BufferedImage(deskWidth, deskHeight, BufferedImage.TYPE_3BYTE_BGR);
        // 绘制缩小后的图
        tag.getGraphics().drawImage(src, 0, 0, deskWidth, deskHeight, null);
        // 输出到文件流
        String formatName = deskURL.substring(deskURL.lastIndexOf(".") + 1);
        ImageIO.write(tag, formatName, new File(deskURL));
    }

}