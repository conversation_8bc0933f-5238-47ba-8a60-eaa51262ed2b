package com.jettech.basic.cache.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;

import java.time.Duration;

/**
 * 缓存 key 封装
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CacheKey
{
    /**
     * redis key
     */
    @NonNull
    private String key;
    /**
     * 失效时间 秒
     */
    private Duration expire;

    public CacheKey(final @NonNull String key)
    {
        this.key = key;
    }


}
