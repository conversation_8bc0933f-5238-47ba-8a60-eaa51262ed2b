package com.jettech.basic.map;

import com.google.common.collect.Maps;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;

/**
 * Map操作工具类
 *
 * <AUTHOR>
 */
public final class MapUtil
{

    private MapUtil()
    {

    }

    /**
     * 将一个 JavaBean 对象转化为一个  Map
     *
     * @param bean 要转化的JavaBean 对象
     * @return 转化出来的  Map 对象
     * @throws IntrospectionException 如果分析类属性失败
     * @throws IllegalAccessException 如果实例化 JavaBean 失败
     * @throws InvocationTargetException 如果调用属性的 setter 方法失败
     */
    public static Map<String, Object> convertBean(Object bean)
            throws IntrospectionException, IllegalAccessException, InvocationTargetException
    {
        Class<? extends Object> type = bean.getClass();
        Map<String, Object> returnMap = Maps.newHashMapWithExpectedSize(7);
        BeanInfo beanInfo = Introspector.getBeanInfo(type);

        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        for (int i = 0; i < propertyDescriptors.length; i++)
        {
            PropertyDescriptor descriptor = propertyDescriptors[i];
            String propertyName = descriptor.getName();
            if (!"class".equals(propertyName))
            {
                Method readMethod = descriptor.getReadMethod();
                Object result = readMethod.invoke(bean);
                if (result != null)
                {
                    returnMap.put(propertyName, result);
                }
                else
                {
                    returnMap.put(propertyName, "");
                }
            }
        }
        return returnMap;
    }

    /**
     * 利用反射机制把List转换为Map
     *
     * @param list 要转换的List
     * @param fieldName4Key Map中转换list的key
     * @param c class
     * @param <K> K
     * @param <V> V
     * @return Map<K, V> map
     */
    public static <K, V> Map<K, V> list2Map2(List<V> list, String fieldName4Key, Class<V> c)
    {
        Map<K, V> map = Maps.newHashMapWithExpectedSize(7);
        if (list != null)
        {
            try
            {
                PropertyDescriptor propDesc = new PropertyDescriptor(fieldName4Key, c);
                Method methodGetKey = propDesc.getReadMethod();
                for (int i = 0; i < list.size(); i++)
                {
                    V value = list.get(i);
                    @SuppressWarnings("unchecked")
                    K key = (K) methodGetKey.invoke(list.get(i));
                    map.put(key, value);
                }
            }
            catch (IntrospectionException | IllegalAccessException | IllegalArgumentException
                    | InvocationTargetException e)
            {
                throw new IllegalArgumentException("field can't match the key!");
            }
        }

        return map;
    }
}
