package com.jettech.basic.base.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 包括id、create_time、created_by、updated_by、update_time、label、parent_id、sort_value 字段的表继承的树形实体
 *
 * <AUTHOR>
 * @date 2021/11/29
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
public class TreeDtoEntity<E, T extends Serializable> extends Entity<T>
{

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    protected String name;

    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    protected T parentId;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序号")
    protected Integer sort;


    @ApiModelProperty(value = "子节点", hidden = true)
    protected List<E> children;


    /**
     * 初始化子类
     */
    public void initChildren()
    {
        if (getChildren() == null)
        {
            this.setChildren(new ArrayList<>());
        }
    }
}
