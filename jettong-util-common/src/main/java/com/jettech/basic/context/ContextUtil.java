package com.jettech.basic.context;

import cn.hutool.core.convert.Convert;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.jettech.basic.utils.StrPool;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 获取当前线程变量中的 用户id、用户昵称、租户编码、账号、机构id、机构名称等信息
 *
 * <AUTHOR>
 * @version 1.0
 * @description 获取当前线程变量中的 用户id、用户昵称、租户编码、账号、机构id、机构名称等信息
 * @projectName jettong
 * @package com.jettech.basic.context
 * @className ContextUtil
 * @date 2021/9/15 9:40
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public final class ContextUtil
{
    private ContextUtil()
    {
    }

    /**
     * 支持多线程传递参数
     */
    private static final ThreadLocal<Map<String, String>> THREAD_LOCAL = new TransmittableThreadLocal<>();

    public static void putAll(Map<String, String> map)
    {
        map.forEach((k, v) ->
        {
            set(k, v);
        });
    }

    public static void set(String key, Object value)
    {
        Map<String, String> map = getLocalMap();
        map.put(key, value == null ? StrPool.EMPTY : value.toString());
    }

    public static <T> T get(String key, Class<T> type)
    {
        Map<String, String> map = getLocalMap();
        return Convert.convert(type, map.get(key));
    }

    public static <T> T get(String key, Class<T> type, Object def)
    {
        Map<String, String> map = getLocalMap();
        return Convert.convert(type, map.getOrDefault(key, String.valueOf(def == null ? StrPool.EMPTY : def)));
    }

    public static String get(String key)
    {
        Map<String, String> map = getLocalMap();
        return map.getOrDefault(key, StrPool.EMPTY);
    }

    public static Map<String, String> getLocalMap()
    {
        Map<String, String> map = THREAD_LOCAL.get();
        if (map == null)
        {
            map = new ConcurrentHashMap<>(10);
            THREAD_LOCAL.set(map);
        }
        return map;
    }

    public static void setLocalMap(Map<String, String> localMap)
    {
        THREAD_LOCAL.set(localMap);
    }


    /**
     * 是否为boot项目，默认false
     *
     * @return Boolean 是否boot项目
     * <AUTHOR>
     * @date 2021/9/15 11:12
     * @update zxy 2021/9/15 11:12
     * @since 1.0
     */
    public static Boolean getBoot()
    {
        return get(ContextConstants.IS_BOOT, Boolean.class, false);
    }

    public static void setBoot(Boolean val)
    {
        set(ContextConstants.IS_BOOT, val);
    }

    /**
     * 获取当前登录用户id
     *
     * @return Long 当前登录用户id
     * <AUTHOR>
     * @date 2021/9/15 11:13
     * @update zxy 2021/9/15 11:13
     * @since 1.0
     */
    public static Long getUserId()
    {
        return get(ContextConstants.JWT_KEY_USER_ID, Long.class, 0L);
    }

    /**
     * 当前登录用户id，字符串类型
     *
     * @return String 当前登录用户id
     * <AUTHOR>
     * @date 2021/9/15 11:14
     * @update zxy 2021/9/15 11:14
     * @since 1.0
     */
    public static String getUserIdStr()
    {
        return String.valueOf(getUserId());
    }

    public static void setUserId(Long userId)
    {
        set(ContextConstants.JWT_KEY_USER_ID, userId);
    }

    public static void setUserId(String userId)
    {
        set(ContextConstants.JWT_KEY_USER_ID, userId);
    }

    /**
     * 当前登录用户账号
     *
     * @return String 登录账号
     * <AUTHOR>
     * @date 2021/9/15 11:14
     * @update zxy 2021/9/15 11:14
     * @since 1.0
     */
    public static String getUserAccount()
    {
        return get(ContextConstants.JWT_KEY_USER_ACCOUNT, String.class);
    }

    public static void setUserAccount(String userAccount)
    {
        set(ContextConstants.JWT_KEY_USER_ACCOUNT, userAccount);
    }


    /**
     * 获取当前登录用户名称
     *
     * @return String 当前登录用户名称
     * <AUTHOR>
     * @date 2021/9/15 11:15
     * @update zxy 2021/9/15 11:15
     * @since 1.0
     */
    public static String getUserName()
    {
        return get(ContextConstants.JWT_KEY_USER_NAME, String.class);
    }

    public static void setUserName(String userName)
    {
        set(ContextConstants.JWT_KEY_USER_NAME, userName);
    }

    /**
     * 获取当前登录用户组织机构id
     *
     * @return Long 当前登录用户组织机构id
     * <AUTHOR>
     * @date 2021/9/15 11:18
     * @update zxy 2021/9/15 11:18
     * @since 1.0
     */
    public static Long getOrgId()
    {
        return get(ContextConstants.JWT_KEY_ORG_ID, Long.class);
    }

    /**
     * 获取当前登录用户组织机构id，字符串类型
     *
     * @return String 当前登录用户组织机构id，字符串类型
     * <AUTHOR>
     * @date 2021/9/15 11:18
     * @update zxy 2021/9/15 11:18
     * @since 1.0
     */
    public static String getOrgIdStr()
    {
        return String.valueOf(getOrgId());
    }

    public static void setOrgId(Long orgId)
    {
        set(ContextConstants.JWT_KEY_ORG_ID, orgId);
    }

    public static void setOrgId(String orgId)
    {
        set(ContextConstants.JWT_KEY_ORG_ID, orgId);
    }

    public static void setOrgIdStr(String orgId)
    {
        set(ContextConstants.JWT_KEY_ORG_ID, orgId);
    }


    /**
     * 获取当前登录用户token
     *
     * @return String 当前登录用户token
     * <AUTHOR>
     * @date 2021/9/15 11:19
     * @update zxy 2021/9/15 11:19
     * @since 1.0
     */
    public static String getToken()
    {
        return get(ContextConstants.BEARER_HEADER_KEY, String.class);
    }

    public static void setToken(String token)
    {
        set(ContextConstants.BEARER_HEADER_KEY, token);
    }

    /**
     * 获取当前登录用户租户编码
     *
     * @return String 当前登录用户租户编码
     * <AUTHOR>
     * @date 2021/9/15 11:20
     * @update zxy 2021/9/15 11:20
     * @since 1.0
     */
    public static String getTenant()
    {
        return get(ContextConstants.JWT_KEY_TENANT, String.class, StrPool.EMPTY);
    }

    public static void setTenant(String val)
    {
        set(ContextConstants.JWT_KEY_TENANT, val);
    }

    public static String getSubTenant()
    {
        return get(ContextConstants.JWT_KEY_SUB_TENANT, String.class, StrPool.EMPTY);
    }

    public static void setSubTenant(String val)
    {
        set(ContextConstants.JWT_KEY_SUB_TENANT, val);
    }

    public static String getClientId()
    {
        return get(ContextConstants.JWT_KEY_CLIENT_ID, String.class);
    }

    public static void setClientId(String val)
    {
        set(ContextConstants.JWT_KEY_CLIENT_ID, val);
    }

    /**
     * 获取灰度版本号
     *
     * @return 灰度版本号
     */
    public static String getGrayVersion()
    {
        return get(ContextConstants.GRAY_VERSION, String.class);
    }

    public static void setGrayVersion(String val)
    {
        set(ContextConstants.GRAY_VERSION, val);
    }

    public static void remove()
    {
        THREAD_LOCAL.remove();
    }

}
