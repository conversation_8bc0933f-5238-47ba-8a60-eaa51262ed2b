package com.jettech.basic.context;

import lombok.Data;

import java.io.Serializable;

/**
 * 线程变量封装的参数
 *
 * <AUTHOR>
 * @version 1.0
 * @description 线程变量封装的参数
 * @projectName jettong
 * @package com.jettech.basic.context
 * @className ThreadLocalParam
 * @date 2021/9/15 9:40
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class ThreadLocalParam implements Serializable
{
    private Boolean boot;
    private String tenant;
    private Long userId;
    private String userName;
    private String userAccount;
    private Long orgId;
}
