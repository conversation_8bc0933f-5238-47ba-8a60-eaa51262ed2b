package com.jettech.basic.date;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.LinkedList;
import java.util.List;

/**
 * 月计算工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 月计算工具类
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.utils
 * @className MonthUtil
 * @date 2022/3/22 17:13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class MonthUtil implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**
     * 获取两个时间段内所有月的最后一天日期.不包含结束时间所在月的最后一天日期（结束时间日期和月最后一天日期相同时会返回）
     *
     * @param beginDateTime 开始时间
     * @param endDateTime 结束时间
     * @return List<LocalDate> 月最后一天日期
     * <AUTHOR>
     * @date 2022/3/22 17:16
     * @update zxy 2022/3/22 17:16
     * @since 1.0
     */
    public static List<LocalDate> getMonthLocalDate(LocalDateTime beginDateTime, LocalDateTime endDateTime)
    {
        return getMonthLocalDate(beginDateTime.toLocalDate(), endDateTime.toLocalDate());
    }

    /**
     * 获取两个日期段内所有月的最后一天日期.不包含结束日期所在月的最后一天日期（结束日期和月的最后一天日期相同时会返回）
     *
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @return List<LocalDate> 周末日期
     * <AUTHOR>
     * @date 2022/3/22 17:16
     * @update zxy 2022/3/22 17:16
     * @since 1.0
     */
    public static List<LocalDate> getMonthLocalDate(LocalDate beginDate, LocalDate endDate)
    {
        List<LocalDate> month = new LinkedList<>();

        // 开始月最后一天
        LocalDate beginMonth = getMonthDate(beginDate);

        // 结束月最后一天
        LocalDate endMonth = getMonthDate(endDate);

        //判断是否同一月
        if (beginMonth.isEqual(endMonth))
        {
            if (endDate.isEqual(endMonth))
            {
                month.add(endMonth);
            }
            return month;
        }
        while (!beginMonth.isAfter(endDate))
        {
            month.add(beginMonth);
            beginMonth = beginMonth.plusMonths(1);
        }
        return month;
    }

    /**
     * 获取两个时间段内所有月的最后一天日期，不包含结束时间所在月的最后一天日期（结束时间日期和月的最后一天日期相同时会返回）
     *
     * @param beginDateTime 开始时间
     * @param endDateTime 结束时间
     * @param dateTimeFormatter 格式化
     * @return List<String> 周字符串集合
     * <AUTHOR>
     * @date 2022/3/22 18:31
     * @update zxy 2022/3/22 18:31
     * @since 1.0
     */
    public static List<String> getMonthString(LocalDateTime beginDateTime, LocalDateTime endDateTime,
            DateTimeFormatter dateTimeFormatter)
    {
        return getMonthString(beginDateTime.toLocalDate(), endDateTime.toLocalDate(), dateTimeFormatter);
    }

    /**
     * 获取两个日期段内所有月的最后一天日期，不包含结束日期所在月的最后一天日期（结束日期和月的最后一天日期相同时会返回）
     *
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @param dateTimeFormatter 格式化
     * @return List<String> 月字符串集合
     * <AUTHOR>
     * @date 2022/3/22 18:31
     * @update zxy 2022/3/22 18:31
     * @since 1.0
     */
    public static List<String> getMonthString(LocalDate beginDate, LocalDate endDate,
            DateTimeFormatter dateTimeFormatter)
    {
        if (null == dateTimeFormatter)
        {
            dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        }
        List<LocalDate> monthLocalDate = getMonthLocalDate(beginDate, endDate);
        List<String> monthStr = new LinkedList<>();
        if (monthLocalDate.isEmpty())
        {
            return monthStr;
        }
        for (LocalDate localDate : monthLocalDate)
        {
            monthStr.add(localDate.format(dateTimeFormatter));
        }
        return monthStr;
    }

    /**
     * 获取日期所在的月最后一天日期
     *
     * @param date 日期
     * @return LocalDate 月最后一天日期
     * <AUTHOR>
     * @date 2022/3/23 11:48
     * @update zxy 2022/3/23 11:48
     * @since 1.0
     */
    public static LocalDate getMonthDate(LocalDate date)
    {
        return date.with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 获取时间所在的月最后一天日期
     *
     * @param localDateTime 时间
     * @return LocalDate 月最后一天日期
     * <AUTHOR>
     * @date 2022/3/23 11:48
     * @update zxy 2022/3/23 11:48
     * @since 1.0
     */
    public static LocalDate getMonthDate(LocalDateTime localDateTime)
    {
        return getMonthDate(localDateTime.toLocalDate());
    }

}
