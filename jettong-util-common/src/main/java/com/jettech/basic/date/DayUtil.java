package com.jettech.basic.date;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedList;
import java.util.List;

/**
 * 日计算工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 日计算工具类
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.utils
 * @className DayUtil
 * @date 2022/3/22 17:13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class DayUtil implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**
     * 获取两个时间段内所有日日期
     *
     * @param beginDateTime 开始时间
     * @param endDateTime 结束时间
     * @return List<LocalDate> 日期集合
     * <AUTHOR>
     * @date 2022/3/22 17:16
     * @update zxy 2022/3/22 17:16
     * @since 1.0
     */
    public static List<LocalDate> getDayLocalDate(LocalDateTime beginDateTime, LocalDateTime endDateTime)
    {
        return getDayLocalDate(beginDateTime.toLocalDate(), endDateTime.toLocalDate());
    }

    /**
     * 获取两个日期段内所有日日期
     *
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @return List<LocalDate> 日期集合
     * <AUTHOR>
     * @date 2022/3/22 17:16
     * @update zxy 2022/3/22 17:16
     * @since 1.0
     */
    public static List<LocalDate> getDayLocalDate(LocalDate beginDate, LocalDate endDate)
    {
        List<LocalDate> day = new LinkedList<>();

        //判断是否同一天
        if (beginDate.isEqual(endDate))
        {
            day.add(endDate);
            return day;
        }
        while (!beginDate.isAfter(endDate))
        {
            day.add(beginDate);
            beginDate = beginDate.plusDays(1);
        }
        return day;
    }

    /**
     * 获取两个时间段内所有日格式化后的日期字符串
     *
     * @param beginDateTime 开始时间
     * @param endDateTime 结束时间
     * @param dateTimeFormatter 格式化
     * @return List<String> 日期字符串集合
     * <AUTHOR>
     * @date 2022/3/22 18:31
     * @update zxy 2022/3/22 18:31
     * @since 1.0
     */
    public static List<String> getDayString(LocalDateTime beginDateTime, LocalDateTime endDateTime,
            DateTimeFormatter dateTimeFormatter)
    {
        return getDayString(beginDateTime.toLocalDate(), endDateTime.toLocalDate(), dateTimeFormatter);
    }

    /**
     * 获取两个日期段内所有日格式化后的日期字符串
     *
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @param dateTimeFormatter 格式化
     * @return List<String> 日期字符串集合
     * <AUTHOR>
     * @date 2022/3/22 18:31
     * @update zxy 2022/3/22 18:31
     * @since 1.0
     */
    public static List<String> getDayString(LocalDate beginDate, LocalDate endDate, DateTimeFormatter dateTimeFormatter)
    {
        if (null == dateTimeFormatter)
        {
            dateTimeFormatter = DateTimeFormatter.ISO_LOCAL_DATE;
        }
        List<LocalDate> dayLocalDate = getDayLocalDate(beginDate, endDate);
        List<String> dayStr = new LinkedList<>();
        if (dayLocalDate.isEmpty())
        {
            return dayStr;
        }

        for (LocalDate localDate : dayLocalDate)
        {
            dayStr.add(localDate.format(dateTimeFormatter));
        }
        return dayStr;
    }

}
