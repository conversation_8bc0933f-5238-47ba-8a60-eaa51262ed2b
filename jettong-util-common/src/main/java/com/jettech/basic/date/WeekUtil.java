package com.jettech.basic.date;

import java.io.Serializable;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjuster;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.LinkedList;
import java.util.List;

/**
 * 周计算工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 周计算工具类
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.utils
 * @className WeekUtil
 * @date 2022/3/22 17:13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class WeekUtil implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**
     * 获取两个时间段内所有周的周末日期.不包含结束时间所在周的周末日期（结束时间日期和周末日期相同时会返回）
     *
     * @param beginDateTime 开始时间
     * @param endDateTime 结束时间
     * @return List<LocalDate> 周末日期
     * <AUTHOR>
     * @date 2022/3/22 17:16
     * @update zxy 2022/3/22 17:16
     * @since 1.0
     */
    public static List<LocalDate> getWeekLocalDate(LocalDateTime beginDateTime, LocalDateTime endDateTime)
    {
        return getWeekLocalDate(beginDateTime.toLocalDate(), endDateTime.toLocalDate());
    }

    /**
     * 获取两个日期段内所有周的周末日期.不包含结束日期所在周的周末日期（结束日期和周末日期相同时会返回）
     *
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @return List<LocalDate> 周末日期
     * <AUTHOR>
     * @date 2022/3/22 17:16
     * @update zxy 2022/3/22 17:16
     * @since 1.0
     */
    public static List<LocalDate> getWeekLocalDate(LocalDate beginDate, LocalDate endDate)
    {
        List<LocalDate> week = new LinkedList<>();

        // 开始周周末日期
        LocalDate beginWeek = getWeekDate(beginDate);

        // 结束周周末日期
        LocalDate endWeek = getWeekDate(endDate);

        // 判断是否同一周
        if (beginWeek.isEqual(endWeek))
        {
            if (endDate.isEqual(endWeek))
            {
                week.add(endWeek);
            }
            return week;
        }
        while (!beginWeek.isAfter(endDate))
        {
            week.add(beginWeek);
            beginWeek = beginWeek.plusDays(7);
        }
        return week;
    }

    /**
     * 获取两个时间段内所有周的周末日期，不包含结束时间所在周的周末日期（结束时间日期和周末日期相同时会返回），返回字符串格式为：2022年1月第1周 2022年1月第2周
     *
     * @param beginDateTime 开始时间
     * @param endDateTime 结束时间
     * @return List<String> 周字符串集合
     * <AUTHOR>
     * @date 2022/3/22 18:31
     * @update zxy 2022/3/22 18:31
     * @since 1.0
     */
    public static List<String> getWeekString(LocalDateTime beginDateTime, LocalDateTime endDateTime)
    {
        return getWeekString(beginDateTime.toLocalDate(), endDateTime.toLocalDate());
    }

    /**
     * 获取两个日期段内所有周的周末日期，不包含结束日期所在周的周末日期（结束日期和周末日期相同时会返回），返回字符串格式为：2022年1月第1周 2022年1月第2周
     *
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @return List<String> 周字符串集合
     * <AUTHOR>
     * @date 2022/3/22 18:31
     * @update zxy 2022/3/22 18:31
     * @since 1.0
     */
    public static List<String> getWeekString(LocalDate beginDate, LocalDate endDate)
    {
        List<LocalDate> weekLocalDate = getWeekLocalDate(beginDate, endDate);
        List<String> weekStr = new LinkedList<>();
        if (weekLocalDate.isEmpty())
        {
            return weekStr;
        }
        WeekFields weekFields = WeekFields.ISO;
        // i 用来特殊处理第0周
        int i = 0;
        for (LocalDate localDate : weekLocalDate)
        {
            int week = localDate.get(weekFields.weekOfMonth());
            if (0 == week || 0 != i)
            {
                if (i < 5)
                {
                    week += 1;
                    i++;
                }
                else
                {
                    i = 0;
                }
            }
            weekStr.add(localDate.getYear() + "年" + localDate.getMonthValue() + "月第" + week + "周");
        }
        return weekStr;
    }

    /**
     * 获取日期所在的周末日期
     *
     * @param date 日期
     * @return LocalDate 周末日期
     * <AUTHOR>
     * @date 2022/3/23 11:48
     * @update zxy 2022/3/23 11:48
     * @since 1.0
     */
    public static LocalDate getWeekDate(LocalDate date)
    {
        TemporalAdjuster lastOfWeek = TemporalAdjusters.ofDateAdjuster(
                localDate -> localDate.plusDays(DayOfWeek.SUNDAY.getValue() - localDate.getDayOfWeek().getValue()));
        return date.with(lastOfWeek);
    }

    /**
     * 获取时间所在的周末日期
     *
     * @param localDateTime 时间
     * @return LocalDate 周末日期
     * <AUTHOR>
     * @date 2022/3/23 11:48
     * @update zxy 2022/3/23 11:48
     * @since 1.0
     */
    public static LocalDate getWeekDate(LocalDateTime localDateTime)
    {
        return getWeekDate(localDateTime.toLocalDate());
    }

    public static void main(String[] args)
    {
        System.out.println(WeekUtil.getWeekLocalDate(LocalDate.of(2022, 3, 10), LocalDate.now()));
    }
}
