package com.jettech.basic.date;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.LinkedList;
import java.util.List;

/**
 * 年计算工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 年计算工具类
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.utils
 * @className YearUtil
 * @date 2022/3/22 17:13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class YearUtil implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**
     * 获取两个时间段内所有年的最后一天日期，不包含结束时间所在年的最后一天日期（结束时间日期和年最后一天日期相同时会返回）
     *
     * @param beginDateTime 开始时间
     * @param endDateTime 结束时间
     * @return List<LocalDate> 年最后一天日期
     * <AUTHOR>
     * @date 2022/3/22 17:16
     * @update zxy 2022/3/22 17:16
     * @since 1.0
     */
    public static List<LocalDate> getYearLocalDate(LocalDateTime beginDateTime, LocalDateTime endDateTime)
    {
        return getYearLocalDate(beginDateTime.toLocalDate(), endDateTime.toLocalDate());
    }

    /**
     * 获取两个日期段内所有年的最后一天日期，不包含结束日期所在年的最后一天日期（结束日期和年最后一天日期相同时会返回）
     *
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @return List<LocalDate> 年最后一天日期
     * <AUTHOR>
     * @date 2022/3/22 17:16
     * @update zxy 2022/3/22 17:16
     * @since 1.0
     */
    public static List<LocalDate> getYearLocalDate(LocalDate beginDate, LocalDate endDate)
    {
        List<LocalDate> year = new LinkedList<>();

        // 开始年最后一天
        LocalDate beginYear = getYearDate(beginDate);

        // 结束年最后一天
        LocalDate endYear = getYearDate(endDate);

        //判断是否同一年
        if (beginYear.isEqual(endYear))
        {
            if (endDate.isEqual(endYear))
            {
                year.add(endYear);
            }
            return year;
        }
        while (!beginYear.isAfter(endDate))
        {
            year.add(beginYear);
            beginYear = beginYear.plusYears(1);
        }
        return year;
    }

    /**
     * 获取两个时间段内所有年的字符串，不包含结束时间所在年（结束时间日期和年最后一天日期日期相同时会返回）
     *
     * @param beginDateTime 开始时间
     * @param endDateTime 结束时间
     * @param dateTimeFormatter 格式化
     * @return List<String> 年字符串集合
     * <AUTHOR>
     * @date 2022/3/22 18:31
     * @update zxy 2022/3/22 18:31
     * @since 1.0
     */
    public static List<String> getYearString(LocalDateTime beginDateTime, LocalDateTime endDateTime,
            DateTimeFormatter dateTimeFormatter)
    {
        return getYearString(beginDateTime.toLocalDate(), endDateTime.toLocalDate(), dateTimeFormatter);
    }

    /**
     * 获取两个日期段内所有年的字符串，不包含结束日期所在年（结束日期和年最后一天日期相同时会返回）
     *
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @param dateTimeFormatter 格式化
     * @return List<String> 年字符串集合
     * <AUTHOR>
     * @date 2022/3/22 18:31
     * @update zxy 2022/3/22 18:31
     * @since 1.0
     */
    public static List<String> getYearString(LocalDate beginDate, LocalDate endDate,
            DateTimeFormatter dateTimeFormatter)
    {
        if (null == dateTimeFormatter)
        {
            dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy");
        }
        List<LocalDate> yearLocalDate = getYearLocalDate(beginDate, endDate);
        List<String> yearStr = new LinkedList<>();
        if (yearLocalDate.isEmpty())
        {
            return yearStr;
        }

        for (LocalDate localDate : yearLocalDate)
        {
            yearStr.add(localDate.format(dateTimeFormatter));
        }
        return yearStr;
    }

    /**
     * 获取日期所在的月最后一天日期
     *
     * @param date 日期
     * @return LocalDate 月最后一天日期
     * <AUTHOR>
     * @date 2022/3/23 11:48
     * @update zxy 2022/3/23 11:48
     * @since 1.0
     */
    public static LocalDate getYearDate(LocalDate date)
    {
        return date.with(TemporalAdjusters.lastDayOfYear());
    }

    /**
     * 获取时间所在的年最后一天日期
     *
     * @param localDateTime 时间
     * @return LocalDate 年最后一天日期
     * <AUTHOR>
     * @date 2022/3/23 11:48
     * @update zxy 2022/3/23 11:48
     * @since 1.0
     */
    public static LocalDate getYearDate(LocalDateTime localDateTime)
    {
        return getYearDate(localDateTime.toLocalDate());
    }

}
