package com.jettech.basic.utils;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.fasterxml.jackson.dataformat.yaml.YAMLParser;

import java.io.IOException;
import java.io.InputStream;


/**
 * yaml文件读取工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description yaml文件读取工具类
 * @projectName jettong
 * @package com.jettech.basic.utils
 * @className YmalFc
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class YmalFc
{
    private YAMLFactory yamlFactory;

    private ObjectMapper mapper;

    public YmalFc()
    {
        this.yamlFactory = new YAMLFactory();
        this.mapper = new ObjectMapper();
        mapper.enable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

    }

    public JsonNode build(String fileName) throws IOException
    {
        InputStream input = this.getClass().getResourceAsStream(fileName);
        YAMLParser yamlParser = yamlFactory.createParser(input);
        return mapper.readTree(yamlParser);
    }

}
