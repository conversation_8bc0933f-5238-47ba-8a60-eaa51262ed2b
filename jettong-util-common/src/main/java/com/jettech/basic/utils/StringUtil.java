package com.jettech.basic.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 字符串工具类
 * @projectName jettong-enterprises
 * @package com.jettech.basic.utils
 * @className HttpUtil
 * @date 2021/10/30 12:45
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
public abstract class StringUtil extends StrUtil
{

    private StringUtil()
    {
    }


    /**
     * 获取满足正则表达式的字符串
     *
     * @param srcString 字符串
     * @param regex 正则表达式
     * @return String 满足正则表达式的字符串
     * <AUTHOR>
     * @date 2021/10/30 13:02
     * @update zxy 2021/10/30 13:02
     * @since 1.0
     */
    public static String getMatchStringByRegex(String srcString, String regex)
    {
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);
        // 忽略大小写的写法
        Matcher matcher = pattern.matcher(srcString);
        matcher.find();

        try
        {
            return StrUtil.nullToEmpty(matcher.group());
        }
        catch (IllegalStateException e)
        {
            log.error(e.getMessage(), e);
            return EMPTY;
        }
    }

    /**
     * 输入流按编码转换成字符串
     *
     * @param is 输入流
     * @param encoding 编码
     * @return String 字符串
     * @throws IOException 流异常
     * <AUTHOR>
     * @date 2021/10/30 13:01
     * @update zxy 2021/10/30 13:01
     * @since 1.0
     */
    public static String inputStreamToString(InputStream is, String encoding) throws IOException
    {
        // 字节流的字节数
        int count = is.available();

        //由于InputStream.read(byte[] b)方法并不能一次性读取太多字节,所以需要判断是否已读取完毕
        byte[] b = new byte[count];
        // 已经成功读取的字节的个数
        int readCount = 0;
        while (readCount < count)
        {
            readCount += is.read(b, readCount, count - readCount);
        }
        return new String(b, 0, count, encoding);
    }

    /**
     * 获取文件的扩展名
     *
     * @param fileName fileName
     * @return String 文件的扩展名
     * <AUTHOR>
     * @date 2021/10/30 13:00
     * @update zxy 2021/10/30 13:00
     * @since 1.0
     */
    public static String getFileExtension(String fileName)
    {
        return fileName.substring(fileName.lastIndexOf('.') + 1);
    }

    /**
     * 获取文件的文件名，不含后缀
     *
     * @param fileName 文件名
     * @return String 文件名
     * <AUTHOR>
     * @date 2021/10/30 13:01
     * @update zxy 2021/10/30 13:01
     * @since 1.0
     */
    public static String getFileName(String fileName)
    {
        return fileName.substring(0, fileName.lastIndexOf('.'));
    }

    /**
     * 字符串的安全处理
     *
     * @param obj
     * @return 如果为空指针的话，就返回空字符串
     */
    public static String safeStr(Object obj)
    {
        return obj == null ? "" : obj.toString();
    }

    /**
     * 获取字符串中最后一串数字，字符串为空或没有截取到数字时返回1
     * 例如：BUG-123获取123
     *
     * @param str 字符串
     * @return Long 数字
     * <AUTHOR>
     * @date 2021/11/12 10:02
     * @update zxy 2021/11/12 10:02
     * @since 1.0
     */
    public static Long getLongToStr(String str)
    {
        if (StrUtil.isEmpty(str))
        {
            return 1L;
        }
        String longStr = str.replaceAll(".*[^\\d](?=(\\d+))", "");
        try
        {
            return Long.parseLong(longStr);
        }
        catch (Exception e)
        {
            log.error(e.getMessage(), e);
        }
        return 1L;
    }

}
