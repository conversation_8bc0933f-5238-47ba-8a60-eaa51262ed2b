package com.jettech.basic.utils;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.TreeNode;
import com.jettech.basic.base.entity.TreeDtoEntity;
import com.jettech.basic.base.entity.TreeEntity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * list列表 转换成tree列表
 * Created by Ace on 2017/6/12.
 *
 * <AUTHOR>
 */
public final class TreeUtil
{
    private TreeUtil()
    {
    }

    /**
     * 构建Tree结构
     *
     * @param treeList 待转换的集合
     * @return 树结构
     */
    public static <E extends TreeEntity<E, ? extends Serializable>> List<E> buildTree(List<E> treeList)
    {
        if (CollUtil.isEmpty(treeList))
        {
            return treeList;
        }
        //记录自己是自己的父节点的id集合
        List<Serializable> selfIdEqSelfParent = new ArrayList<>();
        // 为每一个节点找到子节点集合
        for (E parent : treeList)
        {
            Serializable id = parent.getId();
            for (E children : treeList)
            {
                if (parent != children)
                {
                    //parent != children 这个来判断自己的孩子不允许是自己，因为有时候，根节点的parent会被设置成为自己
                    if (id.equals(children.getParentId()))
                    {
                        parent.initChildren();
                        parent.getChildren().add(children);
                    }
                }
                else if (id.equals(parent.getParentId()))
                {
                    selfIdEqSelfParent.add(id);
                }
            }
        }
        // 找出根节点集合
        List<E> trees = new ArrayList<>();

        List<? extends Serializable> allIds = treeList.stream().map(node -> node.getId()).collect(Collectors.toList());
        for (E baseNode : treeList)
        {
            if (!allIds.contains(baseNode.getParentId()) || selfIdEqSelfParent.contains(baseNode.getParentId()))
            {
                trees.add(baseNode);
            }
        }
        return trees;
    }

    /**
     * 构建Tree结构
     *
     * @param treeList 待转换的集合
     * @return 树结构
     */
    public static <E extends TreeDtoEntity<E, ? extends Serializable>> List<E> buildTreeDto(List<E> treeList)
    {
        if (CollUtil.isEmpty(treeList))
        {
            return treeList;
        }
        //记录自己是自己的父节点的id集合
        List<Serializable> selfIdEqSelfParent = new ArrayList<>();
        // 为每一个节点找到子节点集合
        for (E parent : treeList)
        {
            Serializable id = parent.getId();
            for (E children : treeList)
            {
                if (parent != children)
                {
                    //parent != children 这个来判断自己的孩子不允许是自己，因为有时候，根节点的parent会被设置成为自己
                    if (id.equals(children.getParentId()))
                    {
                        parent.initChildren();
                        parent.getChildren().add(children);
                    }
                }
                else if (id.equals(parent.getParentId()))
                {
                    selfIdEqSelfParent.add(id);
                }
            }
        }
        // 找出根节点集合
        List<E> trees = new ArrayList<>();

        List<? extends Serializable> allIds = treeList.stream().map(node -> node.getId()).collect(Collectors.toList());
        for (E baseNode : treeList)
        {
            if (!allIds.contains(baseNode.getParentId()) || selfIdEqSelfParent.contains(baseNode.getParentId()))
            {
                trees.add(baseNode);
            }
        }
        return trees;
    }

    /**
     * 判断是否是子集点
     * @param allNodes 所有节点列表
     * @param parent 父节点
     * @param target 目标节点
     * @return boolean 如果target是parent的子节点(直接或间接)则返回true
     */
    public static boolean isChildNode(List<TreeNode> allNodes, TreeNode parent, TreeNode target) {
        if (parent == null || target == null || parent.equals(target)) {
            return false;
        }

        // 构建父子关系映射 (子节点ID -> 父节点ID)
        Map<Object, Object> childToParentMap = allNodes.stream()
            .filter(node -> node.getParentId() != null)
            .collect(Collectors.toMap(TreeNode::getId, TreeNode::getParentId, (v1, v2) -> v1));

        // 从target开始向上追踪父节点路径
        Object parentId = parent.getId();
        Object currentId = target.getId();

        // 向上追踪直到找到parent或到达根节点
        while (currentId != null) {
            Object currentParentId = childToParentMap.get(currentId);
            if (parentId.equals(currentParentId)) {
                return true;
            }
            currentId = currentParentId;
        }

        return false;
    }

}
