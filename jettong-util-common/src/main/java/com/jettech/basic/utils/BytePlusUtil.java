package com.jettech.basic.utils;

import cn.hutool.core.util.ByteUtil;

import java.nio.charset.StandardCharsets;

/**
 * 字节工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 字节工具类
 * @projectName jettong-enterprises
 * @package com.jettech.basic.utils
 * @className ByteUtil
 * @date 2021/11/2 16:28
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class BytePlusUtil extends ByteUtil
{
    /**
     * 将long转换为byte，截取或以0填充16位，主要用户加解密的key
     *
     * @param longValue 要转换的long
     * @return byte 字节
     * <AUTHOR>
     * @date 2021/11/2 16:31
     * @update zxy 2021/11/2 16:31
     * @since 1.0
     */
    public static byte[] longToBytesForKey(long longValue)
    {
        String longStr = String.valueOf(longValue);
        int length = 16;
        if (longStr.length() > length)
        {
            longValue = Long.parseLong(longStr.substring(0, length));
        }
        else if (longStr.length() < length)
        {
            longValue = 9999999999999999L - longValue;
        }

        return String.valueOf(longValue).getBytes(StandardCharsets.UTF_8);
    }
}
