package com.jettech.basic.utils;

import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Http工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description Http工具类
 * @projectName jettong-enterprises
 * @package com.jettech.basic.utils
 * @className HttpUtil
 * @date 2021/10/30 12:45
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
public class HttpUtil extends cn.hutool.http.HttpUtil
{

    /**
     * 获取ip地址
     *
     * @param url url
     * @return String IP地址
     * <AUTHOR>
     * @date 2021/10/30 12:47
     * @update zxy 2021/10/30 12:47
     * @since 1.0
     */
    public static String getIP(String url)
    {
        String doubleSlash = "//";
        if (url.contains(doubleSlash))
        {
            int beginIndex = url.indexOf(doubleSlash);
            url = url.substring(beginIndex).replace(doubleSlash, "");
        }

        String colonKey = ":";
        if (url.contains(colonKey))
        {
            url = url.substring(0, url.indexOf(colonKey));
        }

        String slash = "/";
        if (url.contains(slash))
        {
            url = url.substring(0, url.indexOf(slash));
        }

        String re = "([0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3})";
        String str = "";
        Pattern pattern = Pattern.compile(re);
        Matcher matcher = pattern.matcher(url);
        if (matcher.matches())
        {
            str = url;
        }
        else
        {
            try
            {
                InetAddress iAddress = InetAddress.getByName(url);
                str = iAddress.getHostAddress();
            }
            catch (UnknownHostException e)
            {
                log.error("获取IP失败，原因：{}" + e.getMessage(), e);
            }
        }

        return str;
    }

    /**
     * 获取ip地址
     *
     * @param url url
     * @return String IP地址
     * <AUTHOR>
     * @date 2021/10/30 12:47
     * @update zxy 2021/10/30 12:47
     * @since 1.0
     */
    public static String getIpFromUrl(String url)
    {
        String regex = "\\w+(\\.\\w+)+";
        String doMain = StringUtil.getMatchStringByRegex(url, regex);
        String ip = "";

        try
        {
            ip = InetAddress.getByName(doMain).getHostAddress();
        }
        catch (UnknownHostException e)
        {
            log.error("获取IP失败，原因：{}", e.getMessage(), e);
        }

        return ip;
    }
}
