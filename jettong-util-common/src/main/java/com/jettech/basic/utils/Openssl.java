package com.jettech.basic.utils;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.MessageFormat;


/**
 * OpenSSL加解密工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description OpenSSL加解密工具类
 * @projectName jettong
 * @package com.jettech.basic.utils
 * @className Openssl
 * @date 2021-10-25
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public final class Openssl
{

    private Openssl()
    {
    }

    /**
     * 数据加密处理
     *
     * @param data 要加密的数据
     * @param commonKey 加密口令文件名
     * @return String 加密数据
     * @throws IOException IOException
     */
    public static synchronized String toEncryption(String data, String commonKey) throws IOException
    {
        // 加密命令
        String encryption = "echo '" + data + "' | openssl enc -aes128 -a -salt -pass pass:" + commonKey;

        String[] sh = new String[]{"/bin/sh", "-c", encryption};

        // Execute Shell Command  
        ProcessBuilder pb = new ProcessBuilder(sh);
        Process p = pb.start();
        String encryptionData = getShellOut(p);
        p.destroy();
        return encryptionData;
    }

    /**
     * 读取输出流数据
     *
     * @param p 进程
     * @return 从输出流中读取的数据
     * @throws IOException IOException
     */
    public static String getShellOut(Process p) throws IOException
    {

        StringBuilder sb = new StringBuilder();
        try (
                BufferedInputStream in = new BufferedInputStream(p.getInputStream());
                BufferedReader br = new BufferedReader(new InputStreamReader(in))
        )
        {

            String s;

            while ((s = br.readLine()) != null)
            {
                sb.append(s);
            }
        }
        return sb.toString();
    }

    /**
     * 数据解密处理
     *
     * @param data 要解密的数据
     * @param commonKey 解密口令文件名
     * @return String 解密数据
     * @throws IOException IOException
     */
    public static String decrypted(String data, String commonKey) throws IOException
    {

        // 解密命令
        String encryption = "echo '" + data + "' | openssl enc -aes128 -a -d -salt -pass pass:" + commonKey;
        // 替换命令中占位符  
        encryption = MessageFormat.format(encryption, data, commonKey);

        String[] sh = new String[]{"/bin/sh", "-c", encryption};

        // Execute Shell Command  
        ProcessBuilder pb = new ProcessBuilder(sh);
        Process p = pb.start();
        String decryptedData = getShellOut(p);
        p.destroy();
        return decryptedData;
    }
}
