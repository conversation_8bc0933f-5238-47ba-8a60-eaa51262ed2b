package com.jettech.basic.utils;

import cn.hutool.core.io.IoUtil;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipFile;
import org.apache.tools.zip.ZipOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Enumeration;
import java.util.zip.CRC32;
import java.util.zip.CheckedOutputStream;

public class FileToZipUtils
{

    private static final String CHINESE_CHARSET = "GBK";

    private static final String UTF8_CHARSET = "GBK";

    private static final int CACHE_SIZE = 1024;

    private static final Logger logger = LoggerFactory.getLogger(FileToZipUtils.class);

    /**
     * 定义递归次数变量
     */
    static int k = 1;

    private FileToZipUtils()
    {
    }

    /**
     * 压缩指定的单个或多个文件，如果是目录，则遍历目录下所有文件进行压缩
     *
     * @param zipFileName ZIP文件名包含全路径
     * @param files 文件列表
     * @return boolean 压缩结果
     * @throws
     * <AUTHOR>
     * @date 2022/7/11 11:58
     * @update 2022/7/11 11:58
     * @since 1.0
     */
    public static boolean zip(String zipFileName, File... files) throws FileNotFoundException
    {
        java.util.zip.ZipOutputStream out = null;
        createDir(zipFileName);
        try
        {
            out = new java.util.zip.ZipOutputStream(new FileOutputStream(zipFileName));
            for (int i = 0; i < files.length; i++)
            {
                if (null != files[i])
                {
                    zip(out, files[i], files[i].getName());
                }
            }
        }
        finally
        {
            IoUtil.close(out);
        }
        logger.debug("压缩完成");
        return true;
    }

    /**
     * 执行压缩
     *
     * @param out ZIP输入流
     * @param f 被压缩的文件
     * @param base 被压缩的文件名
     */
    private static void zip(java.util.zip.ZipOutputStream out, File f, String base)
    {
        // 方法重载
        if (f.isDirectory())
        {
            //压缩目录
            try
            {
                File[] fl = f.listFiles();
                if (fl.length == 0)
                {
                    // 创建zip实体
                    out.putNextEntry(new java.util.zip.ZipEntry(base + "/"));
                }
                for (int i = 0; i < fl.length; i++)
                {
                    // 递归遍历子文件夹
                    zip(out, fl[i], base + "/" + fl[i].getName());
                }
                k++;
            }
            catch (IOException e)
            {
                logger.error("FileToZip zip IOException");
            }
        }
        else
        {
            FileInputStream in = null;
            BufferedInputStream bi = null;
            try
            {
                //压缩单个文件
                // 创建zip实体
                out.putNextEntry(new java.util.zip.ZipEntry(base));
                in = new FileInputStream(f);
                bi = new BufferedInputStream(in);
                int b;
                while ((b = bi.read()) != -1)
                {
                    // 将字节流写入当前zip目录
                    out.write(b);
                }
            }
            catch (IOException e)
            {
                logger.error("FileToZip zip IOException");
            }
            finally
            {
                IoUtil.close(bi);
                IoUtil.close(in);
            }
        }

    }

    /**
     * 目录不存在时，先创建目录
     *
     * @param zipFileName
     */
    private static void createDir(String zipFileName)
    {
        String repalcefilePath = zipFileName.replace("\\", "/");
        String filePathDir = repalcefilePath.substring(0, repalcefilePath.lastIndexOf("/"));
        File targetFile = new File(filePathDir);
        if (!targetFile.exists())
        {
            //目录不存在时，先创建目录
            targetFile.mkdirs();
        }
    }

    /**
     * 解压压缩包
     *
     * @param zipFilePath 压缩文件路径
     * @param destDir 解压目录
     * @throws IOException
     */
    public static void unZip(String zipFilePath, String destDir) throws IOException
    {
        ZipFile zipFile = null;
        BufferedInputStream bis = null;
        FileOutputStream fos = null;
        BufferedOutputStream bos = null;
        try
        {
            zipFile = new ZipFile(zipFilePath, CHINESE_CHARSET);
            Enumeration<?> zipEntries = zipFile.getEntries();
            byte[] cache = new byte[CACHE_SIZE];
            while (zipEntries.hasMoreElements())
            {
                ZipEntry entry = (ZipEntry) zipEntries.nextElement();
                if (entry.isDirectory())
                {
                    new File(destDir + entry.getName()).mkdirs();
                    continue;
                }
                bis = new BufferedInputStream(zipFile.getInputStream(entry));
                File file = new File(destDir + entry.getName());
                File parentFile = file.getParentFile();
                if (parentFile != null && (!parentFile.exists()))
                {
                    parentFile.mkdirs();
                }
                fos = new FileOutputStream(file);
                bos = new BufferedOutputStream(fos, CACHE_SIZE);
                int readIndex = 0;
                while ((readIndex = bis.read(cache, 0, CACHE_SIZE)) != -1)
                {
                    fos.write(cache, 0, readIndex);
                }
                IoUtil.close(bos);
                IoUtil.close(fos);
                IoUtil.close(bis);
            }
            zipFile.close();
        }
        catch (IOException e)
        {
            throw e;
        }
        finally
        {
            IoUtil.close(bos);
            IoUtil.close(fos);
            IoUtil.close(bis);

            if (zipFile != null)
            {
                try
                {
                    zipFile.close();
                }
                catch (IOException e)
                {
                }
            }
        }
    }

    /**
     * 调用系统命令压缩文件
     *
     * @param dirPath 文件路径
     * @param fileZip 压缩文件名称
     * @throws IOException
     */
    public static void zip7za(String dirPath, String fileZip) throws IOException
    {
        String command = "/usr/bin/7za a -tzip -r " + dirPath + ".zip" + " " + dirPath;
        String[] sh = new String[]{"/bin/sh", "-c", command};
        // Execute Shell Command
        ProcessBuilder pb = new ProcessBuilder(sh);
        Process p = pb.start();
        String encryptionData = getShellOut(p);
        p.destroy();
    }

    /**
     * 调用系统命令解压zip文件
     *
     * @param fileZipPath zip文件路径
     * @param dirPath 解压到的文件路径
     */
    public static void un7zaZip(String fileZipPath, String dirPath) throws IOException
    {

        String command = "/usr/bin/7za x " + fileZipPath + " -o" + dirPath
                + " && /usr/bin/convmv -f GBK -t utf8 --notest -r " + dirPath;
        String[] sh = new String[]{"/bin/sh", "-c", command};
        // Execute Shell Command
        ProcessBuilder pb = new ProcessBuilder(sh);
        Process p = pb.start();
        String encryptionData = getShellOut(p);
        p.destroy();
    }

    /**
     * 读取输出流数据
     *
     * @param p 进程
     * @return 从输出流中读取的数据
     * @throws IOException
     */
    public static final String getShellOut(Process p) throws IOException
    {
        StringBuilder sb = new StringBuilder();
        BufferedInputStream in = null;
        BufferedInputStream errIn = null;
        BufferedReader br = null;
        BufferedReader errBr = null;
        errIn = new BufferedInputStream(p.getErrorStream());
        in = new BufferedInputStream(p.getInputStream());
        br = new BufferedReader(new InputStreamReader(in));
        errBr = new BufferedReader(new InputStreamReader(errIn));
        String s;
        String errs;
        while ((s = br.readLine()) != null)
        {
            // 追加换行符
            sb.append(s);
        }
        while ((errs = errBr.readLine()) != null)
        {
            // 追加换行符
            sb.append(errs);
        }
        br.close();
        in.close();
        return sb.toString();
    }

    static final int BUFFER = 8192;

    /**
     * 将目标目录的文件压缩成Zip文件
     *
     * @param srcPath 需要压缩得文件夹
     * @param dstPath 压缩得路径及文件名称
     * @throws IOException
     */
    public static void compress(String srcPath, String dstPath) throws IOException
    {
        File srcFile = new File(srcPath);
        File dstFile = new File(dstPath);
        if (!srcFile.exists())
        {
            throw new FileNotFoundException(srcPath + "不存在！");
        }

        FileOutputStream out = null;
        ZipOutputStream zipOut = null;
        CheckedOutputStream cos = null;
        try
        {
            out = new FileOutputStream(dstFile);
            cos = new CheckedOutputStream(out, new CRC32());
            zipOut = new ZipOutputStream(cos);
            String baseDir = "";
            compress(srcFile, zipOut, baseDir);
        }
        finally
        {
            if (null != zipOut)
            {
                zipOut.close();
                out = null;
            }
            if (null != out)
            {
                out.close();
            }
            if (null != cos)
            {
                cos.close();
            }
        }
    }

    private static void compress(File file, ZipOutputStream zipOut, String baseDir) throws IOException
    {
        if (file.isDirectory())
        {
            compressDirectory(file, zipOut, baseDir);
        }
        else
        {
            compressFile(file, zipOut, baseDir);
        }
    }

    /**
     * 压缩一个目录
     */
    private static void compressDirectory(File dir, ZipOutputStream zipOut, String baseDir) throws IOException
    {
        File[] files = dir.listFiles();
        for (int i = 0; i < files.length; i++)
        {
            compress(files[i], zipOut, baseDir + dir.getName() + "/");
        }
    }

    /**
     * 压缩一个文件
     */
    private static void compressFile(File file, ZipOutputStream zipOut, String baseDir) throws IOException
    {
        if (!file.exists())
        {
            return;
        }

        BufferedInputStream bis = null;
        try
        {
            bis = new BufferedInputStream(new FileInputStream(file));
            ZipEntry entry = new ZipEntry(baseDir + file.getName());
            zipOut.putNextEntry(entry);
            int count;
            byte[] data = new byte[BUFFER];
            while ((count = bis.read(data, 0, BUFFER)) != -1)
            {
                zipOut.write(data, 0, count);
            }

        }
        finally
        {
            if (null != bis)
            {
                bis.close();
            }
        }
    }

    public static void decompress(String zipFile, String dstPath) throws IOException
    {
        File pathFile = new File(dstPath);
        if (!pathFile.exists())
        {
            pathFile.mkdirs();
        }
        ZipFile zip = new ZipFile(zipFile);
        for (Enumeration entries = zip.getEntries(); entries.hasMoreElements(); )
        {
            ZipEntry entry = (ZipEntry) entries.nextElement();
            String zipEntryName = entry.getName();
            InputStream in = null;
            OutputStream out = null;
            try
            {
                in = zip.getInputStream(entry);
                String outPath = (dstPath + "/" + zipEntryName).replaceAll("\\*", "/");
                ;
                //判断路径是否存在,不存在则创建文件路径
                File file = new File(outPath.substring(0, outPath.lastIndexOf('/')));
                if (!file.exists())
                {
                    file.mkdirs();
                }
                //判断文件全路径是否为文件夹,如果是上面已经上传,不需要解压
                if (new File(outPath).isDirectory())
                {
                    continue;
                }

                out = new FileOutputStream(outPath);
                byte[] buf1 = new byte[1024];
                int len;
                while ((len = in.read(buf1)) > 0)
                {
                    out.write(buf1, 0, len);
                }
            }
            finally
            {
                if (null != in)
                {
                    in.close();
                }

                if (null != out)
                {
                    out.close();
                }
            }
        }
        zip.close();
    }

}
