package com.jettech.basic.utils;

import com.fasterxml.jackson.core.JsonGenerationException;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * Jackson util
 * <p>
 * 1、obj need private and set/get；
 * 2、do not support inner class；
 *
 * <AUTHOR> 2015-9-25 18:02:56
 */
public class JacksonUtil
{
    private final static ObjectMapper objectMapper = new ObjectMapper();
    private static Logger logger = LoggerFactory.getLogger(JacksonUtil.class);

    public static ObjectMapper getInstance()
    {
        return objectMapper;
    }

    /**
     * bean、array、List、Map --> json
     *
     * @param obj
     * @return json string
     * @throws Exception
     */
    public static String writeValueAsString(Object obj)
    {
        try
        {
            return getInstance().writeValueAsString(obj);
        }
        catch (JsonGenerationException e)
        {
            logger.error(e.getMessage(), e);
        }
        catch (JsonMappingException e)
        {
            logger.error(e.getMessage(), e);
        }
        catch (IOException e)
        {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * string --> bean、Map、List(array)
     *
     * @param jsonStr
     * @param clazz
     * @return obj
     * @throws Exception
     */
    public static <T> T readValue(String jsonStr, Class<T> clazz)
    {
        try
        {
            return getInstance().readValue(jsonStr, clazz);
        }
        catch (JsonParseException e)
        {
            logger.error(e.getMessage(), e);
        }
        catch (JsonMappingException e)
        {
            logger.error(e.getMessage(), e);
        }
        catch (IOException e)
        {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * string --> List<Bean>...
     *
     * @param jsonStr
     * @param parametrized
     * @param parameterClasses
     * @param <T>
     * @return
     */
    public static <T> T readValue(String jsonStr, Class<?> parametrized, Class<?>... parameterClasses)
    {
        try
        {
            JavaType javaType = getInstance().getTypeFactory().constructParametricType(parametrized, parameterClasses);
            return getInstance().readValue(jsonStr, javaType);
        }
        catch (JsonParseException e)
        {
            logger.error(e.getMessage(), e);
        }
        catch (JsonMappingException e)
        {
            logger.error(e.getMessage(), e);
        }
        catch (IOException e)
        {
            logger.error(e.getMessage(), e);
        }
        return null;
    }
}
