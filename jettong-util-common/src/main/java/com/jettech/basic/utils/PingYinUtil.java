package com.jettech.basic.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;

/**
 * 得到中文首字母工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 得到中文首字母工具类
 * @projectName jettong
 * @package com.jettech.basic.utils
 * @className PingYinUtil
 * @date 2022-07-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
public final class PingYinUtil
{
    private PingYinUtil()
    {
    }

    private final static int[] LI_SEC_POS_VALUE = {1601, 1637, 1833, 2078, 2274,
            2302, 2433, 2594, 2787, 3106, 3212, 3472, 3635, 3722, 3730, 3858,
            4027, 4086, 4390, 4558, 4684, 4925, 5249, 5590};

    private final static String[] LC_FIRST_LETTER = {"a", "b", "c", "d", "e",
            "f", "g", "h", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s",
            "t", "w", "x", "y", "z"};

    /**
     * 取得给定汉字串的首字母串,即声母串
     *
     * @param str 给定汉字串
     * @return String 声母串
     * <AUTHOR>
     * @date 2022/7/11 11:46
     * @update 2022/7/11 11:46
     * @since 1.0
     */
    public String getAllFirstLetter(String str)
    {
        if (str == null || str.trim().length() == 0)
        {
            return "";
        }
        StringBuilder newStr = new StringBuilder();
        for (int i = 0; i < str.length(); i++)
        {
            newStr.append(this.getFirstLetter(str.substring(i, i + 1)));
        }
        return newStr.toString();
    }

    /**
     * 取得给定汉字的首字母,即声母
     *
     * @param chinese 给定的汉字
     * @return String 给定汉字的声母
     * <AUTHOR>
     * @date 2022/7/11 11:51
     * @update 2022/7/11 11:51
     * @since 1.0
     */
    public String getFirstLetter(String chinese)
    {
        if (chinese == null || chinese.trim().length() == 0)
        {
            return "";
        }
        chinese = this.conversionStr(chinese, "GB2312", "ISO8859-1");
        // 判断是不是汉字
        if (chinese.length() > 1)
        {
            // 汉字区码
            int liSectorCode = chinese.charAt(0);
            // 汉字位码
            int liPositionCode = chinese.charAt(1);
            liSectorCode = liSectorCode - 160;
            liPositionCode = liPositionCode - 160;
            // 汉字区位码
            int liSecPosCode = liSectorCode * 100 + liPositionCode;
            for (int i = 0; i < LC_FIRST_LETTER.length; i++)
            {
                if (liSecPosCode >= LI_SEC_POS_VALUE[i]
                        && liSecPosCode < LI_SEC_POS_VALUE[i + 1])
                {
                    chinese = LC_FIRST_LETTER[i];
                    break;
                }
            }

        }
        return chinese;
    }

    /**
     * 字符串编码转换
     *
     * @param str 要转换编码的字符串
     * @param charsetName 原来的编码
     * @param toCharsetName 转换后的编码
     * @return String 经过编码转换后的字符串
     * @date 2022/7/11 11:52
     * @update 2022/7/11 11:52
     * @since 1.0
     */
    private String conversionStr(String str, String charsetName, String toCharsetName)
    {
        try
        {
            str = new String(str.getBytes(charsetName), toCharsetName);
        }
        catch (UnsupportedEncodingException e)
        {
            log.error("字符串编码转换异常：" + e.getMessage(), e);
        }
        return str;
    }

}
