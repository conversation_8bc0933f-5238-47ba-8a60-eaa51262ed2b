package com.jettech.basic.utils;


import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 中文首字母
 * @projectName jettong
 * @package com.jettech.basic.utils
 * @className ChineseCharToEnUtil
 * @date 2025/7/16 19:55
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class ChineseCharToEnUtil
{
    private static final int[] SEC_POS_VALUE = {45217, 45253, 45761, 46318, 46826, 47010, 47297, 47614, 48119, 49062, 49324, 49896, 50371, 50614, 50622, 50906, 51387, 51446, 52218, 52698, 52980, 53689, 54481};
    private static final char[] FIRST_LETTER = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'W', 'X', 'Y', 'Z'};

    public static String getFirstLetters(String chinese) {
        StringBuilder sb = new StringBuilder();
        char[] charArray = chinese.toCharArray();
        for (char c : charArray) {
            if (isChinese(c)) {
                int code = getGbkCode(c);
                for (int i = 0; i < SEC_POS_VALUE.length - 1; i++) {
                    if (code >= SEC_POS_VALUE[i] && code < SEC_POS_VALUE[i + 1]) {
                        sb.append(FIRST_LETTER[i]);
                        break;
                    }
                }
            } else {
                sb.append(c); // 非汉字保留原字符
            }
        }
        return sb.toString();
    }

    private static int getGbkCode(char ch) {
        try {
            byte[] bytes = String.valueOf(ch).getBytes("GBK");
            return (bytes[0] & 0xFF) * 256 + (bytes[1] & 0xFF); // 计算GBK编码值
        } catch (UnsupportedEncodingException e) {
            return -1;
        }
    }

    private static boolean isChinese(char c) {
        return String.valueOf(c).matches("[\\u4E00-\\u9FA5]");
    }

    public static void main(String[] args) {
        System.out.println(getFirstLetters("浙江省")); // 输出: ZJS
        System.out.println(getFirstLetters("上海市")); // 输出: SHS
    }
}
