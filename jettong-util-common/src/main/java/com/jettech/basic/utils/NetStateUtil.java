package com.jettech.basic.utils;

import lombok.extern.slf4j.Slf4j;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;


/**
 * 检测网址、ip地址或者服务器是否可用
 *
 * <AUTHOR>
 * @version 1.0
 * @description 检测网址、ip地址或者服务器是否可用
 * @projectName jettong
 * @package com.jettech.basic.utils
 * @className NetStateUtil
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
public final class NetStateUtil
{
    static HostnameVerifier hv = (urlHostName, session) -> true;

    /**
     * 判断url地址能否访问
     *
     * @param urlAddress url地址
     * @return Boolean 能否访问
     * <AUTHOR>
     * @date 2021/10/26 13:41
     * @update zxy 2021/10/26 13:41
     * @since 1.0
     */
    public static Boolean connectingUrlAddress(String urlAddress)
    {
        //取出地址前5位
        String tempUrl = urlAddress.substring(0, 5);
        //判断传过来的地址中是否有http
        if (tempUrl.contains(StrPool.HTTP))
        {
            //判断服务器是否是https协议
            if (StrPool.HTTPS.equals(tempUrl))
            {
                try
                {
                    //当协议是https时
                    trustAllHttpsCertificates();
                }
                catch (Exception e)
                {
                    log.error("{}", e.getMessage(), e);
                }
                //当协议是https时
                HttpsURLConnection.setDefaultHostnameVerifier(hv);
            }
            return isConnServerByHttp(urlAddress);
        }
        return false;
    }

    /**
     * 判断ip是否能ping通
     *
     * @param ip ip地址
     * @return boolean
     * <AUTHOR>
     * @date 2021/10/26 13:36
     * @update zxy 2021/10/26 13:36
     * @since 1.0
     */
    public static boolean isReachable(String ip)
    {
        try
        {
            InetAddress address = InetAddress.getByName(ip);
            return address.isReachable(1500);
        }
        catch (Exception e)
        {
            log.error("{}", e.getMessage(), e);
        }
        return false;
    }

    /**
     * 判断url是否能访问
     *
     * @param serverUrl url
     * @return boolean 能否访问
     * <AUTHOR>
     * @date 2021/10/26 13:37
     * @update zxy 2021/10/26 13:37
     * @since 1.0
     */
    private static boolean isConnServerByHttp(String serverUrl)
    {
        // 服务器是否开启
        URL url;
        HttpURLConnection conn = null;
        try
        {
            url = new URL(serverUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(3 * 1000);
            return (conn.getResponseCode() == 200);
        }
        catch (IOException e)
        {
            log.error("{}", e.getMessage(), e);
        }
        finally
        {
            if (null != conn)
            {
                conn.disconnect();
            }
        }
        return false;
    }

    /**
     * https请求能否访问
     *
     * @throws Exception Exception
     * <AUTHOR>
     * @date 2021/10/26 13:39
     * @update zxy 2021/10/26 13:39
     * @since 1.0
     */
    private static void trustAllHttpsCertificates() throws Exception
    {
        javax.net.ssl.TrustManager[] trustAllCerts = new javax.net.ssl.TrustManager[1];
        javax.net.ssl.TrustManager tm = new MyTrustManager();
        trustAllCerts[0] = tm;
        javax.net.ssl.SSLContext sc = javax.net.ssl.SSLContext
                .getInstance("SSL");
        sc.init(null, trustAllCerts, null);
        javax.net.ssl.HttpsURLConnection.setDefaultSSLSocketFactory(sc
                .getSocketFactory());
    }

    static class MyTrustManager implements javax.net.ssl.TrustManager,
            javax.net.ssl.X509TrustManager
    {
        @Override
        public java.security.cert.X509Certificate[] getAcceptedIssuers()
        {
            return null;
        }

        @Override
        public void checkServerTrusted(
                java.security.cert.X509Certificate[] certs, String authType)
        {
        }

        @Override
        public void checkClientTrusted(
                java.security.cert.X509Certificate[] certs, String authType)
        {
        }
    }

}