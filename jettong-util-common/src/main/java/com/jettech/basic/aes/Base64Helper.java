package com.jettech.basic.aes;

import org.apache.commons.codec.binary.Base64;

import java.io.UnsupportedEncodingException;

/**
 * ClassName: Base64Helper
 * Function: 编码解码工具类
 * date: 2017-8-17 下午6:50:16
 *
 * <AUTHOR>
 */
public class Base64Helper
{

    /**
     * 加密编码
     */
    private static final String CODE = "UTF-8";

    private Base64Helper()
    {

    }

    /**
     * 编码
     *
     * @param byteArray
     * @return
     */
    public static String encode(byte[] byteArray) throws UnsupportedEncodingException
    {
        return new String(new Base64().encode(byteArray), CODE);
    }

    /**
     * 解码
     *
     * @param base64EncodedString
     * @return
     */
    public static byte[] decode(String base64EncodedString)
    {
        return new Base64().decode(base64EncodedString);
    }
}
