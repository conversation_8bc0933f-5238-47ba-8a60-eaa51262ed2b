package com.jettech.basic.diff;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 文本文件差异返回对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 文本文件差异返回对象
 * @projectName jettong
 * @package com.jettech.basic.diff
 * @className DiffTextFileResult
 * @date 2023/1/10 15:45
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@Builder
public class DiffTextFileResult implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**
     * 差异类型
     */
    private DiffTextFileType diffType;

    /**
     * 原始文件行号
     */
    private Integer originalRowNum;

    /**
     * 原始文件行内容
     */
    private String originalLine;

    /**
     * 修改后文件行号
     */
    private Integer revisedRowNum;

    /**
     * 修改后文件行内容
     */
    private String revisedLine;

    /**
     * 文本文件差异类型枚举
     *
     * <AUTHOR>
     * @version 1.0
     * @description 文本文件差异类型枚举
     * @projectName jettong
     * @package com.jettech.basic.diff
     * @className DiffTextFileType
     * @date 2023/1/10 15:45
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
     */
    public enum DiffTextFileType implements Serializable
    {

        /*
         * ADD:新增行
         * UPDATE:修改行
         * DELETE:删除行
         * EQUAL:一致
         */
        ADD,
        UPDATE,
        DELETE,
        EQUAL
    }
}
