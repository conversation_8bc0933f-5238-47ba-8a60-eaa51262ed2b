package com.jettech.basic.diff;

import difflib.*;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 差异对比工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 差异对比工具类
 * @projectName jettong
 * @package com.jettech.basic.diff
 * @className DiffUtil
 * @date 2023/1/10 15:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class DiffTextFileUtil
{

    /**
     * 以原始文件为基准文件，对比文本文件内容差异
     *
     * @param originalFilePath 原始文件路径
     * @param revisedFilePath 修订后文件路径
     * @param types 文件内容差异类型
     * @return {@link List<DiffTextFileResult>} 文本文件差异
     * @throws IOException IO流异常
     * <AUTHOR>
     * @date 2023/1/10 16:31
     * @update 2023/1/10 16:31
     * @since 1.0
     */
    public static List<DiffTextFileResult> diff(final String originalFilePath, final String revisedFilePath,
            DiffTextFileResult.DiffTextFileType... types) throws
            IOException
    {
        return diff(new File(originalFilePath), new File(revisedFilePath), Charset.defaultCharset(), types);
    }

    /**
     * 以原始文件为基准文件，对比文本文件内容差异
     *
     * @param originalFilePath 原始文件路径
     * @param revisedFilePath 修订后文件路径
     * @param charset 文件编码，没传默认UTF-8
     * @param types 文件内容差异类型
     * @return {@link List<DiffTextFileResult>} 文本文件差异
     * @throws IOException IO流异常
     * <AUTHOR>
     * @date 2023/1/10 16:31
     * @update 2023/1/10 16:31
     * @since 1.0
     */
    public static List<DiffTextFileResult> diff(final String originalFilePath, final String revisedFilePath,
            Charset charset, DiffTextFileResult.DiffTextFileType... types) throws
            IOException
    {
        return diff(new File(originalFilePath), new File(revisedFilePath), charset, types);
    }

    /**
     * 以原始文件为基准文件，对比文本文件内容差异
     *
     * @param originalFile 原始文件
     * @param revisedFile 修订后文件
     * @param charset 文件编码，没传默认UTF-8
     * @param types 文件内容差异类型
     * @return {@link List<DiffTextFileResult>} 文本文件差异
     * @throws IOException IO流异常
     * <AUTHOR>
     * @date 2023/1/10 16:31
     * @update 2023/1/10 16:31
     * @since 1.0
     */
    public static List<DiffTextFileResult> diff(final File originalFile, final File revisedFile, Charset charset,
            DiffTextFileResult.DiffTextFileType... types) throws
            IOException
    {
        if (null == charset)
        {
            charset = Charset.defaultCharset();
        }
        List<String> originalLines = FileUtils.readLines(originalFile, charset);
        List<String> revisedLines = FileUtils.readLines(revisedFile, charset);

        Patch<String> patch = DiffUtils.diff(originalLines, revisedLines);

        DiffRowGenerator.Builder builder = new DiffRowGenerator.Builder();
        builder.showInlineDiffs(false);
        DiffRowGenerator generator = builder.build();

        List<DiffTextFileResult> results = new ArrayList<>();
        for (Delta<String> delta : patch.getDeltas())
        {
            List<DiffRow> generateDiffRows = generator.generateDiffRows(delta.getOriginal().getLines(), delta
                    .getRevised().getLines());
            // 左侧行号
            int leftPos = delta.getOriginal().getPosition();
            // 右侧行号
            int rightPos = delta.getRevised().getPosition();

            for (DiffRow row : generateDiffRows)
            {
                DiffRow.Tag tag = row.getTag();

                if (tag == DiffRow.Tag.INSERT)
                {
                    if (types.length == 0 || Arrays.asList(types).contains(DiffTextFileResult.DiffTextFileType.ADD))
                    {
                        results.add(DiffTextFileResult.builder().diffType(DiffTextFileResult.DiffTextFileType.ADD)
                                .originalRowNum(leftPos).originalLine(
                                        row.getOldLine()).revisedRowNum(rightPos).revisedLine(row.getNewLine())
                                .build());
                    }
                }
                if (tag == DiffRow.Tag.CHANGE)
                {
                    if (types.length == 0 || Arrays.asList(types).contains(DiffTextFileResult.DiffTextFileType.UPDATE))
                    {
                        results.add(DiffTextFileResult.builder().diffType(DiffTextFileResult.DiffTextFileType.UPDATE)
                                .originalRowNum(leftPos).originalLine(
                                        row.getOldLine()).revisedRowNum(rightPos).revisedLine(row.getNewLine())
                                .build());
                    }
                }
                if (tag == DiffRow.Tag.DELETE)
                {
                    if (types.length == 0 || Arrays.asList(types).contains(DiffTextFileResult.DiffTextFileType.DELETE))
                    {
                        results.add(DiffTextFileResult.builder().diffType(DiffTextFileResult.DiffTextFileType.DELETE)
                                .originalRowNum(leftPos).originalLine(
                                        row.getOldLine()).revisedRowNum(rightPos).revisedLine(row.getNewLine())
                                .build());
                    }
                }
                if (tag == DiffRow.Tag.EQUAL)
                {
                    if (types.length == 0 || Arrays.asList(types).contains(DiffTextFileResult.DiffTextFileType.EQUAL))
                    {
                        results.add(DiffTextFileResult.builder().diffType(DiffTextFileResult.DiffTextFileType.EQUAL)
                                .originalRowNum(leftPos).originalLine(
                                        row.getOldLine()).revisedRowNum(rightPos).revisedLine(row.getNewLine())
                                .build());
                    }
                }
            }
        }

        return results;
    }
}
