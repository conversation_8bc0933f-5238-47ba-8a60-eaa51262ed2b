package com.jettech.basic.job.exception;

import cn.hutool.core.util.StrUtil;

/**
 * jettong-job 异常
 *
 * <AUTHOR>
 * @version 1.0
 * @description jettong-job 异常
 * @projectName jettong
 * @package com.jettech.basic.job.exception
 * @className JettongJobException
 * @date 2023/8/1 18:47
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class JettongJobException extends RuntimeException
{

    private static final long serialVersionUID = 1L;

    /**
     * 异常信息
     */
    private String message;

    /**
     * 具体异常码
     */
    private int code;

    public JettongJobException(Throwable cause)
    {
        super(cause);
    }

    public JettongJobException(final int code, Throwable cause)
    {
        super(cause);
        this.code = code;
    }


    public JettongJobException(final int code, final String message)
    {
        super(message);
        this.code = code;
        this.message = message;
    }

    public JettongJobException(final int code, final String message, Throwable cause)
    {
        super(cause);
        this.code = code;
        this.message = message;
    }

    public JettongJobException(final int code, final String format, Object... args)
    {
        super(StrUtil.contains(format, "{}") ? StrUtil.format(format, args) : String.format(format, args));
        this.code = code;
        this.message = StrUtil.contains(format, "{}") ? StrUtil.format(format, args) : String.format(format, args);
    }


    @Override
    public String getMessage()
    {
        return message;
    }

    public int getCode()
    {
        return code;
    }

    @Override
    public String toString()
    {
        return "JettongJobException [message=" + getMessage() + ", code=" + getCode() + "]";
    }
}
