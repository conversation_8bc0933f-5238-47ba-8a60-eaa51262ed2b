package com.jettech.basic.job.model;

import lombok.Builder;
import lombok.Data;

/**
 * 添加job对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 添加job对象
 * @projectName jettong
 * @package com.jettech.basic.job.model
 * @className JettongJobInfo
 * @date 2023/8/1 10:21
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class JettongJobInfo
{
    /**
     * 主键id
     */
    private int id;

    /**
     * 执行器主键ID
     */
    private int jobGroup;

    /**
     * 任务描述
     */
    private String jobDesc;

    /**
     * 负责人
     */
    private String author;

    /**
     * 报警邮件
     */
    private String alarmEmail;

    /**
     * 调度类型
     */
    private String scheduleType;

    /**
     * 调度配置，值含义取决于调度类型
     */
    private String scheduleConf;

    /**
     * 调度过期策略
     */
    private String misfireStrategy;

    /**
     * 执行器路由策略
     */
    private String executorRouteStrategy;

    /**
     * 执行器，任务Handler名称
     */
    private String executorHandler;

    /**
     * 执行器，任务参数
     */
    private String executorParam;

    /**
     * 阻塞处理策略
     */
    private String executorBlockStrategy;

    /**
     * 任务执行超时时间，单位秒
     */
    private int executorTimeout;

    /**
     * 失败重试次数
     */
    private int executorFailRetryCount;

    /**
     * GLUE类型	#com.xxl.job.core.glue.GlueTypeEnum
     */
    private String glueType;

    /**
     * 子任务ID，多个逗号分隔
     */
    private String childJobId;

    @Builder
    public JettongJobInfo(int id, int jobGroup, String jobDesc, String author, String alarmEmail,
                          String scheduleType, String scheduleConf, String misfireStrategy, String executorRouteStrategy,
                          String executorHandler, String executorParam, String executorBlockStrategy, int executorTimeout,
                          int executorFailRetryCount, String glueType, String childJobId)
    {
        this.id = id;
        this.jobGroup = jobGroup;
        this.jobDesc = jobDesc;
        this.author = author;
        this.alarmEmail = alarmEmail;
        this.scheduleType = scheduleType;
        this.scheduleConf = scheduleConf;
        this.misfireStrategy = misfireStrategy;
        this.executorRouteStrategy = executorRouteStrategy;
        this.executorHandler = executorHandler;
        this.executorParam = executorParam;
        this.executorBlockStrategy = executorBlockStrategy;
        this.executorTimeout = executorTimeout;
        this.executorFailRetryCount = executorFailRetryCount;
        this.glueType = glueType;
        this.childJobId = childJobId;
    }
}
