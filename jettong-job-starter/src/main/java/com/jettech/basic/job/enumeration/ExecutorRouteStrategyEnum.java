package com.jettech.basic.job.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * 执行器路由策略枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 执行器路由策略枚举
 * @projectName jettong
 * @package com.jettech.basic.job.enumeration
 * @className ExecutorRouteStrategyEnum
 * @date 2023/8/1 10:40
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "GlueTypeEnum", description = "执行器路由策略枚举")
public enum ExecutorRouteStrategyEnum implements BaseEnum
{

    FIRST("第一个"),
    LAST("最后一个"),
    ROUND("轮询"),
    RANDOM("随机"),
    CONSISTENT_HASH("一致性HASH"),
    LEAST_FREQUENTLY_USED("最不经常使用"),
    LEAST_RECENTLY_USED("最近最久未使用"),
    FAILOVER("故障转移"),
    BUSYOVER("忙碌转移"),
    SHARDING_BROADCAST("分片广播");


    @ApiModelProperty(value = "描述")
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static ExecutorRouteStrategyEnum match(String val, ExecutorRouteStrategyEnum def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ExecutorRouteStrategyEnum get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ExecutorRouteStrategyEnum val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "执行器路由策略枚举",
            allowableValues = "FIRST,LAST,ROUND,RANDOM,CONSISTENT_HASH,LEAST_FREQUENTLY_USED,LEAST_RECENTLY_USED," +
                    "FAILOVER,BUSYOVER,SHARDING_BROADCAST",
            example = "RANDOM")
    public String getCode()
    {
        return this.name();
    }

}
