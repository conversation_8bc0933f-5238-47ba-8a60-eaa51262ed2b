package com.jettech.basic.job.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * 运行模式枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 运行模式枚举
 * @projectName jettong
 * @package com.jettech.basic.job.enumeration
 * @className GlueTypeEnum
 * @date 2023/8/1 10:40
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "GlueTypeEnum", description = "运行模式枚举")
public enum GlueTypeEnum implements BaseEnum
{

    BEAN("BEAN");
//    GLUE_GROOVY("GLUE(Java)"),
//    GLUE_SHELL("GLUE(Shell)"),
//    GLUE_PYTHON("GLUE(Python)"),
//    GLUE_PHP("GLUE(PHP)"),
//    GLUE_NODEJS("GLUE(Nodejs)"),
//    GLUE_POWERSHELL("GLUE(PowerShell)");


    @ApiModelProperty(value = "描述")
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static GlueTypeEnum match(String val, GlueTypeEnum def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static GlueTypeEnum get(String val)
    {
        return match(val, null);
    }

    public boolean eq(GlueTypeEnum val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "运行模式枚举",
            allowableValues = "BEAN,GLUE_GROOVY,GLUE_SHELL,GLUE_PYTHON,GLUE_PHP,GLUE_NODEJS,GLUE_POWERSHELL",
            example = "BEAN")
    public String getCode()
    {
        return this.name();
    }

}
