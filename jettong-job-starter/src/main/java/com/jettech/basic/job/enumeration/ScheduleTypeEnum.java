package com.jettech.basic.job.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * 调度类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 调度类型枚举
 * @projectName jettong
 * @package com.jettech.basic.job.enumeration
 * @className ScheduleTypeEnum
 * @date 2023/8/1 10:40
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ScheduleTypeEnum", description = "调度类型枚举")
public enum ScheduleTypeEnum implements BaseEnum
{

    /**
     * 固定速度
     */
    FIX_RATE("固定速度"),
    /**
     * CRON
     */
    CRON("CRON");

    @ApiModelProperty(value = "描述")
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static ScheduleTypeEnum match(String val, ScheduleTypeEnum def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ScheduleTypeEnum get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ScheduleTypeEnum val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "调度类型枚举",
            allowableValues = "FIX_RATE,CRON",
            example = "CRON")
    public String getCode()
    {
        return this.name();
    }
}
