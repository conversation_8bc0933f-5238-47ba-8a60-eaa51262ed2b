package com.jettech.basic.job.dto;

import com.jettech.basic.job.enumeration.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 添加job对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 添加job对象
 * @projectName jettong
 * @package com.jettech.basic.job.dto
 * @className JettongJobAddDTO
 * @date 2023/8/1 10:21
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "JettongJobAddDTO", description = "添加job对象")
public class JettongJobAddDTO
{

    /**
     * 任务描述
     */
    @ApiModelProperty(value = "任务描述")
    @NotEmpty(message = "请填写任务描述")
    @Size(max = 100, message = "任务描述长度不能超过100")
    private String jobDesc;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @NotEmpty(message = "请填写负责人")
    @Size(max = 50, message = "负责人长度不能超过50")
    private String author;


    /**
     * 报警邮件
     */
    @ApiModelProperty(value = "报警邮件")
    @Size(max = 200, message = "报警邮件长度不能超过200")
    private String alarmEmail;

    /**
     * 调度类型
     */
    @ApiModelProperty(value = "调度类型")
    @NotNull(message = "请填写调度类型")
    private ScheduleTypeEnum scheduleType;

    /**
     * 调度配置，值含义取决于调度类型, scheduleType为FIX_RATE填写间隔秒数，scheduleType为CRON时填写cron表达式
     */
    @ApiModelProperty(value = "调度配置")
    @NotEmpty(message = "请填写调度配置")
    @Size(max = 128, message = "调度配置长度不能超过128")
    private String scheduleConf;

    /**
     * 运行模式
     */
    @ApiModelProperty(value = "运行模式，默认BEAN")
    private GlueTypeEnum glueType;

    /**
     * 执行器，任务Handler名称, BEAN运行模式时使用
     */
    @ApiModelProperty(value = "JobHandler")
    @Size(max = 200, message = "JobHandler长度不能超过200")
    private String executorHandler;


    /**
     * 执行器，任务参数
     */
    @ApiModelProperty(value = "执行器，任务参数")
    @Size(max = 512, message = "任务参数长度不能超过512")
    private String executorParam;

    /**
     * 执行器路由策略
     */
    @ApiModelProperty(value = "执行器路由策略, 默认RANDOM")
    private ExecutorRouteStrategyEnum executorRouteStrategy;


    /**
     * 子任务ID，多个逗号分隔
     */
    @ApiModelProperty(value = "子任务ID，多个逗号分隔")
    @Size(max = 200, message = "子任务ID长度不能超过200")
    private String childJobId;

    /**
     * 调度过期策略
     */
    @ApiModelProperty(value = "调度过期策略,默认DO_NOTHING")
    private MisfireStrategyEnum misfireStrategy;

    /**
     * 阻塞处理策略
     */
    @ApiModelProperty(value = "阻塞处理策略, 默认SERIAL_EXECUTION")
    private ExecutorBlockStrategyEnum executorBlockStrategy;

    /**
     * 任务执行超时时间，单位秒
     */
    @ApiModelProperty(value = "任务执行超时时间,大于0生效")
    private int executorTimeout;

    /**
     * 失败重试次数
     */
    @ApiModelProperty(value = "失败重试次数,大于0生效")
    private int executorFailRetryCount;

}
