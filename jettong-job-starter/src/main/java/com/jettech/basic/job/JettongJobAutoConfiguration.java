package com.jettech.basic.job;

import com.jettech.basic.job.properties.JettongJobProperties;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * jettong-job注入工具 自动配置类
 * <AUTHOR>
 * @version 1.0
 * @description jettong-job注入工具 自动配置类
 * @projectName jettong
 * @package com.jettech.basic.job.properties
 * @className JettongJobAutoConfiguration
 * @date 2023/8/2 11:19
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Configuration
@AllArgsConstructor
@EnableConfigurationProperties(JettongJobProperties.class)
public class JettongJobAutoConfiguration
{
    private final JettongJobProperties jettongJobProperties;

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = JettongJobProperties.PREFIX, name = "enabled", havingValue = "true",
            matchIfMissing = true)
    public JobApi getJobApi()
    {
        return new JobApi(jettongJobProperties);
    }

}

