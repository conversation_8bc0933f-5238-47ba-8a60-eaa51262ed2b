package com.jettech.basic.job.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Jettong-job配置
 * <AUTHOR>
 * @version 1.0
 * @description Jettong-job配置
 * @projectName Jettong
 * @package com.jettech.basic.job.properties
 * @className JettongJobProperties
 * @date 2023/8/2 11:19
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ConfigurationProperties(JettongJobProperties.PREFIX)
public class JettongJobProperties
{
    public static final String PREFIX = "jettong.job";

    private JettongJobAdmin admin = new JettongJobAdmin();

    private JettongJobExecutor executor = new JettongJobExecutor();

    @Data
    public static class JettongJobExecutor
    {
        /**
         * 执行器name
         */
        private String appname;
    }

    @Data
    public static class JettongJobAdmin
    {
        /**
         * Jettong-job管理中心地址
         */
        private String addresses;

        /**
         * Jettong-job管理中心登录用户名
         */
        private String username;

        /**
         * Jettong-job管理中心登录密码
         */
        private String password;
    }
}
