package com.jettech.basic.job.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * 调度过期策略枚举
 *
 * <AUTHOR>
 * @version 1.调度过期策略枚举
 * @description 执行器路由策略枚举
 * @projectName jettong
 * @package com.jettech.basic.job.enumeration
 * @className MisfireStrategyEnum
 * @date 2023/8/1 10:40
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "MisfireStrategyEnum", description = "调度过期策略枚举")
public enum MisfireStrategyEnum implements BaseEnum
{

    DO_NOTHING("忽略"),
    FIRE_ONCE_NOW("立即执行一次");

    @ApiModelProperty(value = "描述")
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static MisfireStrategyEnum match(String val, MisfireStrategyEnum def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static MisfireStrategyEnum get(String val)
    {
        return match(val, null);
    }

    public boolean eq(MisfireStrategyEnum val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "调度过期策略枚举",
            allowableValues = "DO_NOTHING,FIRE_ONCE_NOW",
            example = "DO_NOTHING")
    public String getCode()
    {
        return this.name();
    }

}
