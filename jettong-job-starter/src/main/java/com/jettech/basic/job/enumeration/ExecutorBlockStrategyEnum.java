package com.jettech.basic.job.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * 阻塞处理策略枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 阻塞处理策略枚举
 * @projectName jettong
 * @package com.jettech.basic.job.enumeration
 * @className ExecutorBlockStrategyEnum
 * @date 2023/8/1 10:40
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "GlueTypeEnum", description = "阻塞处理策略枚举")
public enum ExecutorBlockStrategyEnum implements BaseEnum
{

    SERIAL_EXECUTION("单机串行"),
    DISCARD_LATER("丢弃后续调度"),
    COVER_EARLY("覆盖之前调度");

    @ApiModelProperty(value = "描述")
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static ExecutorBlockStrategyEnum match(String val, ExecutorBlockStrategyEnum def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ExecutorBlockStrategyEnum get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ExecutorBlockStrategyEnum val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "阻塞处理策略枚举",
            allowableValues = "SERIAL_EXECUTION,DISCARD_LATER,COVER_EARLY",
            example = "SERIAL_EXECUTION")
    public String getCode()
    {
        return this.name();
    }

}
