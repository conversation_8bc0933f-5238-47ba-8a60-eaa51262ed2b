package com.jettech.basic.job;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.jettech.basic.job.dto.JettongJobAddDTO;
import com.jettech.basic.job.dto.JettongJobUpdateDTO;
import com.jettech.basic.job.enumeration.ExecutorBlockStrategyEnum;
import com.jettech.basic.job.enumeration.ExecutorRouteStrategyEnum;
import com.jettech.basic.job.enumeration.GlueTypeEnum;
import com.jettech.basic.job.enumeration.MisfireStrategyEnum;
import com.jettech.basic.job.exception.JettongJobException;
import com.jettech.basic.job.model.JettongJobInfo;
import com.jettech.basic.job.properties.JettongJobProperties;
import com.jettech.basic.utils.BeanPlusUtil;
import org.springframework.validation.annotation.Validated;

import java.net.HttpCookie;
import java.util.List;

/**
 * 任务调度API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 任务调度API
 * @projectName jettong
 * @package com.jettech.basic.job
 * @className JobApi
 * @date 2023/8/1 17:49
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class JobApi
{

    private final JettongJobProperties jettongJobProperties;

    /**
     * 登录接口，用户获取HttpCookie
     */
    private static final String LOGIN_URL = "/login";

    /**
     * 添加任务 url
     */
    private static final String ADD_URL = "/jobinfo/add";

    /**
     * 修改任务 url
     */
    private static final String UPDATE_URL = "/jobinfo/update";

    /**
     * 删除任务 url
     */
    private static final String REMOVE_URL = "/jobinfo/remove";

    /**
     * 停止任务 url
     */
    private static final String PAUSE_URL = "/jobinfo/stop";

    /**
     * 启动任务 url
     */
    private static final String START_URL = "/jobinfo/start";

    /**
     * 添加并启动任务 url
     */
    private static final String ADD_START_URL = "/jobinfo/addAndStart";

    /**
     * 根据appname获取执行器id url
     */
    private static final String GET_GROUP_ID_URL = "/jobgroup/getGroupId";

    /**
     * 根据checkByExecutorHandler查询任务是否存在 url
     */
    private static final String CHECK_BY_EXECUTOR_HANDLER_URL = "/jobinfo/checkByExecutorHandler";

    JobApi(JettongJobProperties jettongJobProperties)
    {
        this.jettongJobProperties = jettongJobProperties;
    }

    /**
     * 添加定时任务
     *
     * @param param 定时任务参数
     * @return {@link Integer} 任务id
     * @throws JettongJobException 添加失败异常
     * <AUTHOR>
     * @date 2023/8/1 18:14
     * @update 2023/8/1 18:14
     * @since 1.0
     */
    public Integer add(@Validated JettongJobAddDTO param) throws JettongJobException
    {
        List<HttpCookie> httpCookies = getJettongJobToken();

        JettongJobInfo jobInfo = fillJettongJobInfo(param, httpCookies);

        HttpRequest request = HttpUtil.createPost(jettongJobProperties.getAdmin().getAddresses() + ADD_URL);
        request.form(BeanPlusUtil.beanToMap(jobInfo));
        request.cookie(httpCookies);

        HttpResponse response = request.execute();
        if (response.isOk())
        {
            JSONObject resultJson = JSONObject.parseObject(response.body());
            if (resultJson.getInteger("code") == 200)
            {
                return resultJson.getInteger("content");
            }
            throw new JettongJobException(response.getStatus(), "添加任务失败，错误码{},错误信息{}", resultJson.getInteger("code"),
                    resultJson.getString("msg"));
        }
        throw new JettongJobException(response.getStatus(), "请求jettong-job接口失败，状态码{},请检查服务是否可用", response.getStatus());

    }

    /**
     * 补全添加添加任务参数信息并转换为JettongJobInfo
     *
     * @param param 添加定时任务参数
     * @param httpCookies httpCookies
     * @return {@link JettongJobInfo} JettongJobInfo
     * @throws JettongJobException 获取groupId异常
     * <AUTHOR>
     * @date 2023/8/1 11:33
     * @update 2023/8/1 11:33
     * @since 1.0
     */
    private JettongJobInfo fillJettongJobInfo(@Validated JettongJobAddDTO param, List<HttpCookie> httpCookies)
            throws JettongJobException
    {
        if (null == param.getGlueType())
        {
            param.setGlueType(GlueTypeEnum.BEAN);
        }
        if (null == param.getExecutorRouteStrategy())
        {
            param.setExecutorRouteStrategy(ExecutorRouteStrategyEnum.ROUND);
        }
        if (null == param.getMisfireStrategy())
        {
            param.setMisfireStrategy(MisfireStrategyEnum.DO_NOTHING);
        }
        if (null == param.getExecutorBlockStrategy())
        {
            param.setExecutorBlockStrategy(ExecutorBlockStrategyEnum.SERIAL_EXECUTION);
        }

        JettongJobInfo jobInfo = BeanPlusUtil.toBean(param, JettongJobInfo.class);

        // 查询对应groupId:
        int groupId = getGroupId(httpCookies);

        jobInfo.setJobGroup(groupId);
        return jobInfo;
    }

    /**
     * 补全添加添加任务参数信息并转换为JettongJobInfo
     *
     * @param param 添加定时任务参数
     * @param httpCookies httpCookies
     * @return {@link JettongJobInfo} JettongJobInfo
     * @throws JettongJobException 获取groupId异常
     * <AUTHOR>
     * @date 2023/8/1 11:33
     * @update 2023/8/1 11:33
     * @since 1.0
     */
    private JettongJobInfo fillJettongJobInfo(@Validated JettongJobUpdateDTO param, List<HttpCookie> httpCookies)
            throws JettongJobException
    {
        if (null == param.getGlueType())
        {
            param.setGlueType(GlueTypeEnum.BEAN);
        }
        if (null == param.getExecutorRouteStrategy())
        {
            param.setExecutorRouteStrategy(ExecutorRouteStrategyEnum.ROUND);
        }
        if (null == param.getMisfireStrategy())
        {
            param.setMisfireStrategy(MisfireStrategyEnum.DO_NOTHING);
        }
        if (null == param.getExecutorBlockStrategy())
        {
            param.setExecutorBlockStrategy(ExecutorBlockStrategyEnum.SERIAL_EXECUTION);
        }

        JettongJobInfo jobInfo = BeanPlusUtil.toBean(param, JettongJobInfo.class);

        // 查询对应groupId:
        int groupId = getGroupId(httpCookies);

        jobInfo.setJobGroup(groupId);
        return jobInfo;
    }

    /**
     * 修改定时任务
     *
     * @param param 定时任务参数
     * @throws JettongJobException 修改失败异常
     * <AUTHOR>
     * @date 2023/8/1 18:40
     * @update 2023/8/1 18:40
     * @since 1.0
     */
    public void update(JettongJobUpdateDTO param) throws JettongJobException
    {
        HttpRequest request = HttpUtil.createPost(jettongJobProperties.getAdmin().getAddresses() + UPDATE_URL);
        List<HttpCookie> httpCookies = getJettongJobToken();

        JettongJobInfo jobInfo = fillJettongJobInfo(param, httpCookies);

        request.form(BeanPlusUtil.beanToMap(jobInfo));
        request.cookie(httpCookies);

        HttpResponse response = request.execute();
        if (response.isOk())
        {

            JSONObject resultJson = JSONObject.parseObject(response.body());
            if (resultJson.getInteger("code") == 200)
            {
                return;
            }
            throw new JettongJobException(response.getStatus(), "修改任务失败，错误码{},错误信息{}", resultJson.getInteger("code"),
                    resultJson.getString("msg"));
        }
        throw new JettongJobException(response.getStatus(), "请求jettong-job接口失败，状态码{},请检查服务是否可用", response.getStatus());
    }

    /**
     * 删除定时任务
     *
     * @param id 任务id
     * @throws JettongJobException 删除失败异常
     * <AUTHOR>
     * @date 2023/8/1 18:41
     * @update 2023/8/1 18:41
     * @since 1.0
     */
    public void remove(int id) throws JettongJobException
    {
        HttpRequest request = HttpUtil.createPost(jettongJobProperties.getAdmin().getAddresses() + REMOVE_URL);
        request.form("id", id);
        request.cookie(getJettongJobToken());

        HttpResponse response = request.execute();
        if (response.isOk())
        {
            JSONObject resultJson = JSONObject.parseObject(response.body());
            if (resultJson.getInteger("code") == 200)
            {
                return;
            }
            throw new JettongJobException(response.getStatus(), "删除任务失败，错误码{},错误信息{}", resultJson.getInteger("code"),
                    resultJson.getString("msg"));
        }
        throw new JettongJobException(response.getStatus(), "请求jettong-job接口失败，状态码{},请检查服务是否可用", response.getStatus());
    }

    /**
     * 停止定时任务
     *
     * @param id 任务id
     * @throws JettongJobException 停止失败异常
     * <AUTHOR>
     * @date 2023/8/1 18:42
     * @update 2023/8/1 18:42
     * @since 1.0
     */
    public void pause(int id) throws JettongJobException
    {
        HttpRequest request = HttpUtil.createPost(jettongJobProperties.getAdmin().getAddresses() + PAUSE_URL);
        request.form("id", id);
        request.cookie(getJettongJobToken());

        HttpResponse response = request.execute();
        if (response.isOk())
        {
            JSONObject resultJson = JSONObject.parseObject(response.body());
            if (resultJson.getInteger("code") == 200)
            {
                return;
            }
            throw new JettongJobException(response.getStatus(), "停止任务失败，错误码{},错误信息{}", resultJson.getInteger("code"),
                    resultJson.getString("msg"));
        }
        throw new JettongJobException(response.getStatus(), "请求jettong-job接口失败，状态码{},请检查服务是否可用", response.getStatus());
    }

    /**
     * 启动定时任务
     *
     * @param id 任务id
     * @throws JettongJobException 启动失败异常
     * <AUTHOR>
     * @date 2023/8/1 18:42
     * @update 2023/8/1 18:42
     * @since 1.0
     */
    public void start(int id) throws JettongJobException
    {
        HttpRequest request = HttpUtil.createPost(jettongJobProperties.getAdmin().getAddresses() + START_URL);
        request.form("id", id);
        request.cookie(getJettongJobToken());

        HttpResponse response = request.execute();
        if (response.isOk())
        {
            JSONObject resultJson = JSONObject.parseObject(response.body());
            if (resultJson.getInteger("code") == 200)
            {
                return;
            }
            throw new JettongJobException(response.getStatus(), "启动任务失败，错误码{},错误信息{}", resultJson.getInteger("code"),
                    resultJson.getString("msg"));
        }
        throw new JettongJobException(response.getStatus(), "请求jettong-job接口失败，状态码{},请检查服务是否可用", response.getStatus());
    }

    /**
     * 添加并启动定时任务
     *
     * @param param 定时任务参数
     * @return {@link Integer} 任务id
     * @throws JettongJobException 添加并启动异常
     * <AUTHOR>
     * @date 2023/8/1 18:43
     * @update 2023/8/1 18:43
     * @since 1.0
     */
    public Integer addAndStart(JettongJobAddDTO param) throws JettongJobException
    {
        List<HttpCookie> httpCookies = getJettongJobToken();
        JettongJobInfo jobInfo = fillJettongJobInfo(param, httpCookies);

        HttpRequest request = HttpUtil.createPost(jettongJobProperties.getAdmin().getAddresses() + ADD_START_URL);
        request.form(BeanPlusUtil.beanToMap(jobInfo));
        request.cookie(httpCookies);

        HttpResponse response = request.execute();
        if (response.isOk())
        {
            JSONObject resultJson = JSONObject.parseObject(response.body());
            if (resultJson.getInteger("code") == 200)
            {
                return resultJson.getInteger("content");
            }
            throw new JettongJobException(response.getStatus(), "请求jettong-job接口失败，错误码{},返回内容{}", resultJson.getInteger("code"),
                    resultJson.getString("msg"));
        }
        throw new JettongJobException(response.getStatus(), "请求jettong-job接口失败，状态码{},请检查服务是否可用", response.getStatus());
    }

    /**
     * 模拟登录接口获取 HttpCookie
     *
     * @return {@link List< HttpCookie>} HttpCookie
     * @throws JettongJobException 登录失败
     * <AUTHOR>
     * @date 2023/8/1 15:03
     * @update 2023/8/1 15:03
     * @since 1.0
     */
    private List<HttpCookie> getJettongJobToken() throws JettongJobException
    {
        HttpRequest request = HttpUtil.createPost(jettongJobProperties.getAdmin().getAddresses() + LOGIN_URL);

        request.form("userName", jettongJobProperties.getAdmin().getUsername());
        request.form("password", jettongJobProperties.getAdmin().getPassword());

        HttpResponse response = request.execute();
        if (response.isOk())
        {
            JSONObject resultJson = JSONObject.parseObject(response.body());
            if (resultJson.getInteger("code") == 200)
            {
                return response.getCookies();
            }
            throw new JettongJobException(response.getStatus(), "获取cookie失败，错误码{},返回内容{}", resultJson.getInteger("code"),
                    resultJson.getString("msg"));
        }
        throw new JettongJobException(response.getStatus(), "请求jettong-job接口失败，状态码{},请检查服务是否可用", response.getStatus());
    }

    /**
     * 根据appname获取groupId
     *
     * @param httpCookies httpCookies
     * @return {@link int} groupId
     * @throws JettongJobException 获取groupId异常
     * <AUTHOR>
     * @date 2023/8/1 18:59
     * @update 2023/8/1 18:59
     * @since 1.0
     */
    public int getGroupId(List<HttpCookie> httpCookies) throws JettongJobException
    {
        HttpRequest request = HttpUtil.createPost(jettongJobProperties.getAdmin().getAddresses() + GET_GROUP_ID_URL);
        request.form("appname", jettongJobProperties.getExecutor().getAppname());
        if (null == httpCookies)
        {
            httpCookies = getJettongJobToken();
        }
        request.cookie(httpCookies);

        HttpResponse response = request.execute();
        if (response.isOk())
        {
            JSONObject resultJson = JSONObject.parseObject(response.body());
            if (resultJson.getInteger("code") == 200)
            {
                return resultJson.getInteger("content");
            }
            throw new JettongJobException(response.getStatus(), "获取groupId失败，错误码{},错误信息{}", resultJson.getInteger("code"),
                    resultJson.getString("msg"));
        }
        throw new JettongJobException(response.getStatus(), "请求jettong-job接口失败，状态码{},请检查服务是否可用", response.getStatus());
    }

    /**
     * 根据executorHandler判断任务是否存在（针对无参的任务，例如同步服务器）
     *
     * @param executorHandler executorHandler
     * @return {@link boolean} 是否存在
     * @throws JettongJobException 判断异常
     * <AUTHOR>
     * @date 2023/8/1 18:59
     * @update 2023/8/1 18:59
     * @since 1.0
     */
    public boolean checkByExecutorHandler(String executorHandler) throws JettongJobException
    {
        HttpRequest request = HttpUtil.createPost(jettongJobProperties.getAdmin().getAddresses() + CHECK_BY_EXECUTOR_HANDLER_URL);
        request.form("executorHandler", executorHandler);
        request.cookie(getJettongJobToken());

        HttpResponse response = request.execute();
        if (response.isOk())
        {
            JSONObject resultJson = JSONObject.parseObject(response.body());
            if (resultJson.getInteger("code") == 200)
            {
                return resultJson.getBoolean("content");
            }
            throw new JettongJobException(response.getStatus(), "根据executorHandler判断任务是否存在失败，错误码{},错误信息{}", resultJson.getInteger("code"),
                    resultJson.getString("msg"));
        }
        throw new JettongJobException(response.getStatus(), "请求jettong-job接口失败，状态码{},请检查服务是否可用", response.getStatus());
    }
}
