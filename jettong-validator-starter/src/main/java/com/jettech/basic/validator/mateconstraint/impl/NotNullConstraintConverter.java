package com.jettech.basic.validator.mateconstraint.impl;

import com.jettech.basic.validator.mateconstraint.IConstraintConverter;
import com.jettech.basic.validator.utils.ValidatorConstants;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.lang.annotation.Annotation;
import java.util.Arrays;
import java.util.List;


/**
 * 非空 转换器
 *
 * <AUTHOR>
 * @date 2019-07-25 15:14
 */
public class NotNullConstraintConverter extends BaseConstraintConverter implements IConstraintConverter
{


    @Override
    protected String getType(Class<? extends Annotation> type)
    {
        return ValidatorConstants.NOT_NULL;
    }

    @Override
    protected List<Class<? extends Annotation>> getSupport()
    {
        return Arrays.asList(NotNull.class, NotEmpty.class, NotBlank.class);
    }

    @Override
    protected List<String> getMethods()
    {
        return Arrays.asList(ValidatorConstants.MESSAGE);
    }
}
