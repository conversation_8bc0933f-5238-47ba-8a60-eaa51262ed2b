package com.jettech.basic.validator.mateconstraint.impl;


import com.jettech.basic.validator.mateconstraint.IConstraintConverter;
import com.jettech.basic.validator.utils.ValidatorConstants;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Size;
import java.lang.annotation.Annotation;
import java.util.Arrays;
import java.util.List;

/**
 * 长度 转换器
 *
 * <AUTHOR>
 * @date 2019-07-25 15:15
 */
public class RangeConstraintConverter extends BaseConstraintConverter implements IConstraintConverter
{

    @Override
    protected List<String> getMethods()
    {
        return Arrays.asList("min", "max", ValidatorConstants.MESSAGE);
    }

    @Override
    protected String getType(Class<? extends Annotation> type)
    {
        return "Range";
    }

    @Override
    protected List<Class<? extends Annotation>> getSupport()
    {
        return Arrays.asList(Length.class, Size.class, Range.class);
    }
}
