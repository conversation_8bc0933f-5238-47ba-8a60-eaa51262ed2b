package com.jettech.basic.validator.mateconstraint.impl;

import com.jettech.basic.validator.mateconstraint.IConstraintConverter;
import com.jettech.basic.validator.utils.ValidatorConstants;

import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 其他 转换器
 *
 * <AUTHOR>
 * @date 2019-07-25 15:15
 */
public class OtherConstraintConverter extends BaseConstraintConverter implements IConstraintConverter
{

    @Override
    protected String getType(Class<? extends Annotation> type)
    {
        return type.getSimpleName();
    }

    @Override
    protected List<Class<? extends Annotation>> getSupport()
    {
        return new ArrayList<>();
    }


    @Override
    protected List<String> getMethods()
    {
        return Arrays.asList(ValidatorConstants.MESSAGE);
    }
}
