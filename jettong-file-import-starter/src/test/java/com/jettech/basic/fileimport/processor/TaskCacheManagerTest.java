package com.jettech.basic.fileimport.processor;

import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.fileimport.config.ImportConfig;
import com.jettech.basic.fileimport.model.ImportStatus;
import com.jettech.basic.fileimport.model.ImportTask;
import com.jettech.basic.cache.model.CacheKey;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @ClassName TaskCacheManagerTest
 * @Description TaskCacheManager测试类
 * <AUTHOR>
 * @Date 2025/9/2 11:00
 */
@ExtendWith(MockitoExtension.class)
class TaskCacheManagerTest {

    @Mock
    private CacheOps cacheOps;

    @Mock
    private ImportConfig importConfig;

    private TaskCacheManager taskCacheManager;

    @BeforeEach
    void setUp() {
        when(importConfig.getTaskCacheExpireHours()).thenReturn(2);
        taskCacheManager = new TaskCacheManager(cacheOps, importConfig);
    }

    @Test
    void testSaveTask() {
        // 准备测试数据
        ImportTask task = ImportTask.builder()
                .taskId("test-task-1")
                .fileName("test.xlsx")
                .fileSize(1024L)
                .status(ImportStatus.PENDING)
                .progress(0)
                .startTime(LocalDateTime.now())
                .userId("user-1")
                .serviceName("test-service")
                .processorName("excel-processor")
                .build();

        // 模拟缓存操作
        when(cacheOps.get(any(CacheKey.class))).thenReturn(null);

        // 执行测试
        taskCacheManager.saveTask(task);

        // 验证缓存操作被调用
        verify(cacheOps, times(4)).set(any(CacheKey.class), any());
        verify(cacheOps, times(3)).get(any(CacheKey.class));
    }

    @Test
    void testGetTask() {
        // 准备测试数据
        String taskId = "test-task-1";
        ImportTask expectedTask = ImportTask.builder()
                .taskId(taskId)
                .fileName("test.xlsx")
                .status(ImportStatus.PROCESSING)
                .build();

        // 模拟缓存返回
        when(cacheOps.get(any(CacheKey.class))).thenReturn(expectedTask);

        // 执行测试
        ImportTask actualTask = taskCacheManager.getTask(taskId);

        // 验证结果
        assertNotNull(actualTask);
        assertEquals(expectedTask.getTaskId(), actualTask.getTaskId());
        assertEquals(expectedTask.getFileName(), actualTask.getFileName());
        assertEquals(expectedTask.getStatus(), actualTask.getStatus());
    }

    @Test
    void testGetTasksByUserId() {
        // 准备测试数据
        String userId = "user-1";
        Set<String> taskIds = Set.of("task-1", "task-2");
        
        ImportTask task1 = ImportTask.builder()
                .taskId("task-1")
                .userId(userId)
                .fileName("file1.xlsx")
                .build();
        
        ImportTask task2 = ImportTask.builder()
                .taskId("task-2")
                .userId(userId)
                .fileName("file2.xlsx")
                .build();

        // 模拟缓存操作
        when(cacheOps.get(argThat(key -> key.getKey().contains("import:user:" + userId))))
                .thenReturn(taskIds);
        when(cacheOps.get(argThat(key -> key.getKey().contains("import:task:task-1"))))
                .thenReturn(task1);
        when(cacheOps.get(argThat(key -> key.getKey().contains("import:task:task-2"))))
                .thenReturn(task2);

        // 执行测试
        List<ImportTask> tasks = taskCacheManager.getTasksByUserId(userId);

        // 验证结果
        assertNotNull(tasks);
        assertEquals(2, tasks.size());
        assertTrue(tasks.stream().anyMatch(t -> "task-1".equals(t.getTaskId())));
        assertTrue(tasks.stream().anyMatch(t -> "task-2".equals(t.getTaskId())));
    }

    @Test
    void testGetTasksByProcessorName() {
        // 准备测试数据
        String processorName = "excel-processor";
        Set<String> taskIds = Set.of("task-1", "task-2");
        
        ImportTask task1 = ImportTask.builder()
                .taskId("task-1")
                .processorName(processorName)
                .fileName("file1.xlsx")
                .build();
        
        ImportTask task2 = ImportTask.builder()
                .taskId("task-2")
                .processorName(processorName)
                .fileName("file2.xlsx")
                .build();

        // 模拟缓存操作
        when(cacheOps.get(argThat(key -> key.getKey().contains("import:processor:" + processorName))))
                .thenReturn(taskIds);
        when(cacheOps.get(argThat(key -> key.getKey().contains("import:task:task-1"))))
                .thenReturn(task1);
        when(cacheOps.get(argThat(key -> key.getKey().contains("import:task:task-2"))))
                .thenReturn(task2);

        // 执行测试
        List<ImportTask> tasks = taskCacheManager.getTasksByProcessorName(processorName);

        // 验证结果
        assertNotNull(tasks);
        assertEquals(2, tasks.size());
        assertTrue(tasks.stream().anyMatch(t -> "task-1".equals(t.getTaskId())));
        assertTrue(tasks.stream().anyMatch(t -> "task-2".equals(t.getTaskId())));
    }

    @Test
    void testGetTasksByUserIdAndProcessorName() {
        // 准备测试数据
        String userId = "user-1";
        String processorName = "excel-processor";
        Set<String> taskIds = Set.of("task-1");
        
        ImportTask task = ImportTask.builder()
                .taskId("task-1")
                .userId(userId)
                .processorName(processorName)
                .fileName("file1.xlsx")
                .build();

        // 模拟缓存操作
        when(cacheOps.get(argThat(key -> key.getKey().contains("import:user:" + userId + ":processor:" + processorName))))
                .thenReturn(taskIds);
        when(cacheOps.get(argThat(key -> key.getKey().contains("import:task:task-1"))))
                .thenReturn(task);

        // 执行测试
        List<ImportTask> tasks = taskCacheManager.getTasksByUserIdAndProcessorName(userId, processorName);

        // 验证结果
        assertNotNull(tasks);
        assertEquals(1, tasks.size());
        assertEquals("task-1", tasks.get(0).getTaskId());
        assertEquals(userId, tasks.get(0).getUserId());
        assertEquals(processorName, tasks.get(0).getProcessorName());
    }

    @Test
    void testUpdateTask() {
        // 准备测试数据
        ImportTask task = ImportTask.builder()
                .taskId("test-task-1")
                .fileName("test.xlsx")
                .status(ImportStatus.SUCCESS)
                .progress(100)
                .endTime(LocalDateTime.now())
                .build();

        // 执行测试
        taskCacheManager.updateTask(task);

        // 验证缓存操作被调用
        verify(cacheOps, times(1)).set(any(CacheKey.class), eq(task));
    }

    @Test
    void testRemoveTask() {
        // 准备测试数据
        String taskId = "test-task-1";
        ImportTask task = ImportTask.builder()
                .taskId(taskId)
                .userId("user-1")
                .processorName("excel-processor")
                .build();

        // 模拟缓存返回
        when(cacheOps.get(argThat(key -> key.getKey().contains("import:task:" + taskId))))
                .thenReturn(task);
        when(cacheOps.get(argThat(key -> key.getKey().contains("import:user:user-1"))))
                .thenReturn(new HashSet<>(Arrays.asList(taskId, "other-task")));
        when(cacheOps.get(argThat(key -> key.getKey().contains("import:processor:excel-processor"))))
                .thenReturn(new HashSet<>(Arrays.asList(taskId, "other-task")));
        when(cacheOps.get(argThat(key -> key.getKey().contains("import:user:user-1:processor:excel-processor"))))
                .thenReturn(new HashSet<>(Arrays.asList(taskId)));

        // 执行测试
        taskCacheManager.removeTask(taskId);

        // 验证删除操作被调用
        verify(cacheOps, times(1)).del(argThat(key -> key.getKey().contains("import:task:" + taskId)));
        verify(cacheOps, times(1)).del(argThat(key -> key.getKey().contains("import:user:user-1:processor:excel-processor")));
    }

    @Test
    void testSaveTaskWithNullTask() {
        // 执行测试
        taskCacheManager.saveTask(null);

        // 验证没有缓存操作被调用
        verify(cacheOps, never()).set(any(CacheKey.class), any());
    }

    @Test
    void testGetTaskWithNullTaskId() {
        // 执行测试
        ImportTask result = taskCacheManager.getTask(null);

        // 验证结果
        assertNull(result);
        verify(cacheOps, never()).get(any(CacheKey.class));
    }

    @Test
    void testGetTasksByUserIdWithNullUserId() {
        // 执行测试
        List<ImportTask> result = taskCacheManager.getTasksByUserId(null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(cacheOps, never()).get(any(CacheKey.class));
    }

    @Test
    void testGetTasksByProcessorNameWithNullProcessorName() {
        // 执行测试
        List<ImportTask> result = taskCacheManager.getTasksByProcessorName(null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(cacheOps, never()).get(any(CacheKey.class));
    }
}
