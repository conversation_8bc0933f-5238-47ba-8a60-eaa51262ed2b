package com.jettech.basic.fileimport.controller;

import com.jettech.basic.fileimport.model.ImportStatus;
import com.jettech.basic.fileimport.model.ImportTask;
import com.jettech.basic.fileimport.processor.ImportResourceManager;
import com.jettech.basic.fileimport.processor.ImportTaskManager;
import com.jettech.basic.fileimport.processor.TaskCacheManager;
import com.jettech.basic.fileimport.processor.UniversalImportService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @ClassName UniversalImportControllerTest
 * @Description UniversalImportController测试类
 * <AUTHOR>
 * @Date 2025/9/2 11:30
 */
@ExtendWith(MockitoExtension.class)
class UniversalImportControllerTest {

    @Mock
    private UniversalImportService importService;

    @Mock
    private ImportTaskManager taskManager;

    @Mock
    private ImportResourceManager resourceManager;

    @Mock
    private TaskCacheManager taskCacheManager;

    private UniversalImportController controller;

    @BeforeEach
    void setUp() {
        controller = new UniversalImportController(
                importService, taskManager, resourceManager, taskCacheManager);
    }

    @Test
    void testGetTasksByUserId() {
        // 准备测试数据
        String userId = "user-1";
        List<ImportTask> expectedTasks = Arrays.asList(
                ImportTask.builder()
                        .taskId("task-1")
                        .userId(userId)
                        .fileName("file1.xlsx")
                        .status(ImportStatus.SUCCESS)
                        .processorName("excel-processor")
                        .build(),
                ImportTask.builder()
                        .taskId("task-2")
                        .userId(userId)
                        .fileName("file2.xlsx")
                        .status(ImportStatus.PROCESSING)
                        .processorName("csv-processor")
                        .build()
        );

        // 模拟服务调用
        when(taskCacheManager.getTasksByUserId(userId)).thenReturn(expectedTasks);

        // 执行测试
        ResponseEntity<List<ImportTask>> response = controller.getTasksByUserId(userId);

        // 验证结果
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(2, response.getBody().size());
        assertEquals("task-1", response.getBody().get(0).getTaskId());
        assertEquals("task-2", response.getBody().get(1).getTaskId());

        // 验证服务调用
        verify(taskCacheManager, times(1)).getTasksByUserId(userId);
    }

    @Test
    void testGetTasksByProcessorName() {
        // 准备测试数据
        String processorName = "excel-processor";
        List<ImportTask> expectedTasks = Arrays.asList(
                ImportTask.builder()
                        .taskId("task-1")
                        .userId("user-1")
                        .fileName("file1.xlsx")
                        .status(ImportStatus.SUCCESS)
                        .processorName(processorName)
                        .build(),
                ImportTask.builder()
                        .taskId("task-3")
                        .userId("user-2")
                        .fileName("file3.xlsx")
                        .status(ImportStatus.FAILED)
                        .processorName(processorName)
                        .build()
        );

        // 模拟服务调用
        when(taskCacheManager.getTasksByProcessorName(processorName)).thenReturn(expectedTasks);

        // 执行测试
        ResponseEntity<List<ImportTask>> response = controller.getTasksByProcessorName(processorName);

        // 验证结果
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(2, response.getBody().size());
        assertEquals(processorName, response.getBody().get(0).getProcessorName());
        assertEquals(processorName, response.getBody().get(1).getProcessorName());

        // 验证服务调用
        verify(taskCacheManager, times(1)).getTasksByProcessorName(processorName);
    }

    @Test
    void testGetTasksByUserIdAndProcessorName() {
        // 准备测试数据
        String userId = "user-1";
        String processorName = "excel-processor";
        List<ImportTask> expectedTasks = Arrays.asList(
                ImportTask.builder()
                        .taskId("task-1")
                        .userId(userId)
                        .fileName("file1.xlsx")
                        .status(ImportStatus.SUCCESS)
                        .processorName(processorName)
                        .build()
        );

        // 模拟服务调用
        when(taskCacheManager.getTasksByUserIdAndProcessorName(userId, processorName))
                .thenReturn(expectedTasks);

        // 执行测试
        ResponseEntity<List<ImportTask>> response = 
                controller.getTasksByUserIdAndProcessorName(userId, processorName);

        // 验证结果
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().size());
        assertEquals(userId, response.getBody().get(0).getUserId());
        assertEquals(processorName, response.getBody().get(0).getProcessorName());

        // 验证服务调用
        verify(taskCacheManager, times(1)).getTasksByUserIdAndProcessorName(userId, processorName);
    }

    @Test
    void testGetTasksWithBothParameters() {
        // 准备测试数据
        String userId = "user-1";
        String processorName = "excel-processor";
        List<ImportTask> expectedTasks = Arrays.asList(
                ImportTask.builder()
                        .taskId("task-1")
                        .userId(userId)
                        .processorName(processorName)
                        .build()
        );

        // 模拟服务调用
        when(taskCacheManager.getTasksByUserIdAndProcessorName(userId, processorName))
                .thenReturn(expectedTasks);

        // 执行测试
        ResponseEntity<List<ImportTask>> response = controller.getTasks(userId, processorName);

        // 验证结果
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().size());

        // 验证服务调用
        verify(taskCacheManager, times(1)).getTasksByUserIdAndProcessorName(userId, processorName);
        verify(taskCacheManager, never()).getTasksByUserId(anyString());
        verify(taskCacheManager, never()).getTasksByProcessorName(anyString());
    }

    @Test
    void testGetTasksWithUserIdOnly() {
        // 准备测试数据
        String userId = "user-1";
        List<ImportTask> expectedTasks = Arrays.asList(
                ImportTask.builder()
                        .taskId("task-1")
                        .userId(userId)
                        .build(),
                ImportTask.builder()
                        .taskId("task-2")
                        .userId(userId)
                        .build()
        );

        // 模拟服务调用
        when(taskCacheManager.getTasksByUserId(userId)).thenReturn(expectedTasks);

        // 执行测试
        ResponseEntity<List<ImportTask>> response = controller.getTasks(userId, null);

        // 验证结果
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(2, response.getBody().size());

        // 验证服务调用
        verify(taskCacheManager, times(1)).getTasksByUserId(userId);
        verify(taskCacheManager, never()).getTasksByProcessorName(anyString());
        verify(taskCacheManager, never()).getTasksByUserIdAndProcessorName(anyString(), anyString());
    }

    @Test
    void testGetTasksWithProcessorNameOnly() {
        // 准备测试数据
        String processorName = "excel-processor";
        List<ImportTask> expectedTasks = Arrays.asList(
                ImportTask.builder()
                        .taskId("task-1")
                        .processorName(processorName)
                        .build()
        );

        // 模拟服务调用
        when(taskCacheManager.getTasksByProcessorName(processorName)).thenReturn(expectedTasks);

        // 执行测试
        ResponseEntity<List<ImportTask>> response = controller.getTasks(null, processorName);

        // 验证结果
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().size());

        // 验证服务调用
        verify(taskCacheManager, times(1)).getTasksByProcessorName(processorName);
        verify(taskCacheManager, never()).getTasksByUserId(anyString());
        verify(taskCacheManager, never()).getTasksByUserIdAndProcessorName(anyString(), anyString());
    }

    @Test
    void testGetTasksWithNoParameters() {
        // 执行测试
        ResponseEntity<List<ImportTask>> response = controller.getTasks(null, null);

        // 验证结果
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isEmpty());

        // 验证没有服务调用
        verify(taskCacheManager, never()).getTasksByUserId(anyString());
        verify(taskCacheManager, never()).getTasksByProcessorName(anyString());
        verify(taskCacheManager, never()).getTasksByUserIdAndProcessorName(anyString(), anyString());
    }

    @Test
    void testGetTaskStatus() {
        // 准备测试数据
        String taskId = "test-task-1";
        ImportTask expectedTask = ImportTask.builder()
                .taskId(taskId)
                .fileName("test.xlsx")
                .status(ImportStatus.PROCESSING)
                .progress(50)
                .startTime(LocalDateTime.now())
                .build();

        // 模拟服务调用
        when(taskManager.getTask(taskId)).thenReturn(expectedTask);

        // 执行测试
        ResponseEntity<ImportTask> response = controller.getTaskStatus(taskId);

        // 验证结果
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(taskId, response.getBody().getTaskId());
        assertEquals(ImportStatus.PROCESSING, response.getBody().getStatus());
        assertEquals(50, response.getBody().getProgress());

        // 验证服务调用
        verify(taskManager, times(1)).getTask(taskId);
    }

    @Test
    void testGetTaskStatusNotFound() {
        // 准备测试数据
        String taskId = "non-existent-task";

        // 模拟服务调用
        when(taskManager.getTask(taskId)).thenReturn(null);

        // 执行测试
        ResponseEntity<ImportTask> response = controller.getTaskStatus(taskId);

        // 验证结果
        assertEquals(404, response.getStatusCodeValue());
        assertNull(response.getBody());

        // 验证服务调用
        verify(taskManager, times(1)).getTask(taskId);
    }

    @Test
    void testGetResourceUsage() {
        // 准备测试数据
        Map<String, Object> expectedUsage = Map.of(
                "currentConcurrentImports", 5,
                "maxConcurrentImports", 100,
                "currentTotalSizeMB", 50,
                "maxTotalSizeMB", 2000
        );

        // 模拟服务调用
        when(resourceManager.getResourceUsage()).thenReturn(expectedUsage);

        // 执行测试
        ResponseEntity<Map<String, Object>> response = controller.getResourceUsage();

        // 验证结果
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(expectedUsage, response.getBody());

        // 验证服务调用
        verify(resourceManager, times(1)).getResourceUsage();
    }
}
