package com.jettech.basic.fileimport.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @ClassName ImportConfig
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/9/1 16:26
 */
@ConfigurationProperties(prefix = ImportConfig.PREFIX)
@Data
public class ImportConfig
{
    public static final String PREFIX = "jettong.file-import";
    /**
     * 最大并发导入数
     */
    private int maxConcurrentImports = 100;

    /**
     * 最大文件大小(MB)
     */
    private long maxFileSizeMB = 100;

    /**
     * 总导入文件大小限制(MB)
     */
    private long maxTotalSizeMB = 2000;

    /**
     * 导入超时时间(分钟)
     */
    private int timeoutMinutes = 30;

    /**
     * 任务缓存过期时间(小时)
     */
    private int taskCacheExpireHours = 2;
}
