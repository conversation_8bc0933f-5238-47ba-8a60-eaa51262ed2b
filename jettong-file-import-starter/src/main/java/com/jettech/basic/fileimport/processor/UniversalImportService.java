package com.jettech.basic.fileimport.processor;

import com.jettech.basic.exception.BizException;
import com.jettech.basic.fileimport.model.ImportProgressEvent;
import com.jettech.basic.fileimport.model.ImportResult;
import com.jettech.basic.fileimport.model.ImportStatus;
import com.jettech.basic.fileimport.model.ImportTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * @ClassName UniversalImportService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/9/1 17:04
 */
@Service
@Slf4j
public class UniversalImportService {
    private final ImportResourceManager resourceManager;
    private final ImportTaskManager taskManager;
    private final Map<String, FileProcessor> processors = new ConcurrentHashMap<>();
    private final TaskExecutor importExecutor;

    public UniversalImportService(ImportResourceManager resourceManager,
            ImportTaskManager taskManager,
            @Qualifier("importTaskExecutor") TaskExecutor importExecutor) {
        this.resourceManager = resourceManager;
        this.taskManager = taskManager;
        this.importExecutor = importExecutor;
    }

    /**
     * 注册文件处理器
     */
    public void registerProcessor(FileProcessor processor) {
        processors.put(processor.getProcessorName(), processor);
        log.info("注册文件处理器: {}", processor.getProcessorName());
    }

    /**
     * 提交导入任务
     */
    public ImportTask submitImportTask(MultipartFile file, String processorName,
            String userId) {
        // 验证处理器
        FileProcessor processor = processors.get(processorName);
        if (processor == null) {
            throw new BizException("文件导入：未找到处理器: " + processorName);
        }

        // 验证文件类型
        String fileType = getFileExtension(file.getOriginalFilename());
        if (!processor.getSupportedFileTypes().contains(fileType)) {
            throw new BizException("文件导入：不支持的文件类型: " + fileType);
        }

        // 创建任务
        ImportTask task = taskManager.createTask(file.getOriginalFilename(),
                file.getSize(), userId, processorName);

        // 尝试获取资源
        if (!resourceManager.tryAcquireResource(task.getTaskId(), file.getSize())) {
            task.setStatus(ImportStatus.FAILED);
            task.setMessage("系统繁忙，请稍后重试");
            throw new BizException("系统繁忙，请稍后重试");
        }
        // 异步执行导入
        importExecutor.execute(() -> executeImport(file, processor, task));
        return task;
    }

    /**
     * 执行导入
     */
    private void executeImport(MultipartFile file, FileProcessor processor, ImportTask task) {
        try {
            log.info("开始执行导入任务: {}", task.getTaskId());
            taskManager.updateTask(task.getTaskId(), ImportStatus.PROCESSING, 0, "开始处理文件");

            // 创建进度
            Consumer<ImportProgressEvent> progressCallback = event -> {
                taskManager.updateTask(event.getTaskId(), event.getStatus(),
                        event.getProgress(), event.getMessage());
            };

            // 执行文件导入处理
            ImportResult result = processor.processFile(file, task.getTaskId(), progressCallback);

            // 更新最终状态
            if (result.isSuccess()) {
                taskManager.updateTask(task.getTaskId(), ImportStatus.SUCCESS, 100,
                        String.format("导入完成，成功: %d, 失败: %d",
                                result.getSuccessRecords(), result.getFailedRecords()));
            } else {
                taskManager.updateTask(task.getTaskId(), ImportStatus.FAILED,
                        task.getProgress(), result.getMessage());
            }

        } catch (Exception e) {
            log.error("导入任务执行失败: {}", task.getTaskId(), e);
            taskManager.updateTask(task.getTaskId(), ImportStatus.FAILED,
                    task.getProgress(), "处理失败: " + e.getMessage());
        } finally {
            // 释放资源
            resourceManager.releaseResource(task.getTaskId());
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
    }
}
