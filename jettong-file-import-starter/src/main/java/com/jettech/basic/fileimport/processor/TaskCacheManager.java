package com.jettech.basic.fileimport.processor;

import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.fileimport.config.ImportConfig;
import com.jettech.basic.fileimport.model.ImportTask;
import com.jettech.basic.utils.StrPool;
import com.jettech.basic.cache.model.CacheKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName TaskCacheManager
 * @Description 任务缓存管理器，使用CacheOps管理任务状态
 * <AUTHOR>
 * @Date 2025/9/2 10:00
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TaskCacheManager {

    private final CacheOps cacheOps;
    private final ImportConfig importConfig;

    // 缓存键前缀
    private static final String TASK_PREFIX = "import:task";
    private static final String USER_INDEX_PREFIX = "import:user";
    private static final String PROCESSOR_INDEX_PREFIX = "import:processor";

    /**
     * 保存任务到缓存
     */
    public void saveTask(ImportTask task) {
        if (task == null || task.getTaskId() == null) {
            return;
        }

        try {
            Duration cacheExpire = Duration.ofHours(importConfig.getTaskCacheExpireHours());
            // 保存主任务数据
            CacheKey taskKey = new CacheKey(buildTaskKey(task.getTaskId()), cacheExpire);
            cacheOps.set(taskKey, task);

            // 更新用户任务索引
            if (task.getUserId() != null) {
                addToUserIndex(task.getUserId(), task.getTaskId());
            }

            // 更新处理器任务索引（从任务中获取processorName）
            String processorName = extractProcessorName(task);
            if (processorName != null) {
                addToProcessorIndex(processorName, task.getTaskId());
                
                // 更新用户+处理器任务索引
                if (task.getUserId() != null) {
                    addToUserProcessorIndex(task.getUserId(), processorName, task.getTaskId());
                }
            }

            log.debug("任务已保存到缓存: {}", task.getTaskId());
        } catch (Exception e) {
            log.error("保存任务到缓存失败: {}", task.getTaskId(), e);
        }
    }

    /**
     * 从缓存获取任务
     */
    public ImportTask getTask(String taskId) {
        if (taskId == null) {
            return null;
        }

        try {
            CacheKey taskKey = new CacheKey(buildTaskKey(taskId));
            return cacheOps.get(taskKey);
        } catch (Exception e) {
            log.error("从缓存获取任务失败: {}", taskId, e);
            return null;
        }
    }

    /**
     * 更新任务状态
     */
    public void updateTask(ImportTask task) {
        if (task == null || task.getTaskId() == null) {
            return;
        }

        try {
            Duration cacheExpire = Duration.ofHours(importConfig.getTaskCacheExpireHours());
            CacheKey taskKey = new CacheKey(buildTaskKey(task.getTaskId()), cacheExpire);
            cacheOps.set(taskKey, task);
            log.debug("任务状态已更新: {}", task.getTaskId());
        } catch (Exception e) {
            log.error("更新任务状态失败: {}", task.getTaskId(), e);
        }
    }

    /**
     * 根据用户ID查询任务列表
     */
    public List<ImportTask> getTasksByUserId(String userId) {
        if (userId == null) {
            return Collections.emptyList();
        }

        try {
            CacheKey userIndexKey = new CacheKey(buildUserIndexKey(userId));
            Set<String> taskIds = cacheOps.get(userIndexKey);
            
            if (taskIds == null || taskIds.isEmpty()) {
                return Collections.emptyList();
            }

            return getTasksByIds(taskIds);
        } catch (Exception e) {
            log.error("根据用户ID查询任务失败: {}", userId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据处理器名称查询任务列表
     */
    public List<ImportTask> getTasksByProcessorName(String processorName) {
        if (processorName == null) {
            return Collections.emptyList();
        }

        try {
            CacheKey processorIndexKey = new CacheKey(buildProcessorIndexKey(processorName));
            Set<String> taskIds = cacheOps.get(processorIndexKey);
            
            if (taskIds == null || taskIds.isEmpty()) {
                return Collections.emptyList();
            }

            return getTasksByIds(taskIds);
        } catch (Exception e) {
            log.error("根据处理器名称查询任务失败: {}", processorName, e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据用户ID和处理器名称查询任务列表
     */
    public List<ImportTask> getTasksByUserIdAndProcessorName(String userId, String processorName) {
        if (userId == null || processorName == null) {
            return Collections.emptyList();
        }

        try {
            CacheKey userProcessorIndexKey = new CacheKey(buildUserProcessorIndexKey(userId, processorName));
            Set<String> taskIds = cacheOps.get(userProcessorIndexKey);
            
            if (taskIds == null || taskIds.isEmpty()) {
                return Collections.emptyList();
            }

            return getTasksByIds(taskIds);
        } catch (Exception e) {
            log.error("根据用户ID和处理器名称查询任务失败: userId={}, processorName={}", userId, processorName, e);
            return Collections.emptyList();
        }
    }

    /**
     * 删除任务
     */
    public void removeTask(String taskId) {
        if (taskId == null) {
            return;
        }

        try {
            // 先获取任务信息，用于清理索引
            ImportTask task = getTask(taskId);
            
            // 删除主任务数据
            CacheKey taskKey = new CacheKey(buildTaskKey(taskId));
            cacheOps.del(taskKey);

            // 清理索引
            if (task != null) {
                if (task.getUserId() != null) {
                    removeFromUserIndex(task.getUserId(), taskId);
                }

                String processorName = extractProcessorName(task);
                if (processorName != null) {
                    removeFromProcessorIndex(processorName, taskId);
                    
                    if (task.getUserId() != null) {
                        removeFromUserProcessorIndex(task.getUserId(), processorName, taskId);
                    }
                }
            }

            log.debug("任务已从缓存删除: {}", taskId);
        } catch (Exception e) {
            log.error("删除任务失败: {}", taskId, e);
        }
    }

    // ========== 私有方法 ==========

    /**
     * 构建任务缓存键
     */
    private String buildTaskKey(String taskId) {
        return TASK_PREFIX + StrPool.COLON + taskId;
    }

    /**
     * 构建用户索引键
     */
    private String buildUserIndexKey(String userId) {
        return USER_INDEX_PREFIX + StrPool.COLON + userId;
    }

    /**
     * 构建处理器索引键
     */
    private String buildProcessorIndexKey(String processorName) {
        return PROCESSOR_INDEX_PREFIX + StrPool.COLON + processorName;
    }

    /**
     * 构建用户+处理器索引键
     */
    private String buildUserProcessorIndexKey(String userId, String processorName) {
        return USER_INDEX_PREFIX + StrPool.COLON + userId + StrPool.COLON + "processor" + StrPool.COLON + processorName;
    }

    /**
     * 从任务中提取处理器名称
     */
    private String extractProcessorName(ImportTask task) {
        return task != null ? task.getProcessorName() : null;
    }

    /**
     * 根据任务ID列表批量获取任务
     */
    private List<ImportTask> getTasksByIds(Set<String> taskIds) {
        return taskIds.stream()
                .map(this::getTask)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 添加到用户索引
     */
    private void addToUserIndex(String userId, String taskId) {
        Duration cacheExpire = Duration.ofHours(importConfig.getTaskCacheExpireHours());
        CacheKey userIndexKey = new CacheKey(buildUserIndexKey(userId), cacheExpire);
        Set<String> taskIds = cacheOps.get(userIndexKey);
        if (taskIds == null) {
            taskIds = new HashSet<>();
        }
        taskIds.add(taskId);
        cacheOps.set(userIndexKey, taskIds);
    }

    /**
     * 添加到处理器索引
     */
    private void addToProcessorIndex(String processorName, String taskId) {
        Duration cacheExpire = Duration.ofHours(importConfig.getTaskCacheExpireHours());
        CacheKey processorIndexKey = new CacheKey(buildProcessorIndexKey(processorName), cacheExpire);
        Set<String> taskIds = cacheOps.get(processorIndexKey);
        if (taskIds == null) {
            taskIds = new HashSet<>();
        }
        taskIds.add(taskId);
        cacheOps.set(processorIndexKey, taskIds);
    }

    /**
     * 添加到用户+处理器索引
     */
    private void addToUserProcessorIndex(String userId, String processorName, String taskId) {
        Duration cacheExpire = Duration.ofHours(importConfig.getTaskCacheExpireHours());
        CacheKey userProcessorIndexKey = new CacheKey(buildUserProcessorIndexKey(userId, processorName), cacheExpire);
        Set<String> taskIds = cacheOps.get(userProcessorIndexKey);
        if (taskIds == null) {
            taskIds = new HashSet<>();
        }
        taskIds.add(taskId);
        cacheOps.set(userProcessorIndexKey, taskIds);
    }

    /**
     * 从用户索引中移除
     */
    private void removeFromUserIndex(String userId, String taskId) {
        CacheKey userIndexKey = new CacheKey(buildUserIndexKey(userId));
        Set<String> taskIds = cacheOps.get(userIndexKey);
        if (taskIds != null) {
            taskIds.remove(taskId);
            if (taskIds.isEmpty()) {
                cacheOps.del(userIndexKey);
            } else {
                cacheOps.set(userIndexKey, taskIds);
            }
        }
    }

    /**
     * 从处理器索引中移除
     */
    private void removeFromProcessorIndex(String processorName, String taskId) {
        CacheKey processorIndexKey = new CacheKey(buildProcessorIndexKey(processorName));
        Set<String> taskIds = cacheOps.get(processorIndexKey);
        if (taskIds != null) {
            taskIds.remove(taskId);
            if (taskIds.isEmpty()) {
                cacheOps.del(processorIndexKey);
            } else {
                cacheOps.set(processorIndexKey, taskIds);
            }
        }
    }

    /**
     * 从用户+处理器索引中移除
     */
    private void removeFromUserProcessorIndex(String userId, String processorName, String taskId) {
        CacheKey userProcessorIndexKey = new CacheKey(buildUserProcessorIndexKey(userId, processorName));
        Set<String> taskIds = cacheOps.get(userProcessorIndexKey);
        if (taskIds != null) {
            taskIds.remove(taskId);
            if (taskIds.isEmpty()) {
                cacheOps.del(userProcessorIndexKey);
            } else {
                cacheOps.set(userProcessorIndexKey, taskIds);
            }
        }
    }
}
