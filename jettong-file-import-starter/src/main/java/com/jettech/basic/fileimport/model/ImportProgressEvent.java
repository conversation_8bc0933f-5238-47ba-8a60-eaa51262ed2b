package com.jettech.basic.fileimport.model;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName ImportProgressEvent
 * @Description 导入进度通知事件
 * <AUTHOR>
 * @Date 2025/9/1 16:03
 */
@Data
@Builder
public class ImportProgressEvent
{
    private String taskId;
    private ImportStatus status;
    private int progress;
    private String message;
    private LocalDateTime timestamp;
}
