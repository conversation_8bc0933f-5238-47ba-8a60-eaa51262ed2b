package com.jettech.basic.fileimport.service;

import com.jettech.basic.fileimport.config.ImportConfig;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @ClassName ImportResourceManager
 * @Description 导入资源管理，临时实现方案
 * <AUTHOR>
 * @Date 2025/9/1 16:43
 */
@Component
public class ImportResourceManager
{
    private final ImportConfig importConfig;
    private final AtomicInteger currentConcurrentImports = new AtomicInteger(0);
    private final AtomicLong currentTotalSize = new AtomicLong(0);
    private final Map<String, Long> taskFileSizes = new ConcurrentHashMap<>();

    public ImportResourceManager(ImportConfig importConfig)
    {
        this.importConfig = importConfig;
    }

    
    /**
     * 尝试获取导入资源
     * @param taskId
     * @param fileSize
     * @return {@link boolean}
     * @throws
     * <AUTHOR>
     * @date 2025/9/1 16:44
     * @update wzj 2025/9/1 16:44
     * @since 1.0
     */
    public boolean tryAcquireResource(String taskId, long fileSize)
    {
        // 检查并发数限制
        if (currentConcurrentImports.get() >= importConfig.getMaxConcurrentImports())
        {
            return false;
        }

        // 检查单文件大小限制
        if (fileSize > importConfig.getMaxFileSizeMB() * 1024 * 1024)
        {
            return false;
        }

        // 检查总大小限制
        if (currentTotalSize.get() + fileSize > importConfig.getMaxTotalSizeMB() * 1024 * 1024)
        {
            return false;
        }

        // 原子性获取资源
        synchronized (this)
        {
            if (currentConcurrentImports.get() >= importConfig.getMaxConcurrentImports() ||
                    currentTotalSize.get() + fileSize > importConfig.getMaxTotalSizeMB() * 1024 * 1024)
            {
                return false;
            }

            currentConcurrentImports.incrementAndGet();
            currentTotalSize.addAndGet(fileSize);
            taskFileSizes.put(taskId, fileSize);
            return true;
        }
    }

    /**
     * 释放导入资源
     * @param taskId
     * @return {@link boolean}
     * @throws
     * <AUTHOR>
     * @date 2025/9/1 16:45
     * @update wzj 2025/9/1 16:45
     * @since 1.0
     */
    public void releaseResource(String taskId)
    {
        Long fileSize = taskFileSizes.remove(taskId);
        if (fileSize != null)
        {
            currentConcurrentImports.decrementAndGet();
            currentTotalSize.addAndGet(-fileSize);
        }
    }

    /**
     * 获取当前资源使用情况
     */
    public Map<String, Object> getResourceUsage()
    {
        Map<String, Object> usage = new HashMap<>();
        usage.put("currentConcurrentImports", currentConcurrentImports.get());
        usage.put("maxConcurrentImports", importConfig.getMaxConcurrentImports());
        usage.put("currentTotalSizeMB", currentTotalSize.get() / 1024 / 1024);
        usage.put("maxTotalSizeMB", importConfig.getMaxTotalSizeMB());
        return usage;
    }
}