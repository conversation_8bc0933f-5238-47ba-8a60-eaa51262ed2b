package com.jettech.basic.fileimport.model;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName ImportTask
 * @Description 导入任务对象
 * <AUTHOR>
 * @Date 2025/9/1 16:03
 */
@Data
@Builder
public class ImportTask
{
    private String taskId;
    private String fileName;
    private long fileSize;
    private ImportStatus status;
    private int progress;
    private String message;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String userId;
    private String processorName;
    private LocalDateTime lastHeartbeat;  // 最后心跳时间，用于检测僵尸任务
}