package com.jettech.basic.fileimport.service;

import com.jettech.basic.fileimport.model.ImportProgressEvent;
import com.jettech.basic.fileimport.model.ImportResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.Set;
import java.util.function.Consumer;

/**
 * @ClassName service
 * @Description 导入文件处理器
 * <AUTHOR>
 * @Date 2025/9/1 16:17
 */
public interface FileProcessor
{
    /**
     * 处理文件
     * @param file 文件
     * @param taskId 任务id
     * @param progressCallback 进度回调
     * @return {@link ImportResult}
     * @throws Exception
     * <AUTHOR>
     * @date 2025/9/1 16:18
     * @update wzj 2025/9/1 16:18
     * @since 1.0
     */
    ImportResult processFile(MultipartFile file, String taskId,
            Consumer<ImportProgressEvent> progressCallback);

    /**
     * 获取支持的文件类型
     * @return {@link Set<String>}
     * @throws
     * <AUTHOR>
     * @date 2025/9/1 16:19
     * @update wzj 2025/9/1 16:19
     * @since 1.0
     */
    Set<String> getSupportedFileTypes();

    /**
     *  返回定义的处理器名称
     * @param
     * @return {@link String}
     * @throws
     * <AUTHOR>
     * @date 2025/9/1 16:20
     * @update wzj 2025/9/1 16:20
     * @since 1.0
     */
    String getProcessorName();
}
