package com.jettech.basic.fileimport.controller;

import com.jettech.basic.base.R;
import com.jettech.basic.fileimport.model.ImportProgressEvent;
import com.jettech.basic.fileimport.model.ImportTask;
import com.jettech.basic.fileimport.processor.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @ClassName UniversalImportController
 * @Description 通用文件导入
 * <AUTHOR>
 * @Date 2025/9/1 17:35
 */
@RestController
@RequestMapping("/file/import")
@Slf4j
public class UniversalImportController
{
    private final UniversalImportService importService;
    private final ImportTaskManager taskManager;
    private final ImportResourceManager resourceManager;
    private final TaskCacheManager taskCacheManager;
    private final TaskRecoveryService taskRecoveryService;

    public UniversalImportController(UniversalImportService importService,
            ImportTaskManager taskManager,
            ImportResourceManager resourceManager,
            TaskCacheManager taskCacheManager,
            TaskRecoveryService taskRecoveryService)
    {
        this.importService = importService;
        this.taskManager = taskManager;
        this.resourceManager = resourceManager;
        this.taskCacheManager = taskCacheManager;
        this.taskRecoveryService = taskRecoveryService;
    }

    /**
     * 提交导入文件
     */
    @PostMapping("/submit")
    public ResponseEntity<Map<String, Object>> submitImport(
            @RequestParam("file") MultipartFile file,
            @RequestParam("processorName") String processorName,
            @RequestParam("userId") String userId,
            @RequestParam("serviceName") String serviceName)
    {

        try
        {
            ImportTask task = importService.submitImportTask(file, processorName, userId);

            Map<String, Object> response = new HashMap<>();
            response.put("taskId", task.getTaskId());
            response.put("status", "submitted");
            response.put("message", "导入任务已提交");

            return ResponseEntity.ok(response);

        }
        catch (Exception e)
        {
            log.error("提交导入任务失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("status", "failed");
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * SSE进度监听端点
     */
    @GetMapping(value = "/progress/{taskId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter getImportProgress(@PathVariable String taskId)
    {
        SseEmitter emitter = taskManager.registerEmitter(taskId);

        // 立即发送当前任务状态
        ImportTask task = taskManager.getTask(taskId);
        if (task != null)
        {
            try
            {
                ImportProgressEvent event = ImportProgressEvent.builder()
                        .taskId(taskId)
                        .status(task.getStatus())
                        .progress(task.getProgress())
                        .message(task.getMessage())
                        .timestamp(LocalDateTime.now())
                        .build();

                emitter.send(SseEmitter.event()
                        .name("progress")
                        .data(event)
                        .id(taskId));
            }
            catch (IOException e)
            {
                log.error("发送初始进度事件失败", e);
            }
        }

        return emitter;
    }

    /**
     * 获取任务状态
     */
    @GetMapping("/status/{taskId}")
    public ResponseEntity<ImportTask> getTaskStatus(@PathVariable String taskId)
    {
        ImportTask task = taskManager.getTask(taskId);
        if (task == null)
        {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(task);
    }

    /**
     * 获取系统资源使用情况
     */
    @GetMapping("/resource-usage")
    public ResponseEntity<Map<String, Object>> getResourceUsage()
    {
        return ResponseEntity.ok(resourceManager.getResourceUsage());
    }

    /**
     * 根据用户ID查询导入任务列表
     */
    @GetMapping("/tasks/user/{userId}")
    public ResponseEntity<List<ImportTask>> getTasksByUserId(@PathVariable String userId)
    {
        List<ImportTask> tasks = taskCacheManager.getTasksByUserId(userId);
        return ResponseEntity.ok(tasks);
    }

    /**
     * 根据处理器名称查询导入任务列表
     */
    @GetMapping("/tasks/processor/{processorName}")
    public ResponseEntity<List<ImportTask>> getTasksByProcessorName(@PathVariable String processorName)
    {
        List<ImportTask> tasks = taskCacheManager.getTasksByProcessorName(processorName);
        return ResponseEntity.ok(tasks);
    }

    /**
     * 根据用户ID和处理器名称查询导入任务列表
     */
    @GetMapping("/tasks/user/{userId}/processor/{processorName}")
    public ResponseEntity<List<ImportTask>> getTasksByUserIdAndProcessorName(
            @PathVariable String userId,
            @PathVariable String processorName)
    {
        List<ImportTask> tasks = taskCacheManager.getTasksByUserIdAndProcessorName(userId, processorName);
        return ResponseEntity.ok(tasks);
    }


    /**
     * 删除单个任务
     */
    @DeleteMapping("/tasks/{taskId}")
    public ResponseEntity<Map<String, Object>> deleteTask(@PathVariable String taskId)
    {
        Map<String, String> results = taskCacheManager.removeTasks(Arrays.asList(taskId));
        String result = results.get(taskId);

        Map<String, Object> response = new HashMap<>();
        response.put("taskId", taskId);
        response.put("result", result);
        response.put("success", "删除成功".equals(result));

        if ("删除成功".equals(result)) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 批量删除任务
     */
    @DeleteMapping("/tasks")
    public ResponseEntity<Map<String, Object>> deleteTasks(@RequestBody List<String> taskIds)
    {
        if (taskIds == null || taskIds.isEmpty()) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "任务ID列表不能为空");
            return ResponseEntity.badRequest().body(response);
        }

        Map<String, String> results = taskCacheManager.removeTasks(taskIds);

        long successCount = results.values().stream()
                .mapToLong(result -> "删除成功".equals(result) ? 1 : 0)
                .sum();

        Map<String, Object> response = new HashMap<>();
        response.put("total", taskIds.size());
        response.put("success", successCount);
        response.put("failed", taskIds.size() - successCount);
        response.put("details", results);

        return ResponseEntity.ok(response);
    }

    /**
     * 获取执行中的任务列表
     */
    @GetMapping("/tasks/processing")
    public ResponseEntity<List<ImportTask>> getProcessingTasks()
    {
        List<ImportTask> tasks = taskRecoveryService.getProcessingTasks();
        return ResponseEntity.ok(tasks);
    }

}
