package com.jettech.basic.fileimport.controller;

import com.jettech.basic.fileimport.model.ImportProgressEvent;
import com.jettech.basic.fileimport.model.ImportTask;
import com.jettech.basic.fileimport.processor.ImportResourceManager;
import com.jettech.basic.fileimport.processor.ImportTaskManager;
import com.jettech.basic.fileimport.processor.UniversalImportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName UniversalImportController
 * @Description 通用文件导入
 * <AUTHOR>
 * @Date 2025/9/1 17:35
 */
@RestController
@RequestMapping("/file/import")
@Slf4j
public class UniversalImportController
{
    private final UniversalImportService importService;
    private final ImportTaskManager taskManager;
    private final ImportResourceManager resourceManager;

    public UniversalImportController(UniversalImportService importService,
            ImportTaskManager taskManager,
            ImportResourceManager resourceManager)
    {
        this.importService = importService;
        this.taskManager = taskManager;
        this.resourceManager = resourceManager;
    }

    /**
     * 提交导入文件
     */
    @PostMapping("/submit")
    public ResponseEntity<Map<String, Object>> submitImport(
            @RequestParam("file") MultipartFile file,
            @RequestParam("processorName") String processorName,
            @RequestParam("userId") String userId,
            @RequestParam("serviceName") String serviceName)
    {

        try
        {
            ImportTask task = importService.submitImportTask(file, processorName, userId, serviceName);

            Map<String, Object> response = new HashMap<>();
            response.put("taskId", task.getTaskId());
            response.put("status", "submitted");
            response.put("message", "导入任务已提交");

            return ResponseEntity.ok(response);

        }
        catch (Exception e)
        {
            log.error("提交导入任务失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("status", "failed");
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * SSE进度监听端点
     */
    @GetMapping(value = "/progress/{taskId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter getImportProgress(@PathVariable String taskId)
    {
        SseEmitter emitter = taskManager.registerEmitter(taskId);

        // 立即发送当前任务状态
        ImportTask task = taskManager.getTask(taskId);
        if (task != null)
        {
            try
            {
                ImportProgressEvent event = ImportProgressEvent.builder()
                        .taskId(taskId)
                        .status(task.getStatus())
                        .progress(task.getProgress())
                        .message(task.getMessage())
                        .timestamp(LocalDateTime.now())
                        .build();

                emitter.send(SseEmitter.event()
                        .name("progress")
                        .data(event)
                        .id(taskId));
            }
            catch (IOException e)
            {
                log.error("发送初始进度事件失败", e);
            }
        }

        return emitter;
    }

    /**
     * 获取任务状态
     */
    @GetMapping("/status/{taskId}")
    public ResponseEntity<ImportTask> getTaskStatus(@PathVariable String taskId)
    {
        ImportTask task = taskManager.getTask(taskId);
        if (task == null)
        {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(task);
    }

    /**
     * 获取系统资源使用情况
     */
    @GetMapping("/resource-usage")
    public ResponseEntity<Map<String, Object>> getResourceUsage()
    {
        return ResponseEntity.ok(resourceManager.getResourceUsage());
    }
}
