package com.jettech.basic.fileimport.service;

import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.fileimport.model.ImportProgressEvent;
import com.jettech.basic.fileimport.model.ImportStatus;
import com.jettech.basic.fileimport.model.ImportTask;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName ImportTaskManager
 * @Description 导入任务管理
 * <AUTHOR>
 * @Date 2025/9/1 16:46
 */
@Service
@RequiredArgsConstructor
public class ImportTaskManager {

    private final CacheOps cacheOps;
    private final TaskCacheManager taskCacheManager;
    private final Map<String, SseEmitter> emitters = new ConcurrentHashMap<>();

    /**
     * 创建导入任务
     */
    public ImportTask createTask(String fileName, long fileSize, String userId, String processorName) {
        String taskId = UUID.randomUUID().toString();
        ImportTask task = ImportTask.builder()
                .taskId(taskId)
                .fileName(fileName)
                .fileSize(fileSize)
                .status(ImportStatus.PENDING)
                .progress(0)
                .startTime(LocalDateTime.now())
                .userId(userId)
                .processorName(processorName)
                .lastHeartbeat(LocalDateTime.now())
                .build();

        taskCacheManager.saveTask(task);
        return task;
    }

    /**
     * 更新任务状态
     */
    public void updateTask(String taskId, ImportStatus status, int progress, String message) {
        ImportTask task = taskCacheManager.getTask(taskId);
        if (task != null) {
            task.setStatus(status);
            task.setProgress(progress);
            task.setMessage(message);
            task.setLastHeartbeat(LocalDateTime.now()); // 更新心跳时间

            if (status == ImportStatus.SUCCESS || status == ImportStatus.FAILED || status == ImportStatus.TIMEOUT) {
                task.setEndTime(LocalDateTime.now());
            }

            // 更新缓存中的任务
            taskCacheManager.updateTask(task);

            // 发送SSE事件
            sendProgressEvent(taskId, status, progress, message);
        }
    }

    /**
     * 注册SSE连接
     */
    public SseEmitter registerEmitter(String taskId) {
        SseEmitter emitter = new SseEmitter(30 * 60 * 1000L); // 30分钟超时
        emitters.put(taskId, emitter);

        emitter.onCompletion(() -> emitters.remove(taskId));
        emitter.onTimeout(() -> emitters.remove(taskId));
        emitter.onError(throwable -> emitters.remove(taskId));

        return emitter;
    }

    /**
     * 发送进度事件
     */
    private void sendProgressEvent(String taskId, ImportStatus status, int progress, String message) {
        SseEmitter emitter = emitters.get(taskId);
        if (emitter != null) {
            try {
                ImportProgressEvent event = ImportProgressEvent.builder()
                        .taskId(taskId)
                        .status(status)
                        .progress(progress)
                        .message(message)
                        .timestamp(LocalDateTime.now())
                        .build();

                emitter.send(SseEmitter.event()
                        .name("progress")
                        .data(event)
                        .id(taskId));

                // 如果任务完成，关闭连接
                if (status == ImportStatus.SUCCESS || status == ImportStatus.FAILED || status == ImportStatus.TIMEOUT) {
                    emitter.complete();
                    emitters.remove(taskId);
                }
            } catch (IOException e) {
                emitters.remove(taskId);
            }
        }
    }

    /**
     * 获取任务信息
     */
    public ImportTask getTask(String taskId) {
        return taskCacheManager.getTask(taskId);
    }

    /**
     * 清理过期任务
     * 注意：现在任务存储在缓存中，缓存本身有过期机制（2小时），
     * 所以这个方法主要用于清理SSE连接等内存资源
     * <AUTHOR>
     * @date 2025/9/1 17:37
     * @update wzj 2025/9/2 10:30
     * @since 1.0
     */
    @Scheduled(fixedRate = 300000) // 5分钟执行一次
    public void cleanupExpiredTasks() {
        // 清理过期的SSE连接
        LocalDateTime expireTime = LocalDateTime.now().minusMinutes(30); // SSE连接30分钟超时
        emitters.entrySet().removeIf(entry -> {
            // 检查对应的任务是否还存在
            ImportTask task = taskCacheManager.getTask(entry.getKey());
            return task == null || (task.getEndTime() != null && task.getEndTime().isBefore(expireTime));
        });
    }
}