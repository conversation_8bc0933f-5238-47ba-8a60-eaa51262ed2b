package com.jettech.basic.fileimport.config;


import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @ClassName ImportExecutorConfig
 * @Description 导入任务执行器配置
 * <AUTHOR>
 * @Date 2025/9/1 15:26
 */
@Configuration
@EnableAsync
@EnableScheduling
@EnableConfigurationProperties(ImportConfig.class)
public class ImportExecutorAutoConfig
{

    /**
     * 导入任务执行器
     * @param importConfig
     * @return {@link TaskExecutor}
     * @throws
     * <AUTHOR>
     * @date 2025/9/1 16:42
     * @update wzj 2025/9/1 16:42
     * @since 1.0
     */
    @Bean("importTaskExecutor")
    public TaskExecutor importTaskExecutor(ImportConfig importConfig)
    {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(importConfig.getMaxConcurrentImports());
        executor.setMaxPoolSize(importConfig.getMaxConcurrentImports());
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("import-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}