package com.jettech.basic.fileimport.model;

import com.jettech.basic.base.BaseEnum;
import com.jettech.basic.database.mybatis.auth.DataScopeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * @ClassName ImportStatus
 * @Description 文件导入任务状态枚举
 * <AUTHOR>
 * @Date 2025/9/1 15:28
 */
@Getter
@AllArgsConstructor
@ApiModel(value = "ImportStatus", description = "文件导入任务状态-枚举")
public enum ImportStatus implements BaseEnum
{

    PENDING("待处理"),
    PROCESSING("处理中"),
    SUCCESS("成功"),
    FAILED("失败"),
    TIMEOUT("超时");


    @ApiModelProperty(value = "描述")
    private final String desc;

    public static ImportStatus match(String val, ImportStatus def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ImportStatus get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ImportStatus val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "文件导入任务状态枚举", allowableValues = "PENDING,PROCESSING,SUCCESS,FAILED,TIMEOUT",
            example = "PENDING")
    public String getCode()
    {
        return this.name();
    }

}