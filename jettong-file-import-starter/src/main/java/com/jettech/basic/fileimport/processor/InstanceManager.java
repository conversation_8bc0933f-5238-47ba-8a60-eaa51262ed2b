package com.jettech.basic.fileimport.processor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * @ClassName InstanceManager
 * @Description 实例管理器，用于生成和管理当前实例的标识
 * <AUTHOR>
 * @Date 2025/9/2 12:00
 */
@Component
@Slf4j
public class InstanceManager {

    private String instanceId;
    private LocalDateTime startTime;

    @PostConstruct
    public void init() {
        this.startTime = LocalDateTime.now();
        this.instanceId = generateInstanceId();
        log.info("实例启动，实例ID: {}, 启动时间: {}", instanceId, startTime);
    }

    /**
     * 获取当前实例ID
     */
    public String getInstanceId() {
        return instanceId;
    }

    /**
     * 获取实例启动时间
     */
    public LocalDateTime getStartTime() {
        return startTime;
    }

    /**
     * 检查指定的实例ID是否是当前实例
     */
    public boolean isCurrentInstance(String instanceId) {
        return this.instanceId.equals(instanceId);
    }

    /**
     * 生成实例ID
     * 格式：主机名-进程ID-启动时间-随机UUID前8位
     */
    private String generateInstanceId() {
        try {
            String hostname = InetAddress.getLocalHost().getHostName();
            String pid = getProcessId();
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String uuid = UUID.randomUUID().toString().substring(0, 8);
            
            return String.format("%s-%s-%s-%s", hostname, pid, timestamp, uuid);
        } catch (UnknownHostException e) {
            log.warn("无法获取主机名，使用默认值", e);
            String pid = getProcessId();
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String uuid = UUID.randomUUID().toString().substring(0, 8);
            
            return String.format("unknown-%s-%s-%s", pid, timestamp, uuid);
        }
    }

    /**
     * 获取进程ID
     */
    private String getProcessId() {
        try {
            String jvmName = java.lang.management.ManagementFactory.getRuntimeMXBean().getName();
            return jvmName.split("@")[0];
        } catch (Exception e) {
            log.warn("无法获取进程ID，使用随机值", e);
            return String.valueOf(System.currentTimeMillis() % 100000);
        }
    }
}
