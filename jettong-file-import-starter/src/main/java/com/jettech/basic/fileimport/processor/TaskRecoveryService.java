package com.jettech.basic.fileimport.processor;

import com.jettech.basic.cache.repository.CachePlusOps;
import com.jettech.basic.fileimport.config.ImportConfig;
import com.jettech.basic.fileimport.model.ImportStatus;
import com.jettech.basic.fileimport.model.ImportTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName TaskRecoveryService
 * @Description 任务恢复服务，处理服务重启后的执行中任务和僵尸任务
 * <AUTHOR>
 * @Date 2025/9/2 13:00
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TaskRecoveryService {

    private final TaskCacheManager taskCacheManager;
    private final InstanceManager instanceManager;
    private final ImportConfig importConfig;
    private final CachePlusOps cachePlusOps;

    /**
     * 应用启动完成后执行任务恢复
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("应用启动完成，开始执行任务恢复检查...");
        try {
            recoverTasks();
        } catch (Exception e) {
            log.error("任务恢复检查失败", e);
        }
    }

    /**
     * 定期检查和清理僵尸任务
     */
    @Scheduled(fixedRate = 300000) // 5分钟执行一次
    public void cleanupZombieTasks() {
        try {
            log.debug("开始检查僵尸任务...");
            cleanupZombieTasksInternal();
        } catch (Exception e) {
            log.error("清理僵尸任务失败", e);
        }
    }

    /**
     * 恢复任务状态
     */
    private void recoverTasks() {
        // 查找所有任务相关的缓存键
        Set<String> taskKeys = cachePlusOps.keys("import:task:*");
        
        int recoveredCount = 0;
        int zombieCount = 0;
        
        for (String taskKey : taskKeys) {
            try {
                String taskId = extractTaskIdFromKey(taskKey);
                ImportTask task = taskCacheManager.getTask(taskId);
                
                if (task == null) {
                    continue;
                }
                
                // 只处理执行中的任务
                if (task.getStatus() != ImportStatus.PROCESSING) {
                    continue;
                }
                
                if (shouldRecoverTask(task)) {
                    recoverTask(task);
                    recoveredCount++;
                } else if (isZombieTask(task)) {
                    markTaskAsFailed(task, "检测到僵尸任务，标记为失败");
                    zombieCount++;
                }
                
            } catch (Exception e) {
                log.error("处理任务恢复失败: {}", taskKey, e);
            }
        }
        
        log.info("任务恢复完成，恢复任务数: {}, 僵尸任务数: {}", recoveredCount, zombieCount);
    }

    /**
     * 清理僵尸任务
     */
    private void cleanupZombieTasksInternal() {
        Set<String> taskKeys = cachePlusOps.keys("import:task:*");
        
        int zombieCount = 0;
        
        for (String taskKey : taskKeys) {
            try {
                String taskId = extractTaskIdFromKey(taskKey);
                ImportTask task = taskCacheManager.getTask(taskId);
                
                if (task == null) {
                    continue;
                }
                
                // 只检查执行中的任务
                if (task.getStatus() == ImportStatus.PROCESSING && isZombieTask(task)) {
                    markTaskAsFailed(task, "检测到僵尸任务，标记为失败");
                    zombieCount++;
                }
                
            } catch (Exception e) {
                log.error("清理僵尸任务失败: {}", taskKey, e);
            }
        }
        
        if (zombieCount > 0) {
            log.info("清理僵尸任务完成，处理数量: {}", zombieCount);
        }
    }

    /**
     * 判断任务是否应该恢复
     */
    private boolean shouldRecoverTask(ImportTask task) {
        // 如果任务在当前实例上执行，且实例启动时间晚于任务开始时间，说明是重启恢复
        return instanceManager.isCurrentInstance(task.getInstanceId()) 
                && instanceManager.getStartTime().isAfter(task.getStartTime());
    }

    /**
     * 判断是否为僵尸任务
     */
    private boolean isZombieTask(ImportTask task) {
        if (task.getLastHeartbeat() == null) {
            // 如果没有心跳时间，使用开始时间判断
            return task.getStartTime().isBefore(
                    LocalDateTime.now().minusMinutes(importConfig.getTimeoutMinutes()));
        }
        
        // 如果心跳超时，认为是僵尸任务
        return task.getLastHeartbeat().isBefore(
                LocalDateTime.now().minusMinutes(importConfig.getTimeoutMinutes()));
    }

    /**
     * 恢复任务
     */
    private void recoverTask(ImportTask task) {
        log.info("恢复任务: {}, 原状态: {}", task.getTaskId(), task.getStatus());
        
        // 将任务标记为失败，因为服务重启导致任务中断
        markTaskAsFailed(task, "服务重启导致任务中断");
    }

    /**
     * 将任务标记为失败
     */
    private void markTaskAsFailed(ImportTask task, String reason) {
        task.setStatus(ImportStatus.FAILED);
        task.setMessage(reason);
        task.setEndTime(LocalDateTime.now());
        task.setProgress(task.getProgress()); // 保持当前进度
        
        taskCacheManager.updateTask(task);
        
        log.info("任务已标记为失败: {}, 原因: {}", task.getTaskId(), reason);
    }

    /**
     * 从缓存键中提取任务ID
     */
    private String extractTaskIdFromKey(String taskKey) {
        // taskKey格式: import:task:taskId
        String[] parts = taskKey.split(":");
        return parts.length >= 3 ? parts[2] : null;
    }

    /**
     * 获取所有执行中的任务（用于监控和调试）
     */
    public List<ImportTask> getProcessingTasks() {
        Set<String> taskKeys = cachePlusOps.keys("import:task:*");
        
        return taskKeys.stream()
                .map(this::extractTaskIdFromKey)
                .filter(taskId -> taskId != null)
                .map(taskCacheManager::getTask)
                .filter(task -> task != null && task.getStatus() == ImportStatus.PROCESSING)
                .collect(Collectors.toList());
    }

    /**
     * 手动触发任务恢复（用于管理接口）
     */
    public void manualRecovery() {
        log.info("手动触发任务恢复...");
        recoverTasks();
    }

    /**
     * 手动清理僵尸任务（用于管理接口）
     */
    public void manualCleanupZombieTasks() {
        log.info("手动触发僵尸任务清理...");
        cleanupZombieTasksInternal();
    }
}
