package com.jettech.basic.fileimport.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @ClassName ImportResult
 * @Description 导入处理结果
 * <AUTHOR>
 * @Date 2025/9/1 16:06
 */
@Data
@Builder
public class ImportResult
{
    private boolean success;
    private String message;
    private int totalRecords;
    private int successRecords;
    private int failedRecords;
    private List<String> errorMessages;
}
