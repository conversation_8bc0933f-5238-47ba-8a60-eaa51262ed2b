# JettoNG File Import Starter

通用文件导入组件，提供异步文件导入、进度监控、任务管理

## 特性

- **异步导入**: 支持大文件异步处理，不阻塞主线程
- **实时进度**: SSE实时推送导入进度
- **任务管理**: 基于Redis的分布式任务状态管理
- ️**资源控制**: 支持并发数、文件大小、总容量限制
- **多维查询**: 支持按用户、处理器等维度查询任务
- **任务清理**: 支持删除非执行中的任务
- **故障恢复**: 服务重启后自动处理遗留任务


## 快速开始

### 1. 引入依赖

在你的 `pom.xml` 中添加：

```xml
<dependency>
    <groupId>com.jettech.basic</groupId>
    <artifactId>jettong-file-import-starter</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 2. 配置

在 nacos  中添加配置：

```yaml
jettong:
  file-import:
    # 最大并发导入数
    max-concurrent-imports: 100
    # 最大文件大小(MB)
    max-file-size-mb: 100
    # 总导入文件大小限制(MB)
    max-total-size-mb: 2000
    # 任务活动超时时间(分钟) -- 当任务进度一直无变化时触发僵尸任务清理
    timeout-minutes: 30
    # 任务缓存过期时间(小时) -- 超过该时间的任务将被自动清理
    task-cache-expire-hours: 24

```

### 3. 实现文件处理器

创建自定义的文件处理器：

```java
@Component
public class ExcelUserImportProcessor implements FileProcessor {
    
    @Override
    public String getProcessorName() {
        return "excel-user-import";
    }
    
    @Override
    public Set<String> getSupportedFileTypes() {
        return Set.of("xlsx", "xls");
    }
    
    @Override
    public ImportResult processFile(MultipartFile file, String taskId, 
                                  Consumer<ImportProgressEvent> progressCallback) {
        try {
            // 1. 解析文件
            List<UserData> users = parseExcelFile(file);
            
            // 2. 处理数据并更新进度
            int total = users.size();
            int processed = 0;
            int success = 0;
            int failed = 0;
            
            for (UserData user : users) {
                try {
                    // 处理单条数据
                    userService.saveUser(user);
                    success++;
                } catch (Exception e) {
                    failed++;
                    log.error("处理用户数据失败: {}", user, e);
                }
                
                processed++;
                
                // 更新进度（每10条或最后一条）
                if (processed % 10 == 0 || processed == total) {
                    int progress = (int) ((double) processed / total * 100);
                    progressCallback.accept(ImportProgressEvent.builder()
                            .taskId(taskId)
                            .status(ImportStatus.PROCESSING)
                            .progress(progress)
                            .message(String.format("已处理 %d/%d 条记录", processed, total))
                            .timestamp(LocalDateTime.now())
                            .build());
                }
            }
            
            // 3. 返回结果
            return ImportResult.builder()
                    .success(failed == 0)
                    .totalRecords(total)
                    .successRecords(success)
                    .failedRecords(failed)
                    .message(String.format("导入完成，成功: %d, 失败: %d", success, failed))
                    .build();
                    
        } catch (Exception e) {
            log.error("文件处理失败", e);
            return ImportResult.builder()
                    .success(false)
                    .message("文件处理失败: " + e.getMessage())
                    .build();
        }
    }
    
    private List<UserData> parseExcelFile(MultipartFile file) {
        // 实现Excel文件解析逻辑
        // 可以使用EasyExcel、POI等库
        return Collections.emptyList();
    }
}
```

### 4. 注册处理器


```java
@Configuration
public class FileImportConfig {
    
    @Autowired
    private UniversalImportService importService;
    
    @PostConstruct
    public void registerProcessors() {
    
         importService.registerProcessor(new ExcelUserImportProcessor());
    }
}
```

## API 接口文档

### 1. 提交导入任务

**POST** `/file/import/submit`

**参数:**
- `file`: 上传的文件 (multipart/form-data)
- `processorName`: 处理器名称 (string)
- `userId`: 用户ID (string)
- `serviceName`: 服务名称 (string)

**响应:**
```json
{
  "taskId": "uuid-task-id",
  "status": "submitted",
  "message": "导入任务已提交"
}
```

### 2. 获取任务状态

**GET** `/file/import/status/{taskId}`

**响应:**
```json
{
  "taskId": "uuid-task-id",
  "fileName": "users.xlsx",
  "fileSize": 1024000,
  "status": "PROCESSING",
  "progress": 65,
  "message": "已处理 650/1000 条记录",
  "startTime": "2025-09-02T10:00:00",
  "endTime": null,
  "userId": "user-123",
  "serviceName": "user-service",
  "processorName": "excel-user-import"
}
```

### 3. SSE 进度监听

**GET** `/file/import/progress/{taskId}`

**响应:** Server-Sent Events 流

```javascript
// 前端监听示例
const eventSource = new EventSource('/file/import/progress/task-123');

eventSource.onmessage = function(event) {
    const progress = JSON.parse(event.data);
    console.log('进度更新:', progress);
    
    // 更新UI
    updateProgressBar(progress.progress);
    updateMessage(progress.message);
    
    // 任务完成时关闭连接
    if (progress.status === 'SUCCESS' || progress.status === 'FAILED') {
        eventSource.close();
    }
};
```

### 4. 查询任务列表

**GET** `/file/import/tasks/user/{userId}`
查询指定用户的所有任务

**GET** `/file/import/tasks/processor/{processorName}`
查询指定处理器的所有任务

**GET** `/file/import/tasks/user/{userId}/processor/{processorName}`
查询指定用户和处理器的任务

**GET** `/file/import/tasks?userId={userId}&processorName={processorName}`
灵活查询（参数可选）

### 5. 删除任务

**DELETE** `/file/import/tasks/{taskId}`
删除单个任务

**DELETE** `/file/import/tasks`
批量删除任务

**请求体:**
```json
["task-id-1", "task-id-2", "task-id-3"]
```

**响应:**
```json
{
  "total": 3,
  "success": 2,
  "failed": 1,
  "details": {
    "task-id-1": "删除成功",
    "task-id-2": "删除成功", 
    "task-id-3": "任务正在执行中，无法删除"
  }
}
```

### 6. 系统监控

**GET** `/file/import/resource-usage`
获取系统资源使用情况

**GET** `/file/import/tasks/processing`
获取所有执行中的任务


## 任务状态

- `PENDING`: 待处理
- `PROCESSING`: 处理中
- `SUCCESS`: 成功
- `FAILED`: 失败
- `TIMEOUT`: 超时

## 错误处理


```java
@Component
@Slf4j
public class MyFileProcessor implements FileProcessor {
    
    @Override
    public ImportResult processFile(MultipartFile file, String taskId, 
                                  Consumer<ImportProgressEvent> progressCallback) {
        
        // 1. 参数验证
        if (file.isEmpty()) {
            return ImportResult.builder()
                    .success(false)
                    .message("文件为空")
                    .build();
        }
        
        // 2. 分批处理大文件
        int batchSize = 100;
        List<DataRecord> allRecords = parseFile(file);
        
        for (int i = 0; i < allRecords.size(); i += batchSize) {
            List<DataRecord> batch = allRecords.subList(i, 
                    Math.min(i + batchSize, allRecords.size()));
            
            // 处理批次
            processBatch(batch);
            
            // 更新进度
            int progress = (int) ((double) (i + batch.size()) / allRecords.size() * 100);
            progressCallback.accept(createProgressEvent(taskId, progress));
        }
        
        return createSuccessResult();
    }
}
```

### 2. 前端集成示例

```javascript
class FileImportManager {
    
    async submitFile(file, processorName, userId, serviceName) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('processorName', processorName);
        formData.append('userId', userId);
        formData.append('serviceName', serviceName);
        
        const response = await fetch('/file/import/submit', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.taskId) {
            this.monitorProgress(result.taskId);
        }
        
        return result;
    }
    
    monitorProgress(taskId) {
        const eventSource = new EventSource(`/file/import/progress/${taskId}`);
        
        eventSource.onmessage = (event) => {
            const progress = JSON.parse(event.data);
            this.updateUI(progress);
            
            if (['SUCCESS', 'FAILED', 'TIMEOUT'].includes(progress.status)) {
                eventSource.close();
                this.onComplete(progress);
            }
        };
        
        eventSource.onerror = () => {
            console.error('SSE连接错误');
            eventSource.close();
        };
    }
}
```

