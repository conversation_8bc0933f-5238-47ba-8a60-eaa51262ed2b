# 文件导入任务管理功能说明

## 概述

本文档介绍了基于CacheOps的文件导入任务管理功能，包括任务查询、删除和恢复机制。

## 主要功能

### 1. 任务缓存管理

- **存储方式**: 使用CacheOps（Redis）替代内存Map存储任务数据
- **缓存键结构**:
  - 主任务: `import:task:{taskId}`
  - 用户索引: `import:user:{userId}`
  - 处理器索引: `import:processor:{processorName}`
  - 用户+处理器索引: `import:user:{userId}:processor:{processorName}`
- **过期时间**: 可配置，默认2小时

### 2. 任务查询接口

#### 根据用户ID查询
```http
GET /file/import/tasks/user/{userId}
```

#### 根据处理器名称查询
```http
GET /file/import/tasks/processor/{processorName}
```

#### 根据用户ID和处理器名称查询
```http
GET /file/import/tasks/user/{userId}/processor/{processorName}
```

#### 灵活查询（支持可选参数）
```http
GET /file/import/tasks?userId={userId}&processorName={processorName}
```

### 3. 任务删除接口

#### 删除单个任务
```http
DELETE /file/import/tasks/{taskId}
```

响应示例：
```json
{
  "taskId": "task-123",
  "result": "删除成功",
  "success": true
}
```

#### 批量删除任务
```http
DELETE /file/import/tasks
Content-Type: application/json

["task-123", "task-456", "task-789"]
```

响应示例：
```json
{
  "total": 3,
  "success": 2,
  "failed": 1,
  "details": {
    "task-123": "删除成功",
    "task-456": "删除成功",
    "task-789": "任务正在执行中，无法删除"
  }
}
```

**注意**: 只能删除非执行中的任务（状态不为PROCESSING）

### 4. 服务重启后的任务处理

#### 多实例场景处理

1. **实例标识**: 每个服务实例都有唯一的instanceId（每次启动生成）
2. **任务恢复**: 服务启动时自动检查执行中的任务
3. **僵尸任务清理**: 定期检查和清理超时的执行中任务

#### 处理策略

- **遗留任务检测**: 基于任务最后活动时间与实例启动时间比较
- **恢复逻辑**: 如果任务的最后活动时间早于实例启动时间，标记为失败
- **僵尸任务**: 超过配置超时时间的任务自动标记为失败
- **多实例安全**: 每个实例启动时都会检查并处理遗留任务

### 5. 管理接口

#### 查看执行中的任务
```http
GET /file/import/tasks/processing
```

#### 手动触发任务恢复
```http
POST /file/import/admin/recover-tasks
```

#### 手动清理僵尸任务
```http
POST /file/import/admin/cleanup-zombie-tasks
```

## 配置说明

```yaml
jettong:
  file-import:
    # 最大并发导入数
    max-concurrent-imports: 100
    # 最大文件大小(MB)
    max-file-size-mb: 100
    # 总导入文件大小限制(MB)
    max-total-size-mb: 2000
    # 导入超时时间(分钟)
    timeout-minutes: 30
    # 任务缓存过期时间(小时)
    task-cache-expire-hours: 2
```

## 任务状态说明

- **PENDING**: 待处理
- **PROCESSING**: 处理中（不可删除）
- **SUCCESS**: 成功
- **FAILED**: 失败
- **TIMEOUT**: 超时

## 实例标识格式

```
{hostname}-{processId}-{timestamp}-{uuid}
```

例如: `server01-12345-20250902130000-a1b2c3d4`

## 心跳机制

- 任务创建时设置初始心跳时间
- 任务状态更新时刷新心跳时间
- 用于检测僵尸任务

## 任务恢复逻辑详解

### 恢复判断条件

```java
// 获取任务的最后活动时间
LocalDateTime taskLastActivity = task.getLastHeartbeat() != null
    ? task.getLastHeartbeat()
    : task.getStartTime();

// 如果任务的最后活动时间早于当前实例启动时间，认为是遗留任务
boolean shouldRecover = taskLastActivity.isBefore(instanceStartTime);
```

### 场景说明

1. **正常情况**: 任务在实例启动后创建，最后活动时间晚于启动时间
2. **遗留任务**: 任务在实例启动前创建，最后活动时间早于启动时间
3. **多实例**: 每个实例启动时都会检查所有执行中的任务，处理遗留任务

### 示例时间线

```
10:00 - 实例A启动，开始执行任务Task1
10:05 - Task1最后心跳时间
10:10 - 实例A重启
10:11 - 实例A重新启动，检查Task1
        Task1最后活动时间(10:05) < 实例启动时间(10:11)
        → 判定为遗留任务，标记为失败
```

## 使用示例

### 1. 提交导入任务
```http
POST /file/import/submit
Content-Type: multipart/form-data

file: [文件]
processorName: excel-processor
userId: user-123
```

### 2. 查询用户的所有任务
```http
GET /file/import/tasks/user/user-123
```

### 3. 删除已完成的任务
```http
DELETE /file/import/tasks/task-123
```

### 4. 批量删除多个任务
```http
DELETE /file/import/tasks
Content-Type: application/json

["task-123", "task-456"]
```

## 注意事项

1. **删除限制**: 只能删除非执行中的任务
2. **缓存依赖**: 需要配置Redis作为缓存存储
3. **多实例**: 支持多实例部署，任务状态共享
4. **恢复机制**: 服务重启时自动处理执行中的任务
5. **监控**: 提供管理接口用于监控和手动干预

## 错误处理

- 任务不存在: 返回相应错误信息
- 任务执行中: 无法删除，返回错误信息
- 系统异常: 记录日志并返回错误响应

## 性能考虑

- 使用Redis缓存提高查询性能
- 索引结构支持高效的多维度查询
- 定期清理过期数据避免内存泄漏
- 批量操作减少网络开销
