# 使用示例

## 完整示例项目

### 项目结构

```
your-project/
├── src/main/java/
│   ├── com/example/
│   │   ├── Application.java
│   │   ├── config/
│   │   │   └── FileImportConfig.java
│   │   ├── processor/
│   │   │   ├── UserExcelProcessor.java
│   │   │   ├── ProductCsvProcessor.java
│   │   │   └── OrderJsonProcessor.java
│   │   ├── controller/
│   │   │   └── FileUploadController.java
│   │   ├── service/
│   │   │   ├── UserService.java
│   │   │   └── ProductService.java
│   │   └── model/
│   │       ├── User.java
│   │       └── Product.java
│   └── resources/
│       ├── application.yml
│       └── static/
│           └── upload.html
└── pom.xml
```

### 1. 主应用类

```java
@SpringBootApplication
@EnableScheduling
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 2. 用户Excel导入处理器

```java
@Component
@Slf4j
public class UserExcelProcessor implements FileProcessor {
    
    @Autowired
    private UserService userService;
    
    @Override
    public String getProcessorName() {
        return "user-excel-import";
    }
    
    @Override
    public Set<String> getSupportedFileTypes() {
        return Set.of("xlsx", "xls");
    }
    
    @Override
    public ImportResult processFile(MultipartFile file, String taskId, 
                                  Consumer<ImportProgressEvent> progressCallback) {
        
        log.info("开始处理用户Excel文件: {}", file.getOriginalFilename());
        
        try {
            // 1. 解析Excel文件
            List<UserData> users = parseExcelFile(file);
            
            if (users.isEmpty()) {
                return ImportResult.builder()
                        .success(false)
                        .message("文件中没有有效数据")
                        .build();
            }
            
            // 2. 数据验证和处理
            int total = users.size();
            int processed = 0;
            int success = 0;
            int failed = 0;
            List<String> errors = new ArrayList<>();
            
            for (UserData userData : users) {
                try {
                    // 数据验证
                    validateUserData(userData);
                    
                    // 保存用户
                    User user = convertToUser(userData);
                    userService.saveUser(user);
                    success++;
                    
                } catch (ValidationException e) {
                    failed++;
                    errors.add(String.format("第%d行: %s", processed + 1, e.getMessage()));
                    log.warn("用户数据验证失败: {}", e.getMessage());
                } catch (Exception e) {
                    failed++;
                    errors.add(String.format("第%d行: 处理失败", processed + 1));
                    log.error("处理用户数据失败", e);
                }
                
                processed++;
                
                // 更新进度（每50条或最后一条）
                if (processed % 50 == 0 || processed == total) {
                    int progress = (int) ((double) processed / total * 100);
                    String message = String.format("已处理 %d/%d 条记录，成功: %d，失败: %d", 
                            processed, total, success, failed);
                    
                    progressCallback.accept(ImportProgressEvent.builder()
                            .taskId(taskId)
                            .status(ImportStatus.PROCESSING)
                            .progress(progress)
                            .message(message)
                            .timestamp(LocalDateTime.now())
                            .build());
                }
            }
            
            // 3. 返回结果
            String resultMessage = String.format("导入完成！总计: %d，成功: %d，失败: %d", 
                    total, success, failed);
            
            if (!errors.isEmpty() && errors.size() <= 10) {
                resultMessage += "\n错误详情:\n" + String.join("\n", errors);
            } else if (errors.size() > 10) {
                resultMessage += String.format("\n错误详情: 共%d个错误，仅显示前10个:\n%s", 
                        errors.size(), String.join("\n", errors.subList(0, 10)));
            }
            
            return ImportResult.builder()
                    .success(failed == 0)
                    .totalRecords(total)
                    .successRecords(success)
                    .failedRecords(failed)
                    .message(resultMessage)
                    .build();
                    
        } catch (Exception e) {
            log.error("文件处理失败", e);
            return ImportResult.builder()
                    .success(false)
                    .message("文件处理失败: " + e.getMessage())
                    .build();
        }
    }
    
    private List<UserData> parseExcelFile(MultipartFile file) throws IOException {
        List<UserData> users = new ArrayList<>();
        
        try (InputStream inputStream = file.getInputStream()) {
            // 使用EasyExcel解析
            EasyExcel.read(inputStream, UserData.class, new AnalysisEventListener<UserData>() {
                @Override
                public void invoke(UserData data, AnalysisContext context) {
                    users.add(data);
                }
                
                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel解析完成，共{}条记录", users.size());
                }
            }).sheet().doRead();
        }
        
        return users;
    }
    
    private void validateUserData(UserData userData) throws ValidationException {
        if (StringUtils.isBlank(userData.getName())) {
            throw new ValidationException("用户名不能为空");
        }
        if (StringUtils.isBlank(userData.getEmail())) {
            throw new ValidationException("邮箱不能为空");
        }
        if (!isValidEmail(userData.getEmail())) {
            throw new ValidationException("邮箱格式不正确");
        }
        if (userService.existsByEmail(userData.getEmail())) {
            throw new ValidationException("邮箱已存在");
        }
    }
    
    private User convertToUser(UserData userData) {
        return User.builder()
                .name(userData.getName())
                .email(userData.getEmail())
                .phone(userData.getPhone())
                .department(userData.getDepartment())
                .createTime(LocalDateTime.now())
                .build();
    }
    
    private boolean isValidEmail(String email) {
        return email != null && email.matches("^[A-Za-z0-9+_.-]+@(.+)$");
    }
}
```

### 3. 产品CSV导入处理器

```java
@Component
@Slf4j
public class ProductCsvProcessor implements FileProcessor {
    
    @Autowired
    private ProductService productService;
    
    @Override
    public String getProcessorName() {
        return "product-csv-import";
    }
    
    @Override
    public Set<String> getSupportedFileTypes() {
        return Set.of("csv");
    }
    
    @Override
    public ImportResult processFile(MultipartFile file, String taskId, 
                                  Consumer<ImportProgressEvent> progressCallback) {
        
        log.info("开始处理产品CSV文件: {}", file.getOriginalFilename());
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            
            List<String> lines = reader.lines().collect(Collectors.toList());
            
            if (lines.size() <= 1) {
                return ImportResult.builder()
                        .success(false)
                        .message("CSV文件没有数据行")
                        .build();
            }
            
            // 跳过标题行
            List<String> dataLines = lines.subList(1, lines.size());
            int total = dataLines.size();
            int processed = 0;
            int success = 0;
            int failed = 0;
            
            for (String line : dataLines) {
                try {
                    ProductData productData = parseCsvLine(line);
                    Product product = convertToProduct(productData);
                    productService.saveProduct(product);
                    success++;
                } catch (Exception e) {
                    failed++;
                    log.error("处理产品数据失败: {}", line, e);
                }
                
                processed++;
                
                // 更新进度
                if (processed % 100 == 0 || processed == total) {
                    int progress = (int) ((double) processed / total * 100);
                    progressCallback.accept(ImportProgressEvent.builder()
                            .taskId(taskId)
                            .status(ImportStatus.PROCESSING)
                            .progress(progress)
                            .message(String.format("已处理 %d/%d 条记录", processed, total))
                            .timestamp(LocalDateTime.now())
                            .build());
                }
            }
            
            return ImportResult.builder()
                    .success(failed == 0)
                    .totalRecords(total)
                    .successRecords(success)
                    .failedRecords(failed)
                    .message(String.format("导入完成，成功: %d, 失败: %d", success, failed))
                    .build();
                    
        } catch (Exception e) {
            log.error("CSV文件处理失败", e);
            return ImportResult.builder()
                    .success(false)
                    .message("CSV文件处理失败: " + e.getMessage())
                    .build();
        }
    }
    
    private ProductData parseCsvLine(String line) {
        String[] fields = line.split(",");
        return ProductData.builder()
                .name(fields[0])
                .price(new BigDecimal(fields[1]))
                .category(fields[2])
                .description(fields.length > 3 ? fields[3] : "")
                .build();
    }
    
    private Product convertToProduct(ProductData data) {
        return Product.builder()
                .name(data.getName())
                .price(data.getPrice())
                .category(data.getCategory())
                .description(data.getDescription())
                .createTime(LocalDateTime.now())
                .build();
    }
}
```

### 4. 前端上传页面

```html
<!DOCTYPE html>
<html>
<head>
    <title>文件导入</title>
    <meta charset="UTF-8">
    <style>
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
        .progress-bar { width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: #4CAF50; transition: width 0.3s; }
        .task-item { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .status-processing { border-left: 4px solid #2196F3; }
        .status-success { border-left: 4px solid #4CAF50; }
        .status-failed { border-left: 4px solid #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <h1>文件导入系统</h1>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <p>点击选择文件或拖拽文件到此处</p>
            <input type="file" id="fileInput" style="display: none;" accept=".xlsx,.xls,.csv">
        </div>
        
        <div>
            <label>处理器类型:</label>
            <select id="processorSelect">
                <option value="user-excel-import">用户Excel导入</option>
                <option value="product-csv-import">产品CSV导入</option>
            </select>
        </div>
        
        <button onclick="uploadFile()">开始导入</button>
        
        <div id="taskList">
            <h3>导入任务</h3>
        </div>
    </div>

    <script>
        class FileImportManager {
            constructor() {
                this.tasks = new Map();
                this.loadTasks();
            }
            
            async uploadFile(file, processorName) {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('processorName', processorName);
                formData.append('userId', 'current-user');
                formData.append('serviceName', 'demo-service');
                
                try {
                    const response = await fetch('/file/import/submit', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    if (result.taskId) {
                        this.addTask(result.taskId, file.name, processorName);
                        this.monitorProgress(result.taskId);
                    }
                    
                    return result;
                } catch (error) {
                    console.error('上传失败:', error);
                    alert('上传失败: ' + error.message);
                }
            }
            
            monitorProgress(taskId) {
                const eventSource = new EventSource(`/file/import/progress/${taskId}`);
                
                eventSource.onmessage = (event) => {
                    const progress = JSON.parse(event.data);
                    this.updateTask(taskId, progress);
                    
                    if (['SUCCESS', 'FAILED', 'TIMEOUT'].includes(progress.status)) {
                        eventSource.close();
                    }
                };
                
                eventSource.onerror = () => {
                    console.error('SSE连接错误');
                    eventSource.close();
                };
            }
            
            addTask(taskId, fileName, processorName) {
                const task = {
                    taskId,
                    fileName,
                    processorName,
                    status: 'PENDING',
                    progress: 0,
                    message: '等待处理...'
                };
                
                this.tasks.set(taskId, task);
                this.renderTask(task);
            }
            
            updateTask(taskId, progress) {
                const task = this.tasks.get(taskId);
                if (task) {
                    Object.assign(task, progress);
                    this.renderTask(task);
                }
            }
            
            renderTask(task) {
                let taskElement = document.getElementById(`task-${task.taskId}`);
                
                if (!taskElement) {
                    taskElement = document.createElement('div');
                    taskElement.id = `task-${task.taskId}`;
                    taskElement.className = 'task-item';
                    document.getElementById('taskList').appendChild(taskElement);
                }
                
                taskElement.className = `task-item status-${task.status.toLowerCase()}`;
                taskElement.innerHTML = `
                    <div><strong>${task.fileName}</strong> (${task.processorName})</div>
                    <div>状态: ${task.status}</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${task.progress}%"></div>
                    </div>
                    <div>${task.message}</div>
                    <div style="margin-top: 10px;">
                        <button onclick="fileManager.deleteTask('${task.taskId}')" 
                                ${task.status === 'PROCESSING' ? 'disabled' : ''}>删除</button>
                    </div>
                `;
            }
            
            async deleteTask(taskId) {
                try {
                    const response = await fetch(`/file/import/tasks/${taskId}`, {
                        method: 'DELETE'
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        this.tasks.delete(taskId);
                        document.getElementById(`task-${taskId}`).remove();
                    } else {
                        alert('删除失败: ' + result.result);
                    }
                } catch (error) {
                    console.error('删除失败:', error);
                    alert('删除失败: ' + error.message);
                }
            }
            
            async loadTasks() {
                try {
                    const response = await fetch('/file/import/tasks?userId=current-user');
                    const tasks = await response.json();
                    
                    tasks.forEach(task => {
                        this.tasks.set(task.taskId, task);
                        this.renderTask(task);
                    });
                } catch (error) {
                    console.error('加载任务失败:', error);
                }
            }
        }
        
        const fileManager = new FileImportManager();
        
        function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const processorSelect = document.getElementById('processorSelect');
            
            if (!fileInput.files[0]) {
                alert('请选择文件');
                return;
            }
            
            fileManager.uploadFile(fileInput.files[0], processorSelect.value);
        }
        
        // 拖拽上传
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.backgroundColor = '#f0f0f0';
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.backgroundColor = '';
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.backgroundColor = '';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('fileInput').files = files;
            }
        });
    </script>
</body>
</html>
```

### 5. 配置文件

```yaml
# application.yml
server:
  port: 8080

spring:
  application:
    name: file-import-demo
  
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

  datasource:
    url: ***********************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver

jettong:
  file-import:
    max-concurrent-imports: 50
    max-file-size-mb: 50
    max-total-size-mb: 1000
    timeout-minutes: 30
    task-cache-expire-hours: 2

logging:
  level:
    com.jettech.basic.fileimport: DEBUG
    com.example: DEBUG
```

这个完整示例展示了如何：
1. 实现不同类型的文件处理器
2. 创建前端上传界面
3. 监控导入进度
4. 管理导入任务

您可以基于这个示例快速搭建自己的文件导入系统。
