<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.jettech.basic</groupId>
    <artifactId>jettong-dependencies</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>
    <description>jettong-standard-server项目依赖管理</description>

    <properties>
        <spring.boot.version>2.5.4</spring.boot.version>
        <spring.cloud.version>2020.0.3</spring.cloud.version>
        <spring.cloud.openfeign.version>3.0.8</spring.cloud.openfeign.version>
        <logback.version>1.2.12</logback.version>
        <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
        <maven-javadoc-plugin.version>3.2.0</maven-javadoc-plugin.version>
        <hutool.version>5.8.18</hutool.version>
        <druid.version>1.2.6</druid.version>
        <mysql.version>8.0.22</mysql.version>
        <mybatis.version>3.5.7</mybatis.version>
        <mybatis-spring.version>2.0.6</mybatis-spring.version>
        <mybatisplus.version>*******</mybatisplus.version>
        <dynamic.datasource.version>3.2.1</dynamic.datasource.version>
        <p6spy.version>3.9.1</p6spy.version>
        <fastjson.version>2.0.31</fastjson.version>
        <transmittable-thread-local.version>2.12.1</transmittable-thread-local.version>
        <knife4j.version>2.0.9</knife4j.version>
        <springfox.version>2.10.5</springfox.version>
        <swagger-models.version>1.5.22</swagger-models.version>
        <dozer.version>6.5.0</dozer.version>
        <easy-captcha.version>1.6.2</easy-captcha.version>
        <easypoi.version>4.2.0</easypoi.version>
        <guava.version>30.1.1-jre</guava.version>
        <commons.collections4.version>4.4</commons.collections4.version>
        <commons.collections.version>3.2.2</commons.collections.version>
        <commons-lang.version>2.6</commons-lang.version>
        <commons-io.version>2.8.0</commons-io.version>
        <commons-logging.version>1.2</commons-logging.version>
        <commons-net.version>3.4</commons-net.version>
        <ant.version>1.10.13</ant.version>
        <antlr-runtime.version>3.5.2</antlr-runtime.version>
        <svnkit.version>1.8.12</svnkit.version>
        <asm.version>5.0.4</asm.version>
        <xxl-job.version>2.3.1</xxl-job.version>
        <jjwt.version>0.11.2</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <s3.version>2.20.26</s3.version>
        <esdk-sdk-obs.version>3.23.5</esdk-sdk-obs.version>
        <activiti.version>6.0.0</activiti.version>
        <qcloudsms.version>1.0.6</qcloudsms.version>
        <lombok.version>1.18.20</lombok.version>
        <javassist.version>3.25.0-GA</javassist.version>
        <antisamy.version>1.6.4</antisamy.version>
        <protostuff.version>1.7.4</protostuff.version>
        <bitwalker.version>1.21</bitwalker.version>
        <gson.version>2.8.2</gson.version>
        <jsoup.version>1.11.3</jsoup.version>
        <log4j.version>2.17.0</log4j.version>
        <truelicense.version>1.32</truelicense.version>
        <larksuite-oapi.version>1.0.18-rc8</larksuite-oapi.version>
        <rocketmq.version>2.2.1</rocketmq.version>
        <diff-utils.version>1.3.0</diff-utils.version>
        <fast-md5.version>2.7.1</fast-md5.version>
    </properties>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${spring.cloud.openfeign.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-openfeign-core</artifactId>
                <version>${spring.cloud.openfeign.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <artifactId>logback-classic</artifactId>
                <groupId>ch.qos.logback</groupId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <artifactId>logback-core</artifactId>
                <groupId>ch.qos.logback</groupId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>${commons-logging.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons-net.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.ant</groupId>
                <artifactId>ant</artifactId>
                <version>${ant.version}</version>
            </dependency>

            <!-- svn -->
            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>antlr-runtime</artifactId>
                <version>${antlr-runtime.version}</version>
            </dependency>

            <dependency>
                <groupId>org.tmatesoft.svnkit</groupId>
                <artifactId>svnkit</artifactId>
                <version>${svnkit.version}</version>
            </dependency>

            <!-- 识别浏览器 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic.datasource.version}</version>
            </dependency>

            <!-- mybatis-plus相关jar -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-core</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>

            <!-- 连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 打印完整sql语句，便于开发人员本地环境调试，其他环境可以关闭 -->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <!-- mysql连接 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <!-- 对象转换 -->
            <dependency>
                <groupId>com.github.dozermapper</groupId>
                <artifactId>dozer-core</artifactId>
                <version>${dozer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.dozermapper</groupId>
                <artifactId>dozer-spring4</artifactId>
                <version>${dozer.version}</version>
            </dependency>

            <!-- swagger相关jar -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-dependencies</artifactId>
                <version>${knife4j.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-core</artifactId>
                <version>${springfox.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger-models.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger-models.version}</version>
            </dependency>

            <!-- jwt相关jar -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!--minio文件系统 -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <!-- S3 模块 -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>${s3.version}</version>
            </dependency>

            <!-- 华为云OBS -->
            <dependency>
                <groupId>com.huaweicloud</groupId>
                <artifactId>esdk-obs-java-bundle</artifactId>
                <version>${esdk-sdk-obs.version}</version>
            </dependency>

            <!-- XXS相关jar -->
            <dependency>
                <groupId>org.owasp.antisamy</groupId>
                <artifactId>antisamy</artifactId>
                <version>${antisamy.version}</version>
            </dependency>

            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
            </dependency>

            <!-- XXL-JOB相关jar -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <!-- 序列化 -->
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-core</artifactId>
                <version>${protostuff.version}</version>
            </dependency>
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-runtime</artifactId>
                <version>${protostuff.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>


            <!-- 验证码 -->
            <dependency>
                <groupId>com.github.whvcse</groupId>
                <artifactId>easy-captcha</artifactId>
                <version>${easy-captcha.version}</version>
            </dependency>

            <!-- 导入导出相关jar -->
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-spring-boot-starter</artifactId>
                <version>${easypoi.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-web</artifactId>
                <version>${easypoi.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-annotation</artifactId>
                <version>${easypoi.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-base</artifactId>
                <version>${easypoi.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>javassist</artifactId>
                        <groupId>org.javassist</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- json 相关jar -->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <!-- java基础工具包，包含对文件、流、加解密、转码、excel处理、word处理等工具类 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- 在使用线程池等会缓存线程的组件情况下传递ThreadLocal，日志模块使用 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!-- Google的 Java项目广泛依赖 的核心库，例如：集合 [collections] 、缓存 [caching] 、原生类型支持 [primitives support] 、并发库 [concurrency libraries] 、通用注解 [common annotations] 、字符串处理 [string processing] 、I/O 等等 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!-- Java 字节码,例如反射等 -->
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${javassist.version}</version>
            </dependency>

            <!-- log4j -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- licenses 依赖包 -->
            <dependency>
                <groupId>de.schlichtherle.truelicense</groupId>
                <artifactId>truelicense-core</artifactId>
                <version>${truelicense.version}</version>
            </dependency>
            <dependency>
                <groupId>de.schlichtherle.truelicense</groupId>
                <artifactId>truelicense-xml</artifactId>
                <version>${truelicense.version}</version>
            </dependency>
            <dependency>
                <groupId>de.schlichtherle.truelicense</groupId>
                <artifactId>truelicense-swing</artifactId>
                <version>${truelicense.version}</version>
            </dependency>

            <!-- 飞书sdk -->
            <dependency>
                <groupId>com.larksuite.oapi</groupId>
                <artifactId>larksuite-oapi</artifactId>
                <version>${larksuite-oapi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>

            <!-- google对比工具 -->
            <dependency>
                <groupId>com.googlecode.java-diff-utils</groupId>
                <artifactId>diffutils</artifactId>
                <version>${diff-utils.version}</version>
            </dependency>

            <!-- fast-md5 -->
            <dependency>
                <groupId>com.joyent.util</groupId>
                <artifactId>fast-md5</artifactId>
                <version>${fast-md5.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>jettech-release</id>
            <name>Jettech Releases Repository</name>
            <url>http://repo.jettech.com/repository/jettech-release/</url>
        </repository>
        <snapshotRepository>
            <id>jettech-snapshots</id>
            <uniqueVersion>false</uniqueVersion>
            <name>Jettech Snapshots  Repository</name>
            <url>http://repo.jettech.com/repository/jettech-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
