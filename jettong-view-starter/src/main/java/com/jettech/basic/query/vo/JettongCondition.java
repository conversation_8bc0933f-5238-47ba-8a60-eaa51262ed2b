package com.jettech.basic.query.vo;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.sql.Condition;
import cn.hutool.db.sql.ConditionBuilder;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * 扩展 hutool的Condition，以自持条件组查询
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.controller.issue.query
 * @className JettongCondition
 * @date 2023/2/15 下午5:45
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class JettongCondition extends Condition {
    /**
     * 时间类型 X天之内
     */
    private static final String OPERATOR_BEFORE = "BEFORE";
    private static final String OPERATOR_BETWEEN = "BETWEEN";

    /**
     * 条件组
     */
    @ApiModelProperty(value = "条件组")
    private JettongCondition[] conditions;


    public JettongCondition() {
    }

    public JettongCondition(JettongCondition... conditions) {
        this.conditions = conditions;
    }

    public JettongCondition(String field, String operator, Object value) {
        super(field, operator, value);
    }

    public JettongCondition(String field, Object value) {
        super(field, value);
    }

    public static JettongCondition parse(Condition condition) {
        if (condition instanceof JettongCondition) {
            return (JettongCondition) condition;
        }
        JettongCondition jettongCondition = new JettongCondition();
        jettongCondition.setField(condition.getField());
        jettongCondition.setOperator(condition.getOperator());
        jettongCondition.setValue(condition.getValue());
        jettongCondition.setSecondValue(condition.getSecondValue());
        jettongCondition.setField(condition.getField());
        jettongCondition.setLinkOperator(condition.getLinkOperator());
        jettongCondition.setPlaceHolder(condition.isPlaceHolder());
        return jettongCondition;
    }

    public JettongCondition[] getConditions() {
        return conditions;
    }

    @Override
    public void setValue(Object value) {
        // hutool源码 value为空时，会自动将条件转为为 IS NULL 的查询条件
        if (ObjectUtil.isEmpty(value)) {
            setField(null);
            super.setValue(null);
            return;
        }

        // 处理 X天之内
        if (isOperatorBefore()) {
            Long days = Convert.toLong(value);
            LocalDate now = LocalDate.now();
            LocalDate startDay = now.minusDays(days);
            LocalDate endDay = now.plusDays(1L);
            this.setOperator(OPERATOR_BETWEEN);
            super.setValue(startDay);
            // 增加一天时间，能耐匹配今天的数据
            super.setSecondValue(endDay);
            return;
        }

        // 处理 BETWEEN 时 value为集合的结果
        if (isOperatorBetween()) {
            if (value instanceof Collection || ArrayUtil.isArray(value)) {
                List<?> list = Convert.toList(value);
                // 防止后面取值时越界
                list.add(null);
                list.add(null);
                super.setValue(list.get(0));
                super.setSecondValue(list.get(1));
                return;
            }
        }

        super.setValue(value);

    }

    /**
     * 包含 LIKE 和 NOT LIKE
     * @return 是否为字符串模糊匹配
     */
    public boolean isOperatorLike() {
        return "LIKE".equalsIgnoreCase(this.getOperator())
                || "NOT LIKE".equalsIgnoreCase(this.getOperator());
    }

    @Override
    public boolean isOperatorIn() {
        return super.isOperatorIn() || "NOT IN".equalsIgnoreCase(this.getOperator());
    }

    public boolean isOperatorBefore() {
        return OPERATOR_BEFORE.equalsIgnoreCase(this.getOperator());
    }

    public void setConditions(JettongCondition[] conditions) {
        this.conditions = conditions;
    }

    /**
     * 追加条件
     *
     * @param conditions 条件列表
     */
    public void addConditions(JettongCondition... conditions) {
        if (null == this.conditions) {
            this.conditions = conditions;
        } else {
            this.conditions = ArrayUtil.addAll(this.conditions, conditions);
        }
    }

    /**
     * 将条件组转换为条件字符串，使用括号包裹，并回填占位符对应的参数值
     *
     * @param paramValues 参数列表，用于回填占位符对应参数值
     * @return 条件字符串
     */
    @Override
    public String toString(List<Object> paramValues) {
        if (ArrayUtil.isEmpty(conditions)) {
            return super.toString(paramValues);
        }

        final StringBuilder conditionStrBuilder = StrUtil.builder();
        conditionStrBuilder.append("(");
        // 将组内的条件构造为SQL，因为toString，会进行递归，处理所有的条件组
        conditionStrBuilder.append(ConditionBuilder.of(this.conditions).build(paramValues));
        conditionStrBuilder.append(")");

        return conditionStrBuilder.toString();
    }
}

