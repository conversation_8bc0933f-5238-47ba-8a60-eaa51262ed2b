package com.jettech.basic.query.model;

import cn.hutool.db.sql.SqlBuilder;
import lombok.Builder;
import lombok.Data;

/**
 * 可定制化查询表的字段信息
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @projectName jettong-tm
 * @package com.jettech.jettong.base.query.model
 * @className JettongTableField
 * @date 2023/3/13 下午7:08
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class JettongTableField {

    /**
     * 对应表名
     */
    private String tableName;
    /**
     * 字段名
     */
    private String column;
    /**
     * 属性名
     */
    private String property;
    /**
     * 描述
     */
    private String description;

    /**
     * 前端使用类型，输入框、文本域（？）、日期、时间、整形、小数、布尔、（单选）调用后端接口查询、（多选）调用后端接口查询
     */
    private FrontType frontType;

    /**
     * 前端预留的配置，json
     * 存放样式、下拉值等数据
     */
    private String config;

    /**
     * 默认筛选条件，json
     */
    private String defaultCondition;

    /**
     * 当前字段筛选时依赖的字段
     */
    private String dependentField;

    /**
     * 字段对应表的关联类型。0-无关联；1-一对多；2-多对多
     */
    private Integer joinType;

    // 关联表的字段
    /**
     * 字段关联的表
     */
    private String selectTableName;

    /**
     * 查询数据库的显示到前端的下拉的key属性
     */
    private String selectKey;

    /**
     * 查询数据库的显示到前端下拉的value属性，可拼接属性
     * eg：工作项为 code+name，<code>concat(code, ' ', name)</code>
     */
    private String selectValue;

    // 多对多使用的字段
    /**
     * 关联表表名
     */
    private String joinTable;

    /**
     * 关联表别名，默认使用
     */
    private String joinAlias;

    /**
     * 表关联方式
     */
    private SqlBuilder.Join join;

    /**
     * 联表查询对应的on条件，须携带表的别名
     */
    private String on;

    /**
     * 空参构造，序列化使用
     */
    public JettongTableField() {
    }

    /**
     * 基础数据的构造方法，用于解析默认配置
     */
    public JettongTableField(String tableName, String column, String property, String description, FrontType frontType) {
        this.tableName = tableName;
        this.column = column;
        this.property = property;
        this.description = description;
        this.frontType = frontType;
    }

    /**
     * 全参构造
     */
    @Builder
    public JettongTableField(String tableName, String column, String property, String description, FrontType frontType,
                             String config, String defaultCondition, String dependentField, Integer joinType, String selectTableName,
                             String selectKey, String selectValue, String joinTable, String joinAlias, SqlBuilder.Join join, String on) {
        this.tableName = tableName;
        this.column = column;
        this.property = property;
        this.description = description;
        this.frontType = frontType;
        this.config = config;
        this.defaultCondition = defaultCondition;
        this.dependentField = dependentField;
        this.joinType = joinType;
        this.selectTableName = selectTableName;
        this.selectKey = selectKey;
        this.selectValue = selectValue;
        this.joinTable = joinTable;
        this.joinAlias = joinAlias;
        this.join = join;
        this.on = on;
    }
}
