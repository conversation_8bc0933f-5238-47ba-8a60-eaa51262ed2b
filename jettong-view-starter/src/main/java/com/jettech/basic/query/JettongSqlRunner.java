package com.jettech.basic.query;

import cn.hutool.db.Entity;
import cn.hutool.db.Page;
import cn.hutool.db.PageResult;
import cn.hutool.db.SqlConnRunner;
import cn.hutool.db.dialect.DialectFactory;
import cn.hutool.db.handler.BeanListHandler;
import cn.hutool.db.sql.SqlBuilder;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.exception.code.ExceptionCode;
import com.jettech.basic.utils.BeanPlusUtil;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.function.Function;

/**
 * 自定义SQL执行器
 * 基于hutool的 SqlConnRunner 扩展
 *
 * <AUTHOR>
 * @version 1.0
 * @description 自定义SQL执行器
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.controller.issue.query
 * @className JettongSqlRunner
 * @date 2023/2/16 下午5:08
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class JettongSqlRunner extends SqlConnRunner {

    private final SqlSessionFactory sqlSessionFactory;

    public JettongSqlRunner(DataSource dataSource, SqlSessionFactory sqlSessionFactory) {
        super(DialectFactory.getDialect(dataSource));
        this.sqlSessionFactory = sqlSessionFactory;
    }

    private SqlSession getSqlSession() {
        return sqlSessionFactory.openSession(true);
    }

    public <E> List<E> findList(SqlBuilder sqlBuilder, Class<E> entity) {
        return execute(conn -> {
            try {
                return page(conn, sqlBuilder, null, new BeanListHandler<>(entity));
            } catch (SQLException e) {
                throw BizException.wrap(-4, "SQL执行异常：{}", sqlBuilder);
            }
        });
    }

    public <E> E findOne(SqlBuilder sqlBuilder, Class<E> entity) {
        List<E> execute = execute(conn -> {
            try {
                return this.page(conn, sqlBuilder, null, new BeanListHandler<>(entity));
            } catch (SQLException e) {
                throw BizException.wrap(-4, "SQL执行异常：{}", sqlBuilder);
            }
        });
        if (execute == null || execute.isEmpty()) {
            return null;
        }
        return execute.get(0);

    }

    public <E> IPage<E> page(SqlBuilder sqlBuilder, Page page, Class<E> entity) {
        PageResult<Entity> pageResult = execute(conn -> {
            try {
                return page(conn, sqlBuilder, page);
            } catch (SQLException e) {
                throw BizException.wrap(-4, "SQL执行异常：{}", sqlBuilder);
            }
        });
        return convert(pageResult, entity);
    }

    /**
     * 数据库连接方法，执行完毕后会关闭连接
     *
     * @param function
     * @param <E>
     * @return
     */
    public <E> E execute(Function<Connection, E> function) {
        try (SqlSession sqlSession = getSqlSession();
             Connection connection = sqlSession.getConnection();
        ) {
            return function.apply(connection);
        } catch (SQLException e) {
            throw BizException.wrap(ExceptionCode.SQL_EX, e);
        }
    }

    private static <E> IPage<E> convert(PageResult<Entity> pageResult, Class<E> clazz) {
        int total = pageResult.getTotal();
        int page = pageResult.getPage();
        int pageSize = pageResult.getPageSize();
        PageDTO<E> pageDTO = new PageDTO<>(page, pageSize, total);

        List<E> list = BeanPlusUtil.toBeanList(pageResult, clazz);

        pageDTO.setRecords(list);
        return pageDTO;
    }

}
