package com.jettech.basic.query.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.db.sql.SqlBuilder;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.jettech.basic.query.JettongSqlRunner;
import com.jettech.basic.query.model.FrontType;
import com.jettech.basic.query.model.JettongTableField;
import com.jettech.basic.query.model.JettongTableInfo;
import com.jettech.basic.query.service.JettongTableCache;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 实体类筛选字段信息 本地JVM缓存实现类
 * 可被继承，扩展redis缓存
 *
 * <AUTHOR>
 * @version 1.0
 * @description 实体类筛选字段信息
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.controller.issue.query
 * @className JettongTableConfig
 * @date 2023/3/10 下午3:03
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class JettongTableCacheImpl implements JettongTableCache {
    private static final Logger logger = LoggerFactory.getLogger(JettongTableCacheImpl.class);
    protected final Map<String, JettongTableInfo> tableCache = new ConcurrentHashMap<>();

    protected final JettongSqlRunner sqlRunner;

    public JettongTableCacheImpl(JettongSqlRunner sqlRunner) {
        this.sqlRunner = sqlRunner;
        buildDefaultConfigCache();
        buildDatabaseConfigCache();
    }

    @Override
    public JettongTableInfo getJettongTableInfo(String tableName) {
        return tableCache.get(tableName);
    }

    @Override
    public void flashDBConfig() {
        buildDatabaseConfigCache();
    }

    @Override
    public Map<String, JettongTableInfo> tableInfoMap() {
        return new HashMap<>(tableCache);
    }


    /**
     * 读取数据中配置数据，放入table信息中
     * 刷新配置数据
     */
    protected void buildDatabaseConfigCache() {
        SqlBuilder sqlBuilder = SqlBuilder.create().select().from(JETTONG_CONF_QUERY_TABLE_NAME);
        List<JettongTableField> configList = null;
        try {
            configList = sqlRunner.findList(sqlBuilder, JettongTableField.class);
        } catch (Exception e) {
            logger.error("read");
            return;
        }

        Map<String, List<JettongTableField>> configTableMap = configList.stream()
                .collect(Collectors.groupingBy(JettongTableField::getTableName));

        tableCache.forEach((tableName, tableInfo) -> {
            List<JettongTableField> annotatedFields = tableInfo.getFields();
            Map<String, JettongTableField> annotatedFieldMap = annotatedFields.stream()
                    .collect(Collectors.toMap(JettongTableField::getProperty, Function.identity()));

            List<JettongTableField> databaseFields = configTableMap.get(tableName);
            if (CollUtil.isEmpty(databaseFields)) {
                return;
            }
            Map<String, JettongTableField> databaseFieldMap = databaseFields.stream()
                    .collect(Collectors.toMap(JettongTableField::getProperty, Function.identity()));

            // 用数据库中的字段配置替换默认加载的配置
            annotatedFieldMap.putAll(databaseFieldMap);
            tableInfo.setFields(new ArrayList<>(annotatedFieldMap.values()));

        });
    }

    /**
     * 根据获取配置JettongTableInfo信息（）
     * 初始化时配置，如更改了表信息，需重启服务
     */
    protected void buildDefaultConfigCache() {
        // todo 其他项目的Entity是否能读取
        List<TableInfo> tableInfoList = TableInfoHelper.getTableInfos();

        tableInfoList.stream()
                .map(this::buildTable)
                .forEach(table -> {
                    tableCache.put(table.getTableName(), table);
                });
    }

    protected JettongTableInfo buildTable(TableInfo tableInfo) {
        Class<?> entityType = tableInfo.getEntityType();
        String desc = getDesc(entityType);
        String tableName = tableInfo.getTableName();

        List<TableFieldInfo> fieldList = tableInfo.getFieldList();
        List<JettongTableField> jettongTableFieldList = fieldList.stream()
                .map(fieldInfo -> buildField(tableName, fieldInfo))
                .collect(Collectors.toList());

        // 主键字段
        if (tableInfo.havePK()) {
            JettongTableField keyField = new JettongTableField(
                    tableName,
                    tableInfo.getKeyColumn(),
                    tableInfo.getKeyProperty(),
                    "主键ID",
                    FrontType.match(tableInfo.getKeyType())
            );
            jettongTableFieldList.add(keyField);
        }

        return new JettongTableInfo(entityType, tableName, desc, jettongTableFieldList);

    }

    protected JettongTableField buildField(String tableName, TableFieldInfo fieldInfo) {

        return new JettongTableField(
                tableName,
                fieldInfo.getColumn(),
                fieldInfo.getProperty(),
                getDesc(fieldInfo.getField()),
                FrontType.match(fieldInfo.getField())
        );
    }

    protected String getDesc(Class<?> clazz) {
        ApiModel apiModel = clazz.getAnnotation(ApiModel.class);
        return Optional.ofNullable(apiModel)
                .map(ApiModel::description)
                .orElse(clazz.getSimpleName());
    }

    protected String getDesc(Field field) {
        ApiModelProperty apiModel = field.getAnnotation(ApiModelProperty.class);
        return Optional.ofNullable(apiModel)
                .map(ApiModelProperty::value)
                .orElse(field.getName());
    }

}
