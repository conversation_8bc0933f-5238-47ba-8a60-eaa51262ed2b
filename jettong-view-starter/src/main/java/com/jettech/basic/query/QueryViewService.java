package com.jettech.basic.query;

import cn.hutool.db.sql.SqlBuilder;
import com.jettech.basic.query.vo.JettongSelectVO;

import java.util.Collection;

/**
 * 回显前段时根据类型补充 自定义的数据，
 * 对应服务自己实现，不同微服务可以实现不同的解析方式
 * 建议：继承 AbsEntityViewService<E> 并将该子类注入到spring容器中
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @projectName jettong-tm
 * @package com.jettech.jettong.base.query
 * @className EntityViewService
 * @date 2023/3/17 上午9:52
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface QueryViewService {

    /**
     * 当前 service 处理的实体类
     * @return  处理的实体类
     */
    Class<?> handleEntity();

    /**
     * 自定义处理 筛选条件
     * @param selectVO  筛选条件
     */
    JettongSelectVO handleCondition(JettongSelectVO selectVO);

    /**
     * 自定义处理 SQL
     * @param sqlBuilder hutool的SQL构建器
     */
    SqlBuilder handleSql(SqlBuilder sqlBuilder);

    /**
     * 自定义补充 实体类显示到前段的数据
     *
     */
    void view(Collection<?> collection);

}
