package com.jettech.basic.query.model;

import com.jettech.basic.base.BaseEnum;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 输入框、文本域（？）、日期、时间、整形、小数、布尔、（单选）调用后端接口查询、（多选）调用后端接口查询
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.controller.issue.query
 * @className FrontType
 * @date 2023/3/13 下午6:05
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public enum FrontType implements BaseEnum {
    INPUT(String.class, Character.class),
    DATE(Date.class, LocalDate.class, LocalDateTime.class, LocalTime.class),
    TIME(),
    NUMBER(Integer.class, Long.class, Short.class, Byte.class, Float.class, Double.class),
    ICON(Boolean.class),
    USER(),
    SELECT(),

    // --------为前端可能使用------
    EDITOR(),
    FILE(),
    INT(),
    LINKED(),
    QUOTE(),
    TEXTAREA(),
    PROJECTUSER(),
    ;

    private final Set<Class<?>> classes;

    FrontType(Class<?>... classes) {
        if (classes == null) {
            this.classes = new HashSet<>();
        } else {
            this.classes = Arrays.stream(classes).collect(Collectors.toSet());
        }
    }

    public static FrontType match(Class<?> clazz) {
        return Arrays.stream(FrontType.values())
                .filter(type -> type.classes.contains(clazz))
                .findFirst()
                .orElse(INPUT);
    }

    public static FrontType match(Field field) {
        Class<?> clazz = field.getType();
        return match(clazz);
    }

    @Override
    public String getDesc() {
        return this.getCode();
    }
}
