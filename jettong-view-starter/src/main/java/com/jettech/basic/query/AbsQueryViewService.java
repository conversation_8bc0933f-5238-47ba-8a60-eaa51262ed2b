package com.jettech.basic.query;

import cn.hutool.core.collection.CollUtil;

import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;

/**
 * 回显前段时根据类型补充
 * 对 QueryViewService 接口进行实体类判断
 * 继承 AbsEntityViewService<E> 并将该子类注入到spring容器中
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @projectName jettong-tm
 * @package com.jettech.jettong.base.query
 * @className AbsEntityViewService
 * @date 2023/3/17 上午9:57
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public abstract class AbsQueryViewService<E> implements QueryViewService {

    @Override
    public abstract Class<E> handleEntity();

    @Override
    public void view(Collection<?> collection) {
        if (CollUtil.isEmpty(collection)) {
            return;
        }

        // noinspection unchecked
        List<E> dataList = (List<E>) collection;
        handle(dataList);
    }

    /**
     * 补充实体类需要返回给前段的数据
     *
     * @param dataList  待补充的实体类
     */
    protected abstract void handle(@NotNull Collection<E> dataList);

}
