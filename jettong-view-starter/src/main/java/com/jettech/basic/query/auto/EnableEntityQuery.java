package com.jettech.basic.query.auto;

import org.springframework.context.annotation.Import;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 启用实体类动态查询工具
 *
 * <AUTHOR>
 * @version 1.0
 * @description 启用实体类动态查询工具
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.controller.issue.query
 * @className JettongSqlRunner
 * @date 2023/2/16 下午5:08
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Import(EntityQueryAutoConfig.class)
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface EnableEntityQuery {
}
