package com.jettech.basic.query.vo;

import cn.hutool.db.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 工作项自定义查询
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作项自定义查询
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.controller.issue.query
 * @className JettongSelect
 * @date 2023/2/15 下午5:52
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ApiModel(value = "JettongSelectVO", description = "工作项自定义查询")
public class JettongSelectVO {

    @NotNull
    @ApiModelProperty("表名")
    private String table;

    /**
     * 是否启用关联数据回显
     */
    @ApiModelProperty("是否启用关联数据回显")
    private boolean enableEcho;

    /**
     * 待查询的字段，为空是查询全部“*”
     */
    @ApiModelProperty("待查询的字段")
    private String[] fields;

    /**
     * 联表
     */
    @ApiModelProperty("联表")
    private JettongJoinTable[] joinTables;

    /**
     * 筛选条件
     */
    @ApiModelProperty("筛选条件")
    private JettongCondition[] where;

    /**
     * 分页信息
     */
    @ApiModelProperty("分页信息")
    private Page page;

}
