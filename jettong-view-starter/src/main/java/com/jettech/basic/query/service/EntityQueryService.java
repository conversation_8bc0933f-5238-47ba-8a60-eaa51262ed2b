package com.jettech.basic.query.service;

import cn.hutool.db.sql.SqlBuilder;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.query.vo.JettongSelectVO;

import java.util.Collection;

/**
 * 自定一筛选器查询服务
 *
 * <AUTHOR>
 * @version 1.0
 * @description 自定一筛选器查询服务
 * @projectName jettong-tm
 * @package com.jettech.jettong.base.query
 * @className JettongTableService
 * @date 2023/3/14 下午4:17
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface EntityQueryService {

    /**
     * 查询全部数据
     *
     * @param selectVO  查询条件
     * @return          全部数据
     */
    Collection<?> query(JettongSelectVO selectVO);

    /**
     * 查询分页数据
     *
     * @param selectVO  查询条件
     * @return          分页数据
     */
    IPage<?> queryPage(JettongSelectVO selectVO);

    /**
     * 构建查询的SQL
     *
     * @param selectVO  查询条件
     * @param table     待查询的表
     * @return          SQL构建器
     */
    SqlBuilder createSqlBuilder(JettongSelectVO selectVO, String table);
}
