package com.jettech.basic.query.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.base.R;
import com.jettech.basic.query.model.JettongTableInfo;
import com.jettech.basic.query.service.EntityQueryService;
import com.jettech.basic.query.service.JettongTableCache;
import com.jettech.basic.query.vo.JettongSelectVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;

/**
 * 实体类动态查询 控制类
 *
 * <AUTHOR>
 * @version 1.0
 * @projectname jettong-util
 * 2023/3/22 11:33
 */
@RestController
@Api(value = "QueryViewController", tags = "筛选器查询类")
@RequestMapping("/entity/query")
@RequiredArgsConstructor
public class EntityQueryController {
    private final JettongTableCache tableCache;
    private final EntityQueryService queryService;

    @ApiOperation("查询分页数据")
    @PostMapping("/page")
    public R<IPage<?>> page(@RequestBody JettongSelectVO selectVO) {
        IPage<?> page = queryService.queryPage(selectVO);
        return R.success(page);
    }

    @ApiOperation("查询全部数据")
    @PostMapping("/collection")
    public R<Collection<?>> list(@RequestBody JettongSelectVO selectVO) {
        Collection<?> query = queryService.query(selectVO);
        return R.success(query);
    }

    @ApiOperation("对应表查询可用字段")
    @GetMapping("/{table}")
    public R<JettongTableInfo> getTableInfo(@PathVariable String table) {
        JettongTableInfo jettongTableInfo = tableCache.getJettongTableInfo(table);
        return R.success(jettongTableInfo);
    }

//    @GetMapping()
    public R<Collection<JettongTableInfo>> getTableInfoList() {
        Collection<JettongTableInfo> values = tableCache.tableInfoMap().values();
        return R.success(values);
    }

    @ApiOperation("刷新数据库中表字段配置")
    @PutMapping
    public R<Boolean> flash() {
        tableCache.flashDBConfig();
        return R.success();
    }
}
