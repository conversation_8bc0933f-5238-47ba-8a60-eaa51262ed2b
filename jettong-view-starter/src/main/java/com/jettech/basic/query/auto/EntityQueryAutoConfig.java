package com.jettech.basic.query.auto;

import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.query.QueryViewService;
import com.jettech.basic.query.JettongSqlRunner;
import com.jettech.basic.query.service.EntityQueryService;
import com.jettech.basic.query.service.JettongTableCache;
import com.jettech.basic.query.service.impl.EntityQueryServiceImpl;
import com.jettech.basic.query.service.impl.JettongTableCacheImpl;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.util.List;

/**
 * 自动配置类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 自动配置类
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.controller.issue.query
 * @className JettongSqlRunner
 * @date 2023/2/16 下午5:08
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Configuration
public class EntityQueryAutoConfig {

    @Bean
    public JettongSqlRunner jettongSqlRunner(DataSource dataSource, SqlSessionFactory sqlSessionFactory) {
        return new JettongSqlRunner(dataSource, sqlSessionFactory);
    }

    @Bean
    @ConditionalOnMissingBean
    public JettongTableCache jettongTableCache(JettongSqlRunner sqlRunner) {
        return new JettongTableCacheImpl(sqlRunner);
    }

    @Bean
    @ConditionalOnMissingBean
    public EntityQueryService entityQueryService(List<QueryViewService> viewServiceList, EchoService echoService,
                                                 JettongSqlRunner sqlRunner, JettongTableCache tableCache) {
        return new EntityQueryServiceImpl(viewServiceList, echoService, sqlRunner, tableCache);
    }

}
