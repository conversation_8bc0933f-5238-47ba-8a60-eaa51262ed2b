package com.jettech.basic.query.service;

import com.jettech.basic.query.model.JettongTableInfo;

import java.util.Map;

/**
 * 实体类筛选字段信息缓存接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @projectName jettong-tm
 * @package com.jettech.jettong.base.query
 * @className JettongTableService
 * @date 2023/3/14 下午4:17
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface JettongTableCache {
    /**
     * 默认字段配置的表名
     */
    String JETTONG_CONF_QUERY_TABLE_NAME = "jettong_conf_query_field";

    /**
     * 获取表字段信息
     *
     * @param tableName 表名
     * @return          表字段信息
     */
    JettongTableInfo getJettongTableInfo(String tableName);

    void flashDBConfig();

    /**
     * 获取全部的表配置
     * 对此MAP的修改，不影响缓存的数据
     * @return  全部的表配置
     */
    Map<String, JettongTableInfo> tableInfoMap();
}
