package com.jettech.basic.query.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

/**
 * 可定制化查询的表信息
 *
 * <AUTHOR>
 * @version 1.0
 * @description 可定制化查询的表信息
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.controller.issue.query
 * @className SelectTableInfo
 * @date 2023/3/10 上午9:34
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class JettongTableInfo {

    @JsonIgnore
    private Class<?> clazz;

    /**
     * 表名称
     */
    private String tableName;

    /**
     * 表描述
     */
    private String description;

    private List<JettongTableField> fields;

    public JettongTableInfo(Class<?> clazz, String tableName, String description,
                            List<JettongTableField> fields) {
        this.clazz = clazz;
//        this.mapper = mapper;
        this.tableName = tableName;
        this.description = description;
        this.fields = fields;
    }

}
