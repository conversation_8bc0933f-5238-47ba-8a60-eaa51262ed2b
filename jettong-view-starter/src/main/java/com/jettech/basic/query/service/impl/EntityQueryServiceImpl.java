package com.jettech.basic.query.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.sql.SqlBuilder;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.query.QueryViewService;
import com.jettech.basic.query.JettongSqlRunner;
import com.jettech.basic.query.model.JettongTableInfo;
import com.jettech.basic.query.service.EntityQueryService;
import com.jettech.basic.query.service.JettongTableCache;
import com.jettech.basic.query.vo.JettongCondition;
import com.jettech.basic.query.vo.JettongJoinTable;
import com.jettech.basic.query.vo.JettongSelectVO;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.basic.utils.StringUtil;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 自定一筛选器查询服务
 *
 * <AUTHOR>
 * @version 1.0
 * @description 自定一筛选器查询服务
 * @projectName jettong-tm
 * @package com.jettech.jettong.base.query
 * @className QueryService
 * @date 2023/3/17 上午9:35
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RequiredArgsConstructor
public class EntityQueryServiceImpl implements EntityQueryService {

    private final List<QueryViewService> viewServiceList;
    private final EchoService echoService;
    private final JettongSqlRunner sqlRunner;
    private final JettongTableCache tableCache;

    /**
     * 查询全部数据
     *
     * @param selectVO
     * @return
     */
    @Override
    public Collection<?> query(JettongSelectVO selectVO) {
        ArgumentAssert.notNull(selectVO.getTable(), "必须填写表名");

        JettongTableInfo jettongTableInfo = getJettongTableInfo(selectVO.getTable());

        Class<?> clazz = jettongTableInfo.getClazz();
        String tableName = jettongTableInfo.getTableName();
        Optional<QueryViewService> viewService = filterEntityViewService(clazz);

        if (viewService.isPresent()) {
            selectVO = viewService.get().handleCondition(selectVO);
        }
        SqlBuilder sqlBuilder = createSqlBuilder(selectVO, tableName);

        if (viewService.isPresent()) {
            sqlBuilder = viewService.get().handleSql(sqlBuilder);
        }

        // 查询数据
        List<?> list = sqlRunner.findList(sqlBuilder, clazz);

        if (selectVO.isEnableEcho()) {
            viewService.ifPresent(service -> service.view(list));
            echoService.action(list);
        }

        return list;
    }

    /**
     * 根据实体类查询对应的 查询服务，做自定义处理
     * @param clazz
     * @return
     */
    private Optional<QueryViewService> filterEntityViewService(Class<?> clazz) {
        return viewServiceList.stream()
                .filter(s -> s.handleEntity().equals(clazz))
                .findFirst();
    }

    /**
     * 查询分页数据
     *
     * @param selectVO  查询条件
     * @return          分页数据
     */
    @Override
    public IPage<?> queryPage(JettongSelectVO selectVO) {

        ArgumentAssert.notNull(selectVO.getTable(), "必须填写表名");
        ArgumentAssert.notNull(selectVO.getPage(), "分页查询时必须填写分页信息");

        JettongTableInfo jettongTableInfo = getJettongTableInfo(selectVO.getTable());

        Class<?> clazz = jettongTableInfo.getClazz();
        String tableName = jettongTableInfo.getTableName();
        Optional<QueryViewService> viewService = filterEntityViewService(clazz);

        if (viewService.isPresent()) {
            selectVO = viewService.get().handleCondition(selectVO);
        }
        SqlBuilder sqlBuilder = createSqlBuilder(selectVO, tableName);

        if (viewService.isPresent()) {
            sqlBuilder = viewService.get().handleSql(sqlBuilder);
        }

        // 查询分页数据
        IPage<?> page = sqlRunner.page(sqlBuilder, selectVO.getPage(), clazz);

        if (selectVO.isEnableEcho()) {
            viewService.ifPresent(service -> service.view(page.getRecords()));
            echoService.action(page);
        }
        return page;
    }

    private JettongTableInfo getJettongTableInfo(String table) {

        JettongTableInfo jettongTableInfo = tableCache.getJettongTableInfo(table);

        ArgumentAssert.notNull(jettongTableInfo, "当前表不支持自定义查询");
        return jettongTableInfo;
    }

    /**
     * 构建查询的SQL
     *
     * @param selectVO  查询条件
     * @param table     待查询的表
     * @return          SQL构建器
     */
    @Override
    public SqlBuilder createSqlBuilder(JettongSelectVO selectVO, String table) {
        // 注意保证 SqlBuilder 的前后顺序，要按照SQL的顺序进行拼接
        SqlBuilder sqlBuilder = SqlBuilder.create();

        // 处理查询的字段
        String[] fields = selectVO.getFields();
        if (ArrayUtil.isEmpty(fields)) {
            sqlBuilder.select(table + ".*");
        } else {
            String[] strings = Arrays.stream(fields)
                    .map(field -> appendTable(table, field))
                    .toArray(String[]::new);
            sqlBuilder.select(strings);
        }

        sqlBuilder.from(table);

        JettongJoinTable[] joinTables = selectVO.getJoinTables();

        if (ArrayUtil.isNotEmpty(joinTables)) {
            for (JettongJoinTable joinTable : joinTables) {
                String tableStr = String.format("%s AS %S", joinTable.getJoinTable(), joinTable.getJoinAlias());
                sqlBuilder.join(tableStr, joinTable.getJoin()).on(joinTable.getOn());
            }
        }

        JettongCondition[] conditions = cleanNullWhere(selectVO.getWhere());

        // 处理条件语句中的字段和value
        makeFieldWhere(table, conditions);

        sqlBuilder.where(conditions);
        return sqlBuilder;
    }

    /**
     * 清理多余的筛选条件
     */
    private static JettongCondition[] cleanNullWhere(JettongCondition... conditions) {
        if (ArrayUtil.isEmpty(conditions)) {
            return conditions;
        }

        for (JettongCondition jettongCondition : conditions) {
            JettongCondition[] children = jettongCondition.getConditions();
            children = cleanNullWhere(children);
            jettongCondition.setConditions(children);
        }
        return Arrays.stream(conditions)
                .filter(child -> child.getField() != null || ArrayUtil.isNotEmpty(child.getConditions()))
                .toArray(JettongCondition[]::new);
    }

    private static void makeFieldWhere(String table, JettongCondition[] conditions) {
        for (JettongCondition condition : conditions) {
            String field = condition.getField();

            // 字段存在，且为主表的字段 拼接主表的表名
            if (StringUtil.isNotBlank(field)) {
                condition.setField(appendTable(table, field));
            }

            // like的时候，value值前后拼接%
            if (condition.isOperatorLike()) {
                Optional.ofNullable(condition.getValue())
                        .map(Object::toString)
                        .map(str -> "%" + str + "%")
                        .ifPresent(condition::setValue);
            }

            // 递归将所有分组下的字段全部修改
            if (ArrayUtil.isNotEmpty(condition.getConditions())) {
                makeFieldWhere(table, condition.getConditions());
            }
        }
    }

    private static String appendTable(String table, String field) {
        // 驼峰转下划线
        String underline = StrUtil.toUnderlineCase(field);
        if (underline.contains(".")) {
            return underline;
        } else {
            return String.format("%s.%s", table, underline);
        }
    }

}
