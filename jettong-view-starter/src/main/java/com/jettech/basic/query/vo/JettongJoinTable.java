package com.jettech.basic.query.vo;

import cn.hutool.db.sql.SqlBuilder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 联表查询关联表信息
 *
 * <AUTHOR>
 * @version 1.0
 * @description 联表查询关联表信息
 * @projectName jettong-tm
 * @package com.jettech.jettong.base.query
 * @className JettongJoinTable
 * @date 2023/3/17 上午9:26
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class JettongJoinTable {

    /**
     * 关联的表
     */
    @ApiModelProperty("关联的表")
    private String joinTable;

    /**
     * 关联表的别名
     */
    @ApiModelProperty("关联表的别名")
    private String joinAlias;

    /**
     * 外连接方式
     */
    @ApiModelProperty("外连接方式")
    private SqlBuilder.Join join = SqlBuilder.Join.LEFT;

    /**
     * 联表查询对应的on条件，须携带表的别名
     */
    @ApiModelProperty("联表查询对应的on条件")
    private String on;

}
