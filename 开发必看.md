## 如何导入本项目
1. 将 .mvn/settings.xml 文件中 mirror 和 profile 下配置的 repo 地址修改为可用的仓库地址
2. 将 jettong-util/pom.xml 导入IDEA
3. 编译jettong-util/pom.xml： mvn clean install -Dmaven.javadoc.skip=true -Dgpg.skip=true -Dmaven.source.skip=true
   -DskipTests=true

## 如何编译 jettong-util 

```
# 跳过 生成javadoc
mvn clean install -Dmaven.javadoc.skip=true -Dgpg.skip=true
# 跳过 生成源代码
mvn clean install -Dmaven.source.skip=true
# 跳过 发布jar到中央仓库
clean install -Dgpg.skip -f pom.xml

# 同时跳过 生成javadoc、生成源代码、发布jar到中央仓库、单元测试， 只编译源码到本地仓库
mvn clean install  -Dmaven.javadoc.skip=true -Dgpg.skip=true -Dmaven.source.skip=true -DskipTests=true -f pom.xml

# 编译 同时生成源代码和javadoc和发布  （默认情况大家都会报错）
mvn clean install
```

## 如何解决 IDEA 控制台生成javadoc时乱码

```
# mac
IntelliJ IDEA -> Preferences  -> Build, Execution, Deployment -> Build Tools ->  Maven -> Runner 
在 Environment variables: 加入  JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8

# window
File -> Settings -> Build, Execution, Deployment-> Build Tools ->  Maven -> Runner 
在VM Options： 加入  -Dfile.encoding=GBK

# 还不行就在命令行执行 mvn -version  看看 mvn 的编码是什么，改成一样的即可。
# 改了还不行，就度娘吧
```

## 统一升级版本号

mvn versions:set -DnewVersion=3.5.1 -DskipTests -DgenerateBackupPoms=false

mvn versions:set -DnewVersion=3.5.1-SNAPSHOT -DskipTests -DgenerateBackupPoms=false


## 如何推送到公司仓库
1. 修改根目录 pom.xml 和 jettong-dependencies/pom.xml 下的 distributionManagement 配置repository的url为公司仓库地址,id 为公司仓库id
2. 在系统环境变量中设置 MAVEN_USERNAME 和 MAVEN_PASSWORD 为公司仓库的用户名和密码
3. 执行 mvn clean deploy -DskipTests -Dgpg.skip=true -Dmaven.javadoc.skip=true -Dmaven.source.skip=true