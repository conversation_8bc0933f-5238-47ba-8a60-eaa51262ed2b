<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.jettech.basic</groupId>
    <artifactId>jettong-util</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>
    <description>jettong项目公共模块</description>

    <modules>
        <module>jettong-dependencies</module>
        <module>jettong-annotation</module>
        <module>jettong-util-common</module>
        <module>jettong-boot-base</module>
        <module>jettong-databases</module>
        <module>jettong-log-starter</module>
        <module>jettong-swagger2-starter</module>
        <module>jettong-validator-starter</module>
        <module>jettong-security-starter</module>
        <module>jettong-jwt-starter</module>
        <module>jettong-dozer-starter</module>
        <module>jettong-xss-starter</module>
        <module>jettong-cache-starter</module>
        <module>jettong-echo-starter</module>
        <module>jettong-cloud-starter</module>
        <module>jettong-salt-starter</module>
        <module>jettong-mq-starter</module>
        <module>jettong-ftp-starter</module>
        <module>jettong-all</module>
        <module>jettong-view-starter</module>
        <module>jettong-job-starter</module>
        <module>jettong-file-import-starter</module>
    </modules>
    <properties>
        <jettong-dependencies.version>1.0.0</jettong-dependencies.version>
        <!-- jdk8 将这里改成 8 -->
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-resources-plugin.version>3.1.0</maven-resources-plugin.version>
        <dockerfile-maven-plugin.version>1.4.12</dockerfile-maven-plugin.version>
        <versions-maven-plugin.version>2.7</versions-maven-plugin.version>
        <spring-cloud-alibaba-dependencies.version>2021.1</spring-cloud-alibaba-dependencies.version>
        <nacos.version>1.4.1</nacos.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-dependencies</artifactId>
                <version>${jettong-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-util-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-annotation</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-salt-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-jenkins-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-ftp-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-boot-base</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-echo-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-databases</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-cache-starter</artifactId>
                <version>${project.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
    <dependencies>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
    </dependencies>


    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <!--                        <compilerArgs>-Xlint:deprecation</compilerArgs>-->
                        <!--                        <compilerArgs>-Xlint:unchecked</compilerArgs>-->
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                    <configuration>
                        <encoding>UTF-8</encoding>
                        <!-- 后缀为pem、pfx的证书文件 -->
                        <nonFilteredFileExtensions>
                            <nonFilteredFileExtension>pem</nonFilteredFileExtension>
                            <nonFilteredFileExtension>pfx</nonFilteredFileExtension>
                            <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                            <nonFilteredFileExtension>key</nonFilteredFileExtension>
                        </nonFilteredFileExtensions>
                    </configuration>
                </plugin>
                <!-- 一键更新子模块版本号 -->
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>${versions-maven-plugin.version}</version>
                    <configuration>
                        <generateBackupPoms>false</generateBackupPoms>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!-- 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <!-- 资源插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
            <!-- 一键更新子模块版本号 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>jettech-release</id>
            <name>Jettech Releases Repository</name>
            <url>http://repo.jettech.com/repository/jettech-release/</url>
        </repository>
        <snapshotRepository>
            <id>jettech-snapshots</id>
            <uniqueVersion>false</uniqueVersion>
            <name>Jettech Snapshots  Repository</name>
            <url>http://repo.jettech.com/repository/jettech-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
